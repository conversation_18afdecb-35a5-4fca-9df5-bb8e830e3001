#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Firebird Database Repair Tool

This tool is designed to repair corrupted Firebird database files that were transferred
in chunks. It analyzes the database file, identifies issues, and attempts to repair them.

Usage:
    python repair_firebird_db.py <path_to_database_file>

Example:
    python repair_firebird_db.py D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups\import_76b3e573\database_20250423_105816.fdb
"""

import os
import sys
import time
import shutil
import logging
import argparse
import subprocess
import traceback
import base64
import zlib
import hashlib
import glob

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('repair_firebird_db.log')
    ]
)
logger = logging.getLogger('repair_firebird_db')

# Firebird signatures to check for in the database header
FIREBIRD_SIGNATURES = [
    b'Firebird', 
    b'INTERBASE', 
    b'GENERAL', 
    b'ODS',
    b'FILE FORMAT', 
    b'Database', 
    b'\x01\x00\x09\x00',  # ODS signature
    b'\x16\x00',  # Firebird/InterBase ODS marker
]

# Minimal valid Firebird database header
MINIMAL_HEADER = (
    b'Firebird/InterBase Database\x00'
    b'Database Format 1.0\x00'
    b'ODS Version 11.0\x00'
    b'\x01\x00\x09\x00'  # ODS signature
    b'\x16\x00'  # Firebird/InterBase ODS marker
)

def check_file_exists(file_path):
    """Check if the file exists and has content"""
    if not os.path.exists(file_path):
        logger.error(f"File does not exist: {file_path}")
        return False
        
    file_size = os.path.getsize(file_path)
    if file_size == 0:
        logger.error(f"File is empty: {file_path}")
        return False
        
    logger.info(f"File exists and has size: {file_size} bytes")
    return True

def create_backup(file_path):
    """Create a backup of the original file"""
    backup_path = f"{file_path}.backup_{int(time.time())}"
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup of original file at: {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"Could not create backup: {e}")
        return None

def check_header(file_path):
    """Check if the file has a valid Firebird database header"""
    try:
        with open(file_path, 'rb') as f:
            header = f.read(4096)  # Read first 4KB
            
            # Check for common Firebird signatures
            for signature in FIREBIRD_SIGNATURES:
                if signature in header:
                    logger.info(f"Found valid Firebird signature: {signature}")
                    return True, signature
                    
            logger.warning("No valid Firebird signatures found in header")
            return False, None
    except Exception as e:
        logger.error(f"Error checking header: {e}")
        return False, None

def find_backup_chunks(file_path):
    """Find any backup chunks or header files"""
    backup_files = []
    
    # Look for various backup files
    patterns = [
        f"{file_path}.first_chunk",
        f"{file_path}.header",
        f"{file_path}.chunk.backup",
        f"{file_path}.*.chunk",
        os.path.join(os.path.dirname(file_path), "*.first_chunk"),
        os.path.join(os.path.dirname(file_path), "*.header")
    ]
    
    for pattern in patterns:
        backup_files.extend(glob.glob(pattern))
    
    if backup_files:
        logger.info(f"Found {len(backup_files)} backup files: {backup_files}")
    else:
        logger.warning("No backup files found")
        
    return backup_files

def restore_header_from_backup(file_path, backup_files):
    """Restore the database header from a backup file"""
    if not backup_files:
        return False
        
    for backup_file in backup_files:
        try:
            logger.info(f"Attempting to restore header from: {backup_file}")
            
            with open(backup_file, 'rb') as src, open(file_path, 'r+b') as dest:
                header_data = src.read(4096)  # Read up to 4KB
                dest.seek(0)
                dest.write(header_data)
                dest.flush()
                os.fsync(dest.fileno())
                
            logger.info(f"Restored header from {backup_file}")
            
            # Check if the header is now valid
            is_valid, _ = check_header(file_path)
            if is_valid:
                logger.info("Header is now valid")
                return True
            else:
                logger.warning("Header is still invalid after restoration")
        except Exception as e:
            logger.error(f"Error restoring header from {backup_file}: {e}")
            
    return False

def create_minimal_header(file_path):
    """Create a minimal valid Firebird database header"""
    try:
        logger.info("Creating minimal valid header")
        
        with open(file_path, 'r+b') as f:
            # Read the existing file to preserve content after header
            f.seek(0)
            existing_data = f.read()
            
            # Write minimal header
            f.seek(0)
            f.write(MINIMAL_HEADER)
            
            # If file is very small, write the existing data after the header
            if len(existing_data) > len(MINIMAL_HEADER):
                f.seek(len(MINIMAL_HEADER))
                f.write(existing_data[len(MINIMAL_HEADER):])
                
            f.flush()
            os.fsync(f.fileno())
            
        logger.info("Created minimal valid header")
        return True
    except Exception as e:
        logger.error(f"Error creating minimal header: {e}")
        return False

def fix_file_permissions(file_path):
    """Fix file permissions"""
    try:
        os.chmod(file_path, 0o644)  # rw-r--r--
        logger.info("Set file permissions to 644")
        return True
    except Exception as e:
        logger.error(f"Error setting file permissions: {e}")
        return False

def validate_with_isql(file_path):
    """Validate the database using isql"""
    try:
        isql_paths = [
            r"C:\Program Files (x86)\Firebird\Firebird_1_5\bin\isql.exe",
            r"C:\Program Files\Firebird\Firebird_1_5\bin\isql.exe",
            r"C:\Program Files (x86)\Firebird\Firebird_2_5\bin\isql.exe",
            r"C:\Program Files\Firebird\Firebird_2_5\bin\isql.exe",
            r"C:\Program Files (x86)\Firebird\Firebird_3_0\bin\isql.exe",
            r"C:\Program Files\Firebird\Firebird_3_0\bin\isql.exe"
        ]
        
        isql_path = None
        for path in isql_paths:
            if os.path.exists(path):
                isql_path = path
                break
                
        if not isql_path:
            logger.error("isql not found")
            return False, "isql not found"
            
        logger.info(f"Using isql at: {isql_path}")
        
        # Create connection string
        connection_string = f"{file_path}"
        
        # Run isql for database validation
        cmd = [isql_path, "-user", "sysdba", "-password", "masterkey", connection_string, "-x"]
        
        # Set timeout to avoid hanging
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        try:
            stdout, stderr = process.communicate(timeout=30)
            
            # Check for errors
            if process.returncode != 0:
                logger.error(f"isql validation failed: {stderr}")
                return False, stderr
                
            logger.info("Database validated successfully with isql")
            return True, stdout
        except subprocess.TimeoutExpired:
            process.kill()
            logger.error("isql validation timed out")
            return False, "Validation timed out"
    except Exception as e:
        logger.error(f"Error validating database: {e}")
        return False, str(e)

def repair_with_gbak(file_path):
    """Repair the database using gbak (backup/restore)"""
    try:
        gbak_paths = [
            r"C:\Program Files (x86)\Firebird\Firebird_1_5\bin\gbak.exe",
            r"C:\Program Files\Firebird\Firebird_1_5\bin\gbak.exe",
            r"C:\Program Files (x86)\Firebird\Firebird_2_5\bin\gbak.exe",
            r"C:\Program Files\Firebird\Firebird_2_5\bin\gbak.exe",
            r"C:\Program Files (x86)\Firebird\Firebird_3_0\bin\gbak.exe",
            r"C:\Program Files\Firebird\Firebird_3_0\bin\gbak.exe"
        ]
        
        gbak_path = None
        for path in gbak_paths:
            if os.path.exists(path):
                gbak_path = path
                break
                
        if not gbak_path:
            logger.error("gbak not found")
            return False
            
        logger.info(f"Using gbak at: {gbak_path}")
        
        # Create temporary files for backup/restore
        temp_backup = f"{file_path}.fbk"
        temp_restored = f"{file_path}.restored.fdb"
        
        # Try to backup the database
        backup_cmd = [gbak_path, "-b", "-user", "sysdba", "-password", "masterkey", file_path, temp_backup]
        try:
            logger.info(f"Running backup command: {' '.join(backup_cmd)}")
            subprocess.run(backup_cmd, check=True, timeout=60)
            logger.info(f"Successfully created backup at: {temp_backup}")
            
            # Try to restore the backup
            restore_cmd = [gbak_path, "-c", "-user", "sysdba", "-password", "masterkey", temp_backup, temp_restored]
            logger.info(f"Running restore command: {' '.join(restore_cmd)}")
            subprocess.run(restore_cmd, check=True, timeout=60)
            logger.info(f"Successfully restored database to: {temp_restored}")
            
            # Replace the original file with the restored one
            shutil.copy2(temp_restored, file_path)
            logger.info(f"Replaced original file with restored database")
            
            # Clean up temporary files
            for temp_file in [temp_backup, temp_restored]:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    
            return True
        except Exception as e:
            logger.error(f"Error using gbak: {e}")
            return False
    except Exception as e:
        logger.error(f"Error using gbak: {e}")
        return False

def repair_database(file_path):
    """Main function to repair the database"""
    logger.info(f"Starting repair of database: {file_path}")
    
    # Check if file exists
    if not check_file_exists(file_path):
        return False
        
    # Create backup
    backup_path = create_backup(file_path)
    
    # Check header
    is_valid_header, signature = check_header(file_path)
    if is_valid_header:
        logger.info("Database header appears to be valid")
        
        # Try to validate with isql
        is_valid_isql, message = validate_with_isql(file_path)
        if is_valid_isql:
            logger.info("Database is valid according to isql")
            return True
        else:
            logger.warning(f"Database header is valid but isql validation failed: {message}")
    else:
        logger.warning("Database header is invalid")
    
    # Find backup chunks
    backup_files = find_backup_chunks(file_path)
    
    # Try to restore header from backup
    if not is_valid_header and backup_files:
        if restore_header_from_backup(file_path, backup_files):
            logger.info("Successfully restored header from backup")
            
            # Check if the database is now valid
            is_valid_isql, message = validate_with_isql(file_path)
            if is_valid_isql:
                logger.info("Database is now valid after header restoration")
                return True
    
    # If header is still invalid, try to create a minimal valid header
    if not is_valid_header:
        if create_minimal_header(file_path):
            logger.info("Created minimal valid header")
            
            # Check if the database is now valid
            is_valid_isql, message = validate_with_isql(file_path)
            if is_valid_isql:
                logger.info("Database is now valid after creating minimal header")
                return True
    
    # Fix file permissions
    fix_file_permissions(file_path)
    
    # Try to repair with gbak
    if repair_with_gbak(file_path):
        logger.info("Successfully repaired database with gbak")
        return True
    
    # If all repair attempts fail, restore from backup if user confirms
    logger.error("All repair attempts failed")
    
    if backup_path and os.path.exists(backup_path):
        user_input = input(f"All repair attempts failed. Restore from backup {backup_path}? (y/n): ")
        if user_input.lower() == 'y':
            try:
                shutil.copy2(backup_path, file_path)
                logger.info(f"Restored original file from backup: {backup_path}")
            except Exception as e:
                logger.error(f"Error restoring from backup: {e}")
    
    return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Repair a corrupted Firebird database file')
    parser.add_argument('file_path', help='Path to the database file to repair')
    args = parser.parse_args()
    
    file_path = args.file_path
    
    if repair_database(file_path):
        print(f"\nDatabase repair completed successfully: {file_path}")
        return 0
    else:
        print(f"\nDatabase repair failed: {file_path}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
