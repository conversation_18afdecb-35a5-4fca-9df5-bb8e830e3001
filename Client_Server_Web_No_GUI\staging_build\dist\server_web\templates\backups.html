{% extends "layout.html" %}

{% block title %}Database Management{% endblock %}

{% block extra_css %}
<style>
    /* Modal styles - PERBAIKAN */
    .backup-modal {
        display: none;
        position: fixed;
        z-index: 9999; /* Meningkatkan z-index */
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        background-color: rgba(0,0,0,0.6);
    }

    .backup-modal-content {
        background-color: #fefefe;
        margin: 10% auto;
        padding: 20px;
        border: 1px solid #888;
        width: 60%;
        border-radius: 8px;
        box-shadow: 0 4px 12px 0 rgba(0,0,0,0.3);
        position: relative; /* Memastikan posisi relatif */
    }

    .backup-close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .backup-close:hover,
    .backup-close:focus {
        color: black;
        text-decoration: none;
    }

    /* Progress bar styling */
    .progress {
        height: 25px;
        margin-top: 15px;
        margin-bottom: 15px;
    }

    .progress-bar {
        font-size: 14px;
        line-height: 25px;
        font-weight: bold;
    }

    /* Debug console styling */
    #debugConsole {
        background-color: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        padding: 10px;
        height: 150px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        margin-top: 10px;
    }

    .debug-message {
        margin: 2px 0;
    }

    .debug-info {
        color: #0c5460;
    }

    .debug-warn {
        color: #856404;
    }

    .debug-error {
        color: #721c24;
    }

    /* Tambahkan style untuk tombol ketika ditekan */
    .btn-pressed {
        transform: translateY(2px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) inset !important;
    }

    /* MEGA upload button styling */
    .mega-upload-btn {
        background-color: #D9534F;
        color: white;
    }
    .mega-upload-btn:hover {
        background-color: #C9302C;
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Database Management</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">Database Transfer Actions</h6>
                                </div>
                                <div class="card-body">
                                    <!-- Form untuk backup request -->
                                    <form id="backupRequestForm" method="post" action="/api/backup/request">
                                        <div class="form-group">
                                            <label for="clientSelect">Select Client:</label>
                                            <select class="form-control" id="clientSelect" name="client_id" required>
                                                <option value="">-- Select Client --</option>
                                                {% for client in clients %}
                                                <option value="{{ client.id }}" data-client-name="{{ client.name }}" data-client-address="{{ client.address }}">{{ client.name }} ({{ client.address }})</option>
                                                {% endfor %}
                                            </select>
                                            <div class="form-text text-muted mt-1">
                                                <small>Connection status: <span id="connectionStatus">Unknown</span></small>
                                            </div>
                                        </div>

                                        <div class="btn-group mt-3" role="group">
                                            <button type="button" class="btn btn-success" id="importDatabaseBtn" onclick="importDatabase()">
                                                Import Database
                                            </button>
                                            <button type="button" class="btn btn-info" id="requestImportBtn" onclick="requestImport()">
                                                Request Database
                                            </button>
                                            <button type="button" class="btn btn-warning" id="megaUploadBtn" onclick="uploadToMegaFromClient()">
                                                Upload to MEGA
                                            </button>
                                            <button type="button" class="btn btn-primary" id="gdriveUploadBtn" onclick="uploadToGdriveFromClient()">
                                                Upload to GDrive
                                            </button>
                                        </div>

                                        <script>
                                        // Definisikan fungsi logDebug di sini agar tersedia untuk semua fungsi
                                        function logDebug(message, type = 'info') {
                                            var timestamp = new Date().toLocaleTimeString();
                                            console.log(`[DEBUG ${timestamp}] ${message}`);

                                            // Log ke debug console jika sudah tersedia
                                            var debugConsole = document.getElementById('debugConsole');
                                            if (debugConsole) {
                                                var msgDiv = document.createElement('div');
                                                msgDiv.className = 'debug-message debug-' + type;
                                                msgDiv.innerHTML = `[${timestamp}] ${message}`;
                                                debugConsole.insertBefore(msgDiv, debugConsole.firstChild);
                                            }

                                            // Log ke modal debug jika tersedia
                                            var modalDebugContent = document.getElementById('modalDebugContent');
                                            if (modalDebugContent) {
                                                modalDebugContent.innerHTML = `[${timestamp}] ${message}<br>` + modalDebugContent.innerHTML;
                                            }
                                        }

                                        function requestImport() {
                                            // INLINE CODE UNTUK MENGATASI MASALAH
                                            logDebug('Request Import button clicked');

                                            var clientId = document.getElementById('clientSelect').value;
                                            if (!clientId) {
                                                alert('Silakan pilih client terlebih dahulu');
                                                document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-warning\'>Silakan pilih client terlebih dahulu</div>';
                                                return;
                                            }

                                            // Tampilkan modal
                                            document.getElementById('importProgressModal').style.display = 'block';
                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-info\'>Mengirim permintaan import ke client...</div>';

                                            // Disable button
                                            document.getElementById('requestImportBtn').disabled = true;
                                            document.getElementById('requestImportBtn').innerHTML = '<span class=\'spinner-border spinner-border-sm\' role=\'status\' aria-hidden=\'true\'></span> Mengirim...';
                                            document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-info\'>Mengirim permintaan import...</div>';

                                            // Kirim request
                                            var xhr = new XMLHttpRequest();
                                            xhr.open('POST', '/api/backup/request', true);
                                            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4) {
                                                    document.getElementById('requestImportBtn').disabled = false;
                                                    document.getElementById('requestImportBtn').innerHTML = 'Request Import';

                                                    if (xhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(xhr.responseText);
                                                            console.log('Response:', response);

                                                            if (response.success) {
                                                                document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-success\'>' + response.message + '</div>';
                                                                document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-success\'>' + response.message + '<br>Memulai proses import...</div>';

                                                                // Poll status
                                                                var pollCount = 0;
                                                                var pollInterval = setInterval(function() {
                                                                    pollCount++;
                                                                    if (pollCount > 120) {
                                                                        clearInterval(pollInterval);
                                                                        return;
                                                                    }

                                                                    var statusXhr = new XMLHttpRequest();
                                                                    statusXhr.open('GET', '/api/backup/status?client_id=' + encodeURIComponent(clientId), true);
                                                                    statusXhr.onreadystatechange = function() {
                                                                        if (statusXhr.readyState === 4 && statusXhr.status === 200) {
                                                                            try {
                                                                                var statusData = JSON.parse(statusXhr.responseText);
                                                                                console.log('Status data:', statusData);

                                                                                if (statusData.success) {
                                                                                    var status = statusData.status;
                                                                                    var statusHtml = '';
                                                                                    var progressBar = document.querySelector('#modalProgressBar .progress-bar');

                                                                                    if (status === 'not_started' || status === 'none') {
                                                                                        statusHtml = '<div class=\'alert alert-info\'>Menunggu import dimulai...</div>';
                                                                                    } else if (status === 'requested') {
                                                                                        statusHtml = '<div class=\'alert alert-info\'>Permintaan import dikirim ke client. Menunggu respon...</div>';
                                                                                    } else if (status === 'in_progress') {
                                                                                        var progress = statusData.progress || 0;
                                                                                        statusHtml = '<div class=\'alert alert-info\'>Import sedang berjalan: ' + progress + '%</div>';

                                                                                        if (progressBar) {
                                                                                            progressBar.style.width = progress + '%';
                                                                                            progressBar.textContent = progress + '%';
                                                                                            progressBar.setAttribute('aria-valuenow', progress);
                                                                                        }
                                                                                    } else if (status === 'completed') {
                                                                                        statusHtml = '<div class=\'alert alert-success\'>Import selesai!</div>';

                                                                                        if (progressBar) {
                                                                                            progressBar.style.width = '100%';
                                                                                            progressBar.textContent = '100%';
                                                                                            progressBar.setAttribute('aria-valuenow', 100);
                                                                                        }

                                                                                        clearInterval(pollInterval);
                                                                                    } else if (status === 'failed') {
                                                                                        statusHtml = '<div class=\'alert alert-danger\'>Import gagal: ' + (statusData.error_message || 'Unknown error') + '</div>';
                                                                                        clearInterval(pollInterval);
                                                                                    }

                                                                                    document.getElementById('modalImportStatus').innerHTML = statusHtml;
                                                                                }
                                                                            } catch (e) {
                                                                                console.error('Error parsing status response:', e);
                                                                            }
                                                                        }
                                                                    };
                                                                    statusXhr.send();
                                                                }, 1000);
                                                            } else {
                                                                document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error: ' + (response.message || 'Unknown error') + '</div>';
                                                                document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Error: ' + (response.message || 'Unknown error') + '</div>';
                                                            }
                                                        } catch (e) {
                                                            console.error('Error parsing response:', e);
                                                            document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error parsing response: ' + e.message + '</div>';
                                                        }
                                                    } else {
                                                        document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>HTTP Error: ' + xhr.status + '</div>';
                                                        document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>HTTP Error: ' + xhr.status + '</div>';
                                                    }
                                                }
                                            };

                                            xhr.send('client_id=' + encodeURIComponent(clientId));
                                        }

                                        // Tambahkan variabel global untuk status transfer
                                        var transferAttempts = 0;
                                        var maxTransferAttempts = 3;
                                        var lastTransferBytes = 0;
                                        var transferStallDetected = false;
                                        var transferStallTimeout = null;
                                        var activeTransfer = null;
                                        var isTransferInProgress = false;

                                        // Cek apakah ada transfer aktif saat halaman dimuat
                                        document.addEventListener('DOMContentLoaded', function() {
                                            checkActiveTransfer();
                                        });

                                        // Fungsi untuk mengecek apakah ada transfer aktif
                                        function checkActiveTransfer() {
                                            var clientId = document.getElementById('clientSelect').value;
                                            if (!clientId) return;

                                            logDebug('Memeriksa transfer aktif untuk client: ' + clientId);

                                            var xhr = new XMLHttpRequest();
                                            xhr.open('GET', '/api/direct-db/status/' + encodeURIComponent(clientId), true);
                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4 && xhr.status === 200) {
                                                    try {
                                                        var statusData = JSON.parse(xhr.responseText);
                                                        logDebug('Status transfer: ' + JSON.stringify(statusData));

                                                        if (statusData.success && statusData.bytes_received > 0 && statusData.status === 'in_progress') {
                                                            // Ada transfer aktif
                                                            activeTransfer = statusData;
                                                            isTransferInProgress = true;

                                                            // Tampilkan notifikasi dan tombol resume
                                                            var resumeNotif = document.createElement('div');
                                                            resumeNotif.className = 'alert alert-info mt-2';
                                                            resumeNotif.innerHTML =
                                                                '<strong>Transfer aktif terdeteksi!</strong><br>' +
                                                                'Transfer database sedang berlangsung: ' + formatBytes(statusData.bytes_received) + ' diterima.<br>' +
                                                                '<div class="btn-group mt-2">' +
                                                                '<button class="btn btn-sm btn-success" onclick="resumeTransfer()">Lanjutkan Transfer</button> ' +
                                                                '<button class="btn btn-sm btn-danger" onclick="cancelTransfer()">Batalkan & Mulai Baru</button>' +
                                                                '</div>';

                                                            // Tambahkan notifikasi di atas tombol import
                                                            var importBtn = document.getElementById('importDatabaseBtn');
                                                            if (importBtn && importBtn.parentNode) {
                                                                importBtn.parentNode.insertBefore(resumeNotif, importBtn);
                                                            }

                                                            logDebug('Transfer aktif terdeteksi: ' + formatBytes(statusData.bytes_received) + ' telah diterima');
                                                        }
                                                    } catch (e) {
                                                        logDebug('Error memeriksa transfer aktif: ' + e.message, 'error');
                                                    }
                                                }
                                            };
                                            xhr.send();
                                        }

                                        // Fungsi untuk melanjutkan transfer yang ada
                                        function resumeTransfer() {
                                            logDebug('Melanjutkan transfer yang ada');

                                            var clientId = document.getElementById('clientSelect').value;
                                            if (!clientId) {
                                                alert('Silakan pilih client terlebih dahulu');
                                                return;
                                            }

                                            // Tampilkan modal
                                            document.getElementById('importProgressModal').style.display = 'block';
                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-info\'>Melanjutkan transfer database...</div>';

                                            // Reset progress tracking tetapi jangan reset byte counter
                                            transferAttempts = 0;
                                            transferStallDetected = false;
                                            if (transferStallTimeout) {
                                                clearTimeout(transferStallTimeout);
                                                transferStallTimeout = null;
                                            }

                                            // Dapatkan progress bar element
                                            var progressBar = document.querySelector('#modalProgressBar .progress-bar');
                                            if (!progressBar) {
                                                // Jika tidak ada, buat log dan beri tahu pengguna
                                                logDebug('KESALAHAN: Element progress bar tidak ditemukan!', 'error');
                                                document.getElementById('modalImportStatus').innerHTML =
                                                    '<div class="alert alert-danger">Error: Progress bar tidak ditemukan! Mohon refresh halaman dan coba lagi.</div>';
                                                return;
                                            }

                                            // Set progress bar ke status terakhir
                                            if (activeTransfer && activeTransfer.progress) {
                                                progressBar.style.width = activeTransfer.progress + '%';
                                                progressBar.textContent = activeTransfer.progress + '%';
                                                progressBar.setAttribute('aria-valuenow', activeTransfer.progress);
                                                progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                            } else {
                                                // Atau reset ke 0 jika tidak ada info
                                                progressBar.style.width = '0%';
                                                progressBar.textContent = '0%';
                                                progressBar.setAttribute('aria-valuenow', 0);
                                            }

                                            // Mulai tracking progress
                                            startTransferTracking(clientId);
                                        }

                                        // Fungsi untuk membatalkan transfer yang ada dan memulai baru
                                        function cancelTransfer() {
                                            logDebug('Membatalkan transfer yang ada dan memulai baru');

                                            var clientId = document.getElementById('clientSelect').value;
                                            if (!clientId) {
                                                alert('Silakan pilih client terlebih dahulu');
                                                return;
                                            }

                                            // Kirim request ke server untuk menghentikan transfer
                                            var xhr = new XMLHttpRequest();
                                            xhr.open('POST', '/api/direct-db/cancel/' + encodeURIComponent(clientId), true);
                                            xhr.setRequestHeader('Content-Type', 'application/json');

                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4) {
                                                    if (xhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(xhr.responseText);
                                                            if (response.success) {
                                                                logDebug('Transfer berhasil dibatalkan');
                                                                // Reset status dan mulai transfer baru
                                                                activeTransfer = null;
                                                                isTransferInProgress = false;

                                                                // Hapus notifikasi transfer aktif jika ada
                                                                var notifs = document.querySelectorAll('.alert.alert-info.mt-2');
                                                                notifs.forEach(function(notif) {
                                                                    if (notif.innerHTML.includes('Transfer aktif terdeteksi')) {
                                                                        notif.remove();
                                                                    }
                                                                });

                                                                // Mulai import baru
                                                                importDatabase();
                                                            } else {
                                                                logDebug('Gagal membatalkan transfer: ' + response.message, 'warn');
                                                                alert('Gagal membatalkan transfer: ' + response.message);
                                                            }
                                                        } catch (e) {
                                                            logDebug('Error parsing response: ' + e.message, 'error');
                                                            alert('Error saat membatalkan transfer. Silakan coba lagi.');
                                                        }
                                                    } else {
                                                        logDebug('HTTP error saat membatalkan transfer: ' + xhr.status, 'error');
                                                        alert('Error server saat membatalkan transfer. Silakan coba lagi.');
                                                    }
                                                }
                                            };

                                            xhr.send(JSON.stringify({ action: 'cancel' }));
                                        }

                                        function importDatabase() {
                                            logDebug('Import Database button clicked');

                                            var clientId = document.getElementById('clientSelect').value;
                                            if (!clientId) {
                                                alert('Silakan pilih client terlebih dahulu');
                                                document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-warning\'>Silakan pilih client terlebih dahulu</div>';
                                                return;
                                            }

                                            // Cek apakah ada transfer aktif
                                            if (isTransferInProgress && !confirm('Transfer database sedang berlangsung. Apakah Anda ingin membatalkannya dan memulai transfer baru?')) {
                                                return;
                                            }

                                            // Tampilkan modal
                                            document.getElementById('importProgressModal').style.display = 'block';
                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-info\'>Memulai proses import database dengan sistem transfer yang lebih handal...</div>';

                                            // Disable button
                                            document.getElementById('importDatabaseBtn').disabled = true;
                                            document.getElementById('importDatabaseBtn').innerHTML = '<span class=\'spinner-border spinner-border-sm\' role=\'status\' aria-hidden=\'true\'></span> Importing...';
                                            document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-info\'>Memulai proses import database dengan sistem transfer yang lebih handal...</div>';

                                            // Reset progress bar dan variabel global
                                            var progressBar = document.querySelector('#modalProgressBar .progress-bar');

                                            // Periksa apakah progressBar ada
                                            if (!progressBar) {
                                                logDebug('KESALAHAN: Element progress bar tidak ditemukan!', 'error');
                                                document.getElementById('modalImportStatus').innerHTML =
                                                    '<div class="alert alert-danger">Error: Progress bar tidak ditemukan! Mohon refresh halaman dan coba lagi.</div>';
                                                document.getElementById('importDatabaseBtn').disabled = false;
                                                document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                return;
                                            }

                                            progressBar.style.width = '0%';
                                            progressBar.textContent = '0%';
                                            progressBar.setAttribute('aria-valuenow', 0);

                                            transferAttempts = 0;
                                            lastTransferBytes = 0;
                                            transferStallDetected = false;
                                            if (transferStallTimeout) {
                                                clearTimeout(transferStallTimeout);
                                                transferStallTimeout = null;
                                            }

                                            // Tandai bahwa transfer sedang berlangsung
                                            isTransferInProgress = true;

                                            // Mulai proses import dengan sistem robust transfer
                                            startRobustImport(clientId);
                                        }

                                        // Fungsi untuk memulai import dengan sistem robust transfer
                                        function startRobustImport(clientId) {
                                            logDebug('Memulai robust import untuk client: ' + clientId);

                                            // Dapatkan nama client untuk informasi yang lebih baik
                                            var clientSelect = document.getElementById('clientSelect');
                                            var clientName = '';
                                            if (clientSelect) {
                                                var selectedOption = clientSelect.options[clientSelect.selectedIndex];
                                                if (selectedOption) {
                                                    clientName = selectedOption.getAttribute('data-client-name') || '';
                                                }
                                            }

                                            // Langkah 1: Dapatkan akses langsung ke database
                                            logDebug('Requesting direct database access for client: ' + clientId);
                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-info\'>Meminta akses langsung ke database client...</div>';

                                            var xhr = new XMLHttpRequest();
                                            xhr.open('GET', '/api/direct-db/access/' + encodeURIComponent(clientId), true);

                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4) {
                                                    if (xhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(xhr.responseText);
                                                            logDebug('Direct access response: ' + JSON.stringify(response));

                                                            if (response.success) {
                                                                // Langkah 2: Inisialisasi transfer dengan sistem robust
                                                                initiateRobustTransfer(clientId, clientName, response.db_info);
                                                            } else {
                                                                document.getElementById('importDatabaseBtn').disabled = false;
                                                                document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                                document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Error getting direct access: ' + response.message + '</div>';
                                                                document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error getting direct access: ' + response.message + '</div>';
                                                                logDebug('Direct access error: ' + response.message, 'error');
                                                                isTransferInProgress = false;
                                                            }
                                                        } catch (e) {
                                                            document.getElementById('importDatabaseBtn').disabled = false;
                                                            document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                            logDebug('Error parsing direct access response: ' + e.message, 'error');
                                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Error parsing direct access response: ' + e.message + '</div>';
                                                            document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error parsing direct access response: ' + e.message + '</div>';
                                                            isTransferInProgress = false;
                                                        }
                                                    } else {
                                                        document.getElementById('importDatabaseBtn').disabled = false;
                                                        document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                        logDebug('HTTP error during direct access request: ' + xhr.status, 'error');
                                                        document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>HTTP error during direct access request: ' + xhr.status + '</div>';
                                                        document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>HTTP error during direct access request: ' + xhr.status + '</div>';
                                                        isTransferInProgress = false;
                                                    }
                                                }
                                            };

                                            xhr.send();
                                        }

                                        // Fungsi untuk menginisialisasi transfer dengan sistem robust
                                        function initiateRobustTransfer(clientId, clientName, dbInfo) {
                                            logDebug('Initiating robust transfer for client: ' + clientId);
                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-info\'>Menginisialisasi transfer database dengan sistem yang lebih handal...</div>';

                                            // Persiapkan informasi file untuk transfer
                                            var fileInfo = {
                                                client_id: clientId,
                                                client_name: clientName || clientId,
                                                file_name: dbInfo?.file_name || 'database_' + clientId + '.fdb',
                                                file_size: dbInfo?.file_size || 0,
                                                chunk_size: 131072  // 128KB chunks for better reliability
                                            };

                                            // Kirim permintaan inisialisasi transfer
                                            var xhr = new XMLHttpRequest();
                                            xhr.open('POST', 'http://localhost:5001/api/robust-transfer/initiate/' + encodeURIComponent(clientId), true);
                                            xhr.setRequestHeader('Content-Type', 'application/json');

                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4) {
                                                    if (xhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(xhr.responseText);
                                                            logDebug('Robust transfer initiation response: ' + JSON.stringify(response));

                                                            if (response.success) {
                                                                // Mulai transfer database
                                                                startRobustTransfer(clientId, response.transfer_info);
                                                            } else {
                                                                document.getElementById('importDatabaseBtn').disabled = false;
                                                                document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                                document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Error initiating transfer: ' + response.message + '</div>';
                                                                document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error initiating transfer: ' + response.message + '</div>';
                                                                logDebug('Transfer initiation error: ' + response.message, 'error');
                                                                isTransferInProgress = false;
                                                            }
                                                        } catch (e) {
                                                            document.getElementById('importDatabaseBtn').disabled = false;
                                                            document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                            logDebug('Error parsing transfer initiation response: ' + e.message, 'error');
                                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Error parsing transfer initiation response: ' + e.message + '</div>';
                                                            document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error parsing transfer initiation response: ' + e.message + '</div>';
                                                            isTransferInProgress = false;
                                                        }
                                                    } else {
                                                        document.getElementById('importDatabaseBtn').disabled = false;
                                                        document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                        logDebug('HTTP error during transfer initiation: ' + xhr.status, 'error');
                                                        // Even if there's an HTTP error, the transfer might still be happening in the background
                                                        document.getElementById('modalImportStatus').innerHTML =
                                                            '<div class="alert alert-warning">HTTP error during transfer initiation: ' + xhr.status + '</div>' +
                                                            '<div class="alert alert-info">Transfer might still be happening in the background. Please wait...</div>' +
                                                            '<div class="mt-2"><button class="btn btn-sm btn-info" onclick="startTransferTracking(\'' + clientId + '\')">Check Transfer Status</button></div>';
                                                        document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>HTTP error during transfer initiation: ' + xhr.status + '</div>';
                                                        isTransferInProgress = false;
                                                    }
                                                }
                                            };

                                            xhr.send(JSON.stringify(fileInfo));
                                        }

                                        // Fungsi untuk memulai transfer database dengan sistem robust
                                        function startRobustTransfer(clientId, transferInfo) {
                                            logDebug('Starting robust transfer for client: ' + clientId);
                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-info\'>Memulai transfer database...</div>';

                                            // Mulai polling status transfer
                                            startRobustTransferPolling(clientId);

                                            // Mulai download database dengan direct access
                                            var downloadXhr = new XMLHttpRequest();
                                            downloadXhr.open('GET', '/api/direct-db/download/' + encodeURIComponent(clientId), true);

                                            downloadXhr.onreadystatechange = function() {
                                                if (downloadXhr.readyState === 4) {
                                                    // Tidak perlu menonaktifkan tombol di sini karena polling masih berjalan
                                                    if (downloadXhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(downloadXhr.responseText);
                                                            logDebug('Download initiated: ' + JSON.stringify(response));

                                                            // Tidak perlu melakukan apa-apa di sini karena polling akan menangani status
                                                        } catch (e) {
                                                            logDebug('Error parsing download response: ' + e.message, 'error');
                                                            // Tidak perlu menampilkan error karena polling masih berjalan
                                                        }
                                                    } else {
                                                        logDebug('HTTP error during download initiation: ' + downloadXhr.status, 'error');
                                                        // Tidak perlu menampilkan error karena polling masih berjalan
                                                    }
                                                }
                                            };

                                            downloadXhr.send();
                                        }

                                        // Fungsi untuk polling status transfer dengan sistem robust
                                        function startRobustTransferPolling(clientId) {
                                            logDebug('Starting robust transfer polling for client: ' + clientId);

                                            var progressBar = document.querySelector('#modalProgressBar .progress-bar');
                                            if (!progressBar) {
                                                logDebug('Progress bar not found', 'error');
                                                return;
                                            }

                                            // Mulai polling status transfer
                                            var pollInterval = setInterval(function() {
                                                var statusXhr = new XMLHttpRequest();
                                                statusXhr.open('GET', 'http://localhost:5001/api/robust-transfer/status/' + encodeURIComponent(clientId), true);

                                                statusXhr.onreadystatechange = function() {
                                                    if (statusXhr.readyState === 4 && statusXhr.status === 200) {
                                                        try {
                                                            var statusData = JSON.parse(statusXhr.responseText);
                                                            logDebug('Robust transfer status: ' + JSON.stringify(statusData));

                                                            if (statusData.success) {
                                                                var status = statusData.status;
                                                                var progress = statusData.progress || 0;
                                                                var bytesReceived = statusData.bytes_received || 0;
                                                                var shouldContinuePolling = statusData.should_continue_polling !== false;

                                                                // Update progress bar
                                                                progressBar.style.width = progress + '%';
                                                                progressBar.textContent = progress + '%';
                                                                progressBar.setAttribute('aria-valuenow', progress);

                                                                // Update status message
                                                                var statusMessage = '';
                                                                if (status === 'initializing') {
                                                                    statusMessage = '<div class="alert alert-info">Menginisialisasi transfer database...</div>';
                                                                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
                                                                } else if (status === 'transferring') {
                                                                    var formattedBytes = formatBytes(bytesReceived);
                                                                    var formattedSize = statusData.file_size_formatted || 'Unknown';
                                                                    statusMessage = '<div class="alert alert-info">Transfer database: ' + progress + '% selesai<br>' +
                                                                        'Diterima: ' + formattedBytes + ' dari ' + formattedSize + '</div>';
                                                                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                                                } else if (status === 'verifying') {
                                                                    statusMessage = '<div class="alert alert-info">Memverifikasi database...</div>';
                                                                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                                                } else if (status === 'completed') {
                                                                    statusMessage = '<div class="alert alert-success">Transfer selesai!</div>';
                                                                    progressBar.className = 'progress-bar bg-success';
                                                                    progressBar.style.width = '100%';
                                                                    progressBar.textContent = '100%';
                                                                    progressBar.setAttribute('aria-valuenow', 100);

                                                                    // Hentikan polling
                                                                    clearInterval(pollInterval);

                                                                    // Tampilkan pesan sukses
                                                                    displayCompletionMessage(statusData);

                                                                    // Reset status
                                                                    isTransferInProgress = false;
                                                                    document.getElementById('importDatabaseBtn').disabled = false;
                                                                    document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                                } else if (status === 'failed') {
                                                                    statusMessage = '<div class="alert alert-danger">Transfer gagal: ' +
                                                                        (statusData.error_message || 'Unknown error') + '</div>';
                                                                    progressBar.className = 'progress-bar bg-danger';

                                                                    // Hentikan polling
                                                                    clearInterval(pollInterval);

                                                                    // Reset status
                                                                    isTransferInProgress = false;
                                                                    document.getElementById('importDatabaseBtn').disabled = false;
                                                                    document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                                } else if (status === 'stalled') {
                                                                    statusMessage = '<div class="alert alert-warning">Transfer terhenti. Mencoba melanjutkan...</div>';
                                                                    progressBar.className = 'progress-bar bg-warning';

                                                                    // Coba resume transfer
                                                                    resumeRobustTransfer(clientId);
                                                                } else if (status === 'paused') {
                                                                    statusMessage = '<div class="alert alert-warning">Transfer dijeda. Klik tombol Resume untuk melanjutkan.</div>';
                                                                    progressBar.className = 'progress-bar bg-warning';

                                                                    // Tambahkan tombol resume
                                                                    statusMessage += '<div class="mt-2"><button class="btn btn-sm btn-success" onclick="resumeRobustTransfer(\'' +
                                                                        clientId + '\')">Resume Transfer</button></div>';
                                                                }

                                                                // Update status message
                                                                if (statusMessage) {
                                                                    document.getElementById('modalImportStatus').innerHTML = statusMessage;
                                                                }

                                                                // Hentikan polling jika tidak perlu dilanjutkan
                                                                if (!shouldContinuePolling) {
                                                                    clearInterval(pollInterval);
                                                                }
                                                            } else {
                                                                // Error mendapatkan status
                                                                logDebug('Error getting transfer status: ' + statusData.message, 'error');
                                                            }
                                                        } catch (e) {
                                                            logDebug('Error parsing transfer status: ' + e.message, 'error');
                                                        }
                                                    }
                                                };

                                                statusXhr.send();
                                            }, 1000);
                                        }

                                        // Fungsi untuk melanjutkan transfer yang terhenti
                                        function resumeRobustTransfer(clientId) {
                                            logDebug('Resuming robust transfer for client: ' + clientId);

                                            var xhr = new XMLHttpRequest();
                                            xhr.open('POST', 'http://localhost:5001/api/robust-transfer/resume/' + encodeURIComponent(clientId), true);
                                            xhr.setRequestHeader('Content-Type', 'application/json');

                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4) {
                                                    if (xhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(xhr.responseText);
                                                            logDebug('Resume response: ' + JSON.stringify(response));

                                                            if (response.success) {
                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                    '<div class="alert alert-info">Transfer dilanjutkan...</div>';
                                                            } else {
                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                    '<div class="alert alert-warning">Gagal melanjutkan transfer: ' +
                                                                    response.message + '</div>';
                                                            }
                                                        } catch (e) {
                                                            logDebug('Error parsing resume response: ' + e.message, 'error');
                                                        }
                                                    } else {
                                                        logDebug('HTTP error during resume: ' + xhr.status, 'error');
                                                    }
                                                }
                                            };

                                            xhr.send(JSON.stringify({ action: 'resume' }));
                                        }

                                        // Fungsi untuk membatalkan transfer
                                        function cancelRobustTransfer(clientId) {
                                            logDebug('Cancelling robust transfer for client: ' + clientId);

                                            var xhr = new XMLHttpRequest();
                                            xhr.open('POST', 'http://localhost:5001/api/robust-transfer/cancel/' + encodeURIComponent(clientId), true);
                                            xhr.setRequestHeader('Content-Type', 'application/json');

                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4) {
                                                    if (xhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(xhr.responseText);
                                                            logDebug('Cancel response: ' + JSON.stringify(response));

                                                            if (response.success) {
                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                    '<div class="alert alert-warning">Transfer dibatalkan.</div>';

                                                                // Reset status
                                                                isTransferInProgress = false;
                                                                document.getElementById('importDatabaseBtn').disabled = false;
                                                                document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                            } else {
                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                    '<div class="alert alert-danger">Gagal membatalkan transfer: ' +
                                                                    response.message + '</div>';
                                                            }
                                                        } catch (e) {
                                                            logDebug('Error parsing cancel response: ' + e.message, 'error');
                                                        }
                                                    } else {
                                                        logDebug('HTTP error during cancel: ' + xhr.status, 'error');
                                                    }
                                                }
                                            };

                                            xhr.send(JSON.stringify({ action: 'cancel' }));
                                        }

                                        // Fungsi untuk melacak progress transfer
                                        function startTransferTracking(clientId) {
                                            logDebug('Memulai tracking transfer untuk client: ' + clientId);
                                            var progressBar = document.querySelector('#modalProgressBar .progress-bar');

                                            // Pastikan progressBar ada
                                            if (!progressBar) {
                                                logDebug('KESALAHAN: Progress bar tidak ditemukan saat memulai tracking!', 'error');
                                                return;
                                            }

                                            // Start progress tracking
                                            var progressInterval = setInterval(function() {
                                                // Get progress from API
                                                var statusXhr = new XMLHttpRequest();
                                                statusXhr.open('GET', '/api/direct-db/status/' + encodeURIComponent(clientId), true);
                                                statusXhr.onreadystatechange = function() {
                                                    if (statusXhr.readyState === 4 && statusXhr.status === 200) {
                                                        try {
                                                            var statusData = JSON.parse(statusXhr.responseText);
                                                            logDebug('Progress update: ' + JSON.stringify(statusData));

                                                            if (statusData.success) {
                                                                var status = statusData.status;
                                                                var progress = statusData.progress || 0;
                                                                var bytesReceived = statusData.bytes_received || 0;

                                                                // Update progress bar
                                                                progressBar.style.width = progress + '%';
                                                                progressBar.textContent = progress + '%';
                                                                progressBar.setAttribute('aria-valuenow', progress);

                                                                // Update status message
                                                                if (bytesReceived > 0) {
                                                                    document.getElementById('modalImportStatus').innerHTML =
                                                                        '<div class="alert alert-info">Transfer database: ' + progress + '% selesai<br>' +
                                                                        'Transfer aktif: ' + formatBytes(bytesReceived) + ' diterima</div>';
                                                                }

                                                                // Cek apakah transfer selesai
                                                                if (status === 'complete' || statusData.polling_complete) {
                                                                    clearInterval(progressInterval);
                                                                    displayCompletionMessage(statusData);
                                                                }
                                                            }
                                                        } catch (e) {
                                                            logDebug('Error parsing status update: ' + e.message, 'error');
                                                        }
                                                    }
                                                };
                                                statusXhr.send();
                                            }, 1000);
                                        }

                                        function startImportProcess(clientId) {
                                            // Increment transfer attempt counter
                                            transferAttempts++;

                                            if (transferAttempts > maxTransferAttempts) {
                                                document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Gagal melakukan transfer setelah beberapa percobaan. Silakan coba lagi nanti.</div>';
                                                document.getElementById('importDatabaseBtn').disabled = false;
                                                document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                return;
                                            }

                                            logDebug(`Memulai/melanjutkan import database (percobaan ke-${transferAttempts})`);

                                            // Langkah 1: Dapatkan akses langsung ke database
                                            logDebug('Requesting direct database access for client: ' + clientId);
                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-info\'>Meminta akses langsung ke database client...</div>';

                                            var xhr = new XMLHttpRequest();
                                            xhr.open('GET', '/api/direct-db/access/' + encodeURIComponent(clientId), true);

                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4) {
                                                    if (xhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(xhr.responseText);
                                                            logDebug('Direct access response: ' + JSON.stringify(response));

                                                            // Even if success is false, we should continue as transfer might still be happening
                                                            if (response.success || (response.message && response.message.includes("may still be in progress"))) {
                                                                // Langkah 2: Download database
                                                                logDebug('Direct access successful, downloading database...');
                                                                document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-info\'>Akses diberikan. Mendownload database...</div>';

                                                                // Log database info for debugging
                                                                if (response.db_info) {
                                                                    logDebug('Database info: ' + JSON.stringify(response.db_info));
                                                                }

                                                                // Dapatkan referensi ke progress bar
                                                                var progressBar = document.querySelector('#modalProgressBar .progress-bar');
                                                                if (!progressBar) {
                                                                    logDebug('KESALAHAN: Element progress bar tidak ditemukan!', 'error');
                                                                    document.getElementById('modalImportStatus').innerHTML =
                                                                        '<div class="alert alert-danger">Error: Progress bar tidak ditemukan! Mohon refresh halaman dan coba lagi.</div>';
                                                                    return;
                                                                }

                                                                // Start real progress tracking with stall detection
                                                                var progressInterval = setInterval(function() {
                                                                    // Get real progress from API
                                                                    var statusXhr = new XMLHttpRequest();
                                                                    statusXhr.open('GET', '/api/direct-db/status/' + encodeURIComponent(clientId), true);
                                                                    statusXhr.onreadystatechange = function() {
                                                                        if (statusXhr.readyState === 4 && statusXhr.status === 200) {
                                                                            try {
                                                                                var statusData = JSON.parse(statusXhr.responseText);
                                                                                logDebug('Direct DB status: ' + JSON.stringify(statusData));

                                                                                if (statusData.success) {
                                                                                    var status = statusData.status;
                                                                                    var progress = statusData.progress || 0;
                                                                                    var bytesReceived = statusData.bytes_received || 0;
                                                                                    var isTransferActive = false;

                                                                                    // Deteksi stall: jika bytes tidak bertambah dalam waktu tertentu
                                                                                    if (bytesReceived > 0 && bytesReceived === lastTransferBytes) {
                                                                                        if (!transferStallDetected) {
                                                                                            transferStallTimeout = setTimeout(function() {
                                                                                                transferStallDetected = true;
                                                                                                logDebug('Transfer stall detected: ' + bytesReceived + ' bytes', 'warn');

                                                                                                // Jika transfer macet, coba lagi
                                                                                                clearInterval(progressInterval);
                                                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                                                    '<div class="alert alert-warning">Transfer terhenti. Mencoba melanjutkan... (percobaan ke-' + transferAttempts + ')</div>';

                                                                                                // Tunggu sebentar dan restart transfer
                                                                                                setTimeout(function() {
                                                                                                    startImportProcess(clientId);
                                                                                                }, 2000);
                                                                                            }, 20000); // Tunggu 20 detik sebelum dianggap macet
                                                                                        }
                                                                                    } else if (bytesReceived > lastTransferBytes) {
                                                                                        // Reset stall detection jika bytes bertambah
                                                                                        lastTransferBytes = bytesReceived;
                                                                                        transferStallDetected = false;
                                                                                        if (transferStallTimeout) {
                                                                                            clearTimeout(transferStallTimeout);
                                                                                            transferStallTimeout = setTimeout(function() {
                                                                                                transferStallDetected = true;
                                                                                                logDebug('Transfer stall detected after progress: ' + bytesReceived + ' bytes', 'warn');

                                                                                                // Jika transfer macet, coba lagi
                                                                                                clearInterval(progressInterval);
                                                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                                                    '<div class="alert alert-warning">Transfer terhenti setelah menerima ' +
                                                                                                    formatBytes(bytesReceived) + '. Mencoba melanjutkan... (percobaan ke-' + transferAttempts + ')</div>';

                                                                                                // Tunggu sebentar dan restart transfer
                                                                                                setTimeout(function() {
                                                                                                    startImportProcess(clientId);
                                                                                                }, 2000);
                                                                                            }, 30000); // Beri waktu lebih lama (30 detik) setelah progres
                                                                                        }

                                                                                        // Cek apakah polling harus dihentikan
                                                                                        if (statusData.polling_complete === true) {
                                                                                            logDebug('Transfer selesai (polling_complete flag). Menghentikan polling.', 'info');
                                                                                            clearInterval(progressInterval);
                                                                                            isTransferInProgress = false;

                                                                                            // Tunjukkan status selesai
                                                                                            displayCompletionMessage(statusData);
                                                                                            return;
                                                                                        }

                                                                                        // Cek apakah ada log transfer aktif dari backend
                                                                                        if (bytesReceived > 0) {
                                                                                            isTransferActive = true;
                                                                                            logDebug('Transfer aktif terdeteksi: ' + bytesReceived + ' bytes');
                                                                                        }

                                                                                        // Update progress bar - Perbaiki agar lebih realistis
                                                                                    if (progressBar) {
                                                                                            // Jika real progress lebih rendah dari yang ditampilkan, gunakan real progress
                                                                                            var currentProgress = parseInt(progressBar.getAttribute('aria-valuenow'));

                                                                                            // Hanya update jika progress baru berbeda atau status != in_progress
                                                                                            if (progress !== currentProgress || status !== 'in_progress' || isTransferActive) {
                                                                                                // Batasi kenaikan progress maksimal 5% per detik untuk mencegah lonjakan tiba-tiba
                                                                                                if (progress > currentProgress + 5 && currentProgress > 0 && status === 'in_progress') {
                                                                                                    progress = currentProgress + 5;
                                                                                                }

                                                                                                // Jangan pernah langsung ke 100% kecuali status = complete
                                                                                                if (progress >= 95 && status !== 'complete') {
                                                                                                    progress = 95;
                                                                                                }

                                                                                                // Gunakan nilai minimum 0% agar terlihat ada proses
                                                                                                progress = Math.max(0, progress);
                                                                                        progressBar.style.width = progress + '%';
                                                                                        progressBar.textContent = progress + '%';
                                                                                        progressBar.setAttribute('aria-valuenow', progress);
                                                                                            }
                                                                                    }

                                                                                    // Update status message
                                                                                        if (status === 'in_progress' || isTransferActive) {
                                                                                            var progressInfo = '';
                                                                                            if (bytesReceived > 0) {
                                                                                                var mbReceived = formatBytes(bytesReceived);
                                                                                                progressInfo = '<br>Transfer aktif: ' + mbReceived + ' diterima';
                                                                                            }

                                                                                        document.getElementById('modalImportStatus').innerHTML =
                                                                                                '<div class="alert alert-info">Transfer database: ' + progress + '% selesai' + progressInfo + '</div>';

                                                                                            // Update progress bar color
                                                                                            progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                                                                    } else if (status === 'complete') {
                                                                                        clearInterval(progressInterval);
                                                                                            isTransferInProgress = false;

                                                                                            // Set progress bar to 100%
                                                                                            if (progressBar) {
                                                                                                progressBar.style.width = '100%';
                                                                                                progressBar.textContent = '100%';
                                                                                                progressBar.setAttribute('aria-valuenow', 100);
                                                                                                progressBar.className = 'progress-bar bg-success';
                                                                                            }

                                                                                            // Tampilkan pesan sukses
                                                                                            displayCompletionMessage(statusData);
                                                                                    } else if (status === 'stalled') {
                                                                                        document.getElementById('modalImportStatus').innerHTML =
                                                                                                '<div class="alert alert-warning">Download terhenti pada ' + progress + '%. Mencoba melanjutkan...</div>';

                                                                                            // Update progress bar color
                                                                                            progressBar.className = 'progress-bar bg-warning';

                                                                                            // Coba restart transfer jika macet
                                                                                            clearInterval(progressInterval);
                                                                                            setTimeout(function() {
                                                                                                startImportProcess(clientId);
                                                                                            }, 3000);
                                                                                        } else if (status === 'not_started' && bytesReceived > 0) {
                                                                                            // Status API belum update tapi data sudah terdeteksi di background
                                                                                            var mbReceived = formatBytes(bytesReceived);
                                                                                            document.getElementById('modalImportStatus').innerHTML =
                                                                                                '<div class="alert alert-info">Transfer sedang berlangsung di background: ' +
                                                                                                mbReceived + ' diterima</div>';

                                                                                            // Update progress bar untuk menunjukkan aktivitas
                                                                                            progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                                                                        }
                                                                                    }
                                                                                }
                                                                            } catch (e) {
                                                                                logDebug('Error parsing direct DB status: ' + e.message, 'error');
                                                                            }
                                                                        }
                                                                    };
                                                                    statusXhr.send();
                                                                }, 1000);

                                                                // Download database
                                                                var downloadXhr = new XMLHttpRequest();
                                                                downloadXhr.open('GET', '/api/direct-db/download/' + encodeURIComponent(clientId), true);

                                                                downloadXhr.onreadystatechange = function() {
                                                                    if (downloadXhr.readyState === 4) {
                                                                        document.getElementById('importDatabaseBtn').disabled = false;
                                                                        document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';

                                                                        if (downloadXhr.status === 200) {
                                                                            try {
                                                                                var downloadResponse = JSON.parse(downloadXhr.responseText);
                                                                                logDebug('Download response: ' + JSON.stringify(downloadResponse));

                                                                                if (downloadResponse.success) {
                                                                                    // JANGAN otomatis anggap sukses karena fallback mode
                                                                                    // mungkin masih menggunakan filesize default
                                                                                    var isDefaultSize = downloadResponse.file_size === 1000000000;
                                                                                    var isFallbackMode = downloadResponse.status === 'in_progress';
                                                                                    var isFirebirdDB = downloadResponse.file_name &&
                                                                                                   downloadResponse.file_name.toLowerCase().endsWith('.fdb');

                                                                                    if (isDefaultSize || isFirebirdDB) {
                                                                                        logDebug(isFirebirdDB ? 'Database Firebird terdeteksi, memulai transfer khusus' :
                                                                                                'Fallback mode terdeteksi (file size default). Transfer masih berlangsung di background.');

                                                                                        // Tampilkan pesan bahwa transfer masih berlangsung
                                                                                        var infoMsg = '<div class=\'alert alert-info\'>Proses transfer sedang berlangsung.<br>' +
                                                                                            'Status awal: ' + downloadResponse.status + '<br>' +
                                                                                            'Filename: ' + downloadResponse.file_name + '<br>' +
                                                                                            'File type: ' + (isFirebirdDB ? 'Firebird Database (.fdb)' : 'Regular file') + '<br>' +
                                                                                            'Progress akan diperbarui secara otomatis.</div>';

                                                                                        document.getElementById('modalImportStatus').innerHTML = infoMsg;

                                                                                        // Mulai dari progress rendah untuk memberikan indikasi visual yang realistis
                                                                                        if (progressBar) {
                                                                                            progressBar.style.width = '0%';  // Mulai dari 0% bukan 1% atau 5%
                                                                                            progressBar.textContent = 'Menunggu data...';
                                                                                            progressBar.setAttribute('aria-valuenow', 0);
                                                                                            progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
                                                                                        }
                                                                                    } else {
                                                                                    // Update progress bar to 100%
                                                                                    if (progressBar) {
                                                                                        progressBar.style.width = '100%';
                                                                                        progressBar.textContent = '100%';
                                                                                        progressBar.setAttribute('aria-valuenow', 100);
                                                                                    }

                                                                                        // Tampilkan pesan sukses
                                                                                        displayCompletionMessage(downloadResponse);
                                                                                    }
                                                                                } else {
                                                                                    document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Error downloading database: ' + downloadResponse.message + '</div>';
                                                                                    document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error downloading database: ' + downloadResponse.message + '</div>';
                                                                                    logDebug('Download failed: ' + downloadResponse.message, 'error');
                                                                                    isTransferInProgress = false;
                                                                                }
                                                                            } catch (e) {
                                                                                logDebug('Error parsing download response: ' + e.message, 'error');
                                                                                logDebug('Raw response: ' + downloadXhr.responseText, 'error');
                                                                                document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Error parsing download response: ' + e.message + '</div>';
                                                                                document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error parsing download response: ' + e.message + '</div>';
                                                                                isTransferInProgress = false;
                                                                            }
                                                                        } else {
                                                                            logDebug('HTTP error during download: ' + downloadXhr.status, 'error');
                                                                            logDebug('Response text: ' + downloadXhr.responseText, 'error');
                                                                            document.getElementById('modalImportStatus').innerHTML =
                                                                                '<div class=\'alert alert-warning\'>HTTP error during download: ' + downloadXhr.status +
                                                                                '<br>Transfer mungkin sedang berlangsung di latar belakang</div>';
                                                                            document.getElementById('requestStatus').innerHTML =
                                                                                '<div class=\'alert alert-warning\'>HTTP error during download: ' + downloadXhr.status +
                                                                                '<br>Transfer mungkin sedang berlangsung di latar belakang</div>';
                                                                        }
                                                                    }
                                                                };

                                                                downloadXhr.send();
                                                            } else {
                                                                document.getElementById('importDatabaseBtn').disabled = false;
                                                                document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                                document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Error getting direct access: ' + response.message + '</div>';
                                                                document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error getting direct access: ' + response.message + '</div>';
                                                                logDebug('Direct access error: ' + response.message, 'error');
                                                                isTransferInProgress = false;
                                                            }
                                                        } catch (e) {
                                                            document.getElementById('importDatabaseBtn').disabled = false;
                                                            document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                            logDebug('Error parsing direct access response: ' + e.message, 'error');
                                                            logDebug('Raw response: ' + xhr.responseText, 'error');
                                                            document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>Error parsing direct access response: ' + e.message + '</div>';
                                                            document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>Error parsing direct access response: ' + e.message + '</div>';
                                                            isTransferInProgress = false;
                                                        }
                                                    } else {
                                                        document.getElementById('importDatabaseBtn').disabled = false;
                                                        document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';
                                                        logDebug('HTTP error during direct access request: ' + xhr.status, 'error');
                                                        logDebug('Response text: ' + xhr.responseText, 'error');
                                                        document.getElementById('modalImportStatus').innerHTML = '<div class=\'alert alert-danger\'>HTTP error during direct access request: ' + xhr.status + '</div>';
                                                        document.getElementById('requestStatus').innerHTML = '<div class=\'alert alert-danger\'>HTTP error during direct access request: ' + xhr.status + '</div>';
                                                        isTransferInProgress = false;
                                                    }
                                                }
                                            };

                                            xhr.send();
                                        }

                                        // Fungsi untuk menampilkan pesan sukses ketika transfer selesai
                                        function displayCompletionMessage(statusData) {
                                            var validationInfo = '';
                                            var bytesReceived = statusData.bytes_received || statusData.file_size || 0;

                                            // Tambahkan informasi validasi Firebird jika ada
                                            if (statusData.is_firebird === true) {
                                                if (statusData.is_valid_firebird === true || statusData.validation_result === true) {
                                                    validationInfo = '<div class="alert alert-success mb-2">Database Firebird tervalidasi dengan baik</div>';
                                                } else {
                                                    validationInfo = '<div class="alert alert-warning mb-2"><b>Peringatan:</b> ' +
                                                        'Database Firebird tidak tervalidasi: ' +
                                                        (statusData.validation_message || 'Database mungkin rusak atau tidak lengkap.') +
                                                        '<br><b>Mohon coba validasi menggunakan IBExpert sebagai alternatif FlameRobin.</b></div>';
                                                }
                                            }

                                            var successMsg = '<div class="alert alert-success">' +
                                                '<h5>Database berhasil diimport!</h5>' +
                                                'Filename: ' + (statusData.file_name || 'database.fdb') + '<br>' +
                                                'Size: ' + formatBytes(bytesReceived) + '<br>' +
                                                (statusData.elapsed_time ? 'Time: ' + statusData.elapsed_time + ' seconds<br>' : '') +
                                                '</div>' +
                                                validationInfo +
                                                '<div class="mt-3">' +
                                                '<a href="' + statusData.download_url + '" class="btn btn-primary" download>Download Database</a>' +
                                                '</div>';

                                            document.getElementById('modalImportStatus').innerHTML = successMsg;

                                            // Jangan auto-close modal agar user bisa download
                                            window.keepModalOpen = true;
                                            document.getElementById('autoCloseInfo').innerText = 'Modal akan tetap terbuka agar Anda bisa download database';

                                            // Enable tombol import
                                            document.getElementById('importDatabaseBtn').disabled = false;
                                            document.getElementById('importDatabaseBtn').innerHTML = 'Import Database';

                                            // Reset status transfer aktif
                                            isTransferInProgress = false;
                                        }

                                        // Modifikasi fungsi JavaScript untuk upload ke MEGA
                                        function pollMegaUploadStatus(clientId, requestId, intervalId) {
                                            var statusXhr = new XMLHttpRequest();
                                            statusXhr.open('GET', '/api/backup/mega-status?client_id=' + encodeURIComponent(clientId) + '&request_id=' + encodeURIComponent(requestId), true);

                                            statusXhr.onreadystatechange = function() {
                                                if (statusXhr.readyState === 4) {
                                                    if (statusXhr.status === 200) {
                                                        try {
                                                            var statusData = JSON.parse(statusXhr.responseText);
                                                            logDebug('MEGA upload status: ' + JSON.stringify(statusData));

                                                            if (statusData.success) {
                                                                updateMegaUploadStatusDisplay(statusData, intervalId);
                                                            }
                                                        } catch (e) {
                                                            logDebug('Error parsing MEGA status response: ' + e.message, 'error');
                                                        }
                                                    } else if (statusXhr.status === 404) {
                                                        // Status not found yet, which is normal in the beginning
                                                        logDebug('MEGA upload status not available yet');
                                                    } else {
                                                        logDebug('HTTP error during MEGA status request: ' + statusXhr.status, 'error');
                                                    }
                                                }
                                            };

                                            statusXhr.send();
                                        }

                                        function updateMegaUploadStatusDisplay(statusData, intervalId) {
                                            var progressBar = document.querySelector('#modalProgressBar .progress-bar');
                                            var status = statusData.status;
                                            var progressHtml = '';

                                            // Handle different statuses
                                            if (status === 'acknowledged') {
                                                progressHtml = '<div class="alert alert-info">Upload ke MEGA sedang dipersiapkan...</div>';

                                                if (progressBar) {
                                                    progressBar.style.width = '10%';
                                                    progressBar.textContent = 'Persiapan...';
                                                    progressBar.setAttribute('aria-valuenow', 10);
                                                }
                                            }
                                            else if (status === 'in_progress') {
                                                var progress = statusData.progress || 0;
                                                progressHtml = '<div class="alert alert-info">Upload ke MEGA sedang berlangsung: ' + progress + '%</div>';

                                                if (progressBar) {
                                                    progressBar.style.width = progress + '%';
                                                    progressBar.textContent = progress + '%';
                                                    progressBar.setAttribute('aria-valuenow', progress);
                                                }
                                            }
                                            else if (status === 'completed') {
                                                // Upload completed successfully
                                                var result = statusData.result || {};

                                                // Clear the interval since we're done
                                                if (intervalId) clearInterval(intervalId);

                                                if (progressBar) {
                                                    progressBar.style.width = '100%';
                                                    progressBar.textContent = '100%';
                                                    progressBar.setAttribute('aria-valuenow', 100);
                                                    progressBar.className = 'progress-bar bg-success';
                                                }

                                                // Display success message
                                                progressHtml =
                                                    '<div class="alert alert-success">' +
                                                    '<h5>Upload ke MEGA berhasil!</h5>' +
                                                    'File: ' + (result.file_name || 'database.fdb') + '<br>' +
                                                    (result.link ? 'Link: <a href="' + result.link + '" target="_blank">' + result.link + '</a><br>' : '') +
                                                    '</div>';
                                            }
                                            else if (status === 'failed') {
                                                // Upload failed
                                                var result = statusData.result || {};
                                                var errorMsg = result.error || 'Unknown error';

                                                // Clear the interval since we're done
                                                if (intervalId) clearInterval(intervalId);

                                                if (progressBar) {
                                                    progressBar.className = 'progress-bar bg-danger';
                                                }

                                                // Display error message
                                                progressHtml =
                                                    '<div class="alert alert-danger">' +
                                                    '<h5>Upload ke MEGA gagal</h5>' +
                                                    'Error: ' + errorMsg +
                                                    '</div>';
                                            }

                                            // Update the modal content
                                            if (progressHtml) {
                                                document.getElementById('modalImportStatus').innerHTML = progressHtml;
                                            }
                                        }

                                        // Fungsi baru untuk mengupload dari klien langsung ke MEGA
                                        function uploadToMegaFromClient() {
                                            var clientId = document.getElementById('clientSelect').value;
                                            if (!clientId) {
                                                alert('Silakan pilih client terlebih dahulu');
                                                return;
                                            }

                                            if (!confirm('Apakah Anda yakin ingin memicu client untuk mengupload database ke MEGA?')) {
                                                return;
                                            }

                                            logDebug('Mengirim permintaan upload ke MEGA untuk client: ' + clientId);

                                            // Tampilkan modal
                                            document.getElementById('importProgressModal').style.display = 'block';
                                            document.getElementById('modalImportStatus').innerHTML = '<div class="alert alert-info">Mengirim permintaan upload ke MEGA ke client...</div>';

                                            // Reset progress bar
                                            var progressBar = document.querySelector('#modalProgressBar .progress-bar');
                                            if (progressBar) {
                                                progressBar.style.width = '0%';
                                                progressBar.textContent = 'Mengirim permintaan...';
                                                progressBar.setAttribute('aria-valuenow', 0);
                                                progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                            }

                                            // Disable button
                                            document.getElementById('megaUploadBtn').disabled = true;
                                            document.getElementById('megaUploadBtn').innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Mengirim...';

                                            // Kirim permintaan ke server untuk memicu client upload ke MEGA
                                            var xhr = new XMLHttpRequest();
                                            xhr.open('POST', '/api/trigger-mega-upload', true);
                                            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4) {
                                                    // Re-enable button
                                                    document.getElementById('megaUploadBtn').disabled = false;
                                                    document.getElementById('megaUploadBtn').innerHTML = 'Upload to MEGA';

                                                    if (xhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(xhr.responseText);
                                                            logDebug('MEGA upload request response: ' + JSON.stringify(response));

                                                            if (response.success) {
                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                    '<div class="alert alert-success">Permintaan upload ke MEGA berhasil dikirim.</div>' +
                                                                    '<div class="alert alert-info">Client sedang memproses permintaan upload. Mohon tunggu...</div>';

                                                                // Start polling for status
                                                                var requestId = response.request_id;
                                                                var statusPollCount = 0;
                                                                var statusPollInterval = setInterval(function() {
                                                                    statusPollCount++;
                                                                    if (statusPollCount > 120) { // Limit to 2 minutes (120 seconds)
                                                                        clearInterval(statusPollInterval);
                                                                        document.getElementById('modalImportStatus').innerHTML +=
                                                                            '<div class="alert alert-warning">Timeout mencapai batas. Upload mungkin masih berlangsung di background.</div>';
                                                                        return;
                                                                    }

                                                                    // Poll for upload status
                                                                    pollMegaUploadStatus(clientId, requestId, statusPollInterval);
                                                                }, 2000); // Poll every 2 seconds
                                                            } else {
                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                    '<div class="alert alert-danger">Gagal mengirim permintaan upload ke MEGA: ' + response.message + '</div>';
                                                            }
                                                        } catch (e) {
                                                            logDebug('Error parsing MEGA upload response: ' + e.message, 'error');
                                                            document.getElementById('modalImportStatus').innerHTML =
                                                                '<div class="alert alert-danger">Error parsing MEGA upload response: ' + e.message + '</div>';
                                                        }
                                                    } else {
                                                        logDebug('HTTP error during MEGA upload request: ' + xhr.status, 'error');
                                                        document.getElementById('modalImportStatus').innerHTML =
                                                            '<div class="alert alert-danger">HTTP error during MEGA upload request: ' + xhr.status + '</div>';
                                                    }
                                                }
                                            };

                                            xhr.send('client_id=' + encodeURIComponent(clientId));
                                        }

                                        // Fungsi untuk upload ke GDrive dari client
                                        function uploadToGdriveFromClient() {
                                            var clientId = document.getElementById('clientSelect').value;
                                            if (!clientId) {
                                                alert('Silakan pilih client terlebih dahulu');
                                                return;
                                            }

                                            if (!confirm('Apakah Anda yakin ingin memicu client untuk mengupload database ke Google Drive?')) {
                                                return;
                                            }

                                            logDebug('Mengirim permintaan upload ke GDrive untuk client: ' + clientId);

                                            // Tampilkan modal
                                            document.getElementById('importProgressModal').style.display = 'block';
                                            document.getElementById('modalImportStatus').innerHTML = '<div class="alert alert-info">Mengirim permintaan upload ke Google Drive ke client...</div>';

                                            // Reset progress bar
                                            var progressBar = document.querySelector('#modalProgressBar .progress-bar');
                                            if (progressBar) {
                                                progressBar.style.width = '0%';
                                                progressBar.textContent = 'Mengirim permintaan...';
                                                progressBar.setAttribute('aria-valuenow', 0);
                                                progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                            }

                                            // Disable button
                                            document.getElementById('gdriveUploadBtn').disabled = true;
                                            document.getElementById('gdriveUploadBtn').innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Mengirim...';

                                            // Kirim permintaan ke server untuk memicu client upload ke GDrive
                                            var xhr = new XMLHttpRequest();
                                            xhr.open('POST', '/api/trigger-gdrive-upload', true);
                                            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4) {
                                                    // Re-enable button
                                                    document.getElementById('gdriveUploadBtn').disabled = false;
                                                    document.getElementById('gdriveUploadBtn').innerHTML = 'Upload to GDrive';

                                                    if (xhr.status === 200) {
                                                        try {
                                                            var response = JSON.parse(xhr.responseText);
                                                            logDebug('GDrive upload request response: ' + JSON.stringify(response));

                                                            if (response.success) {
                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                    '<div class="alert alert-success">Permintaan upload ke Google Drive berhasil dikirim.</div>' +
                                                                    '<div class="alert alert-info">Client sedang memproses permintaan upload. Mohon tunggu...</div>';

                                                                // Start polling for status
                                                                var requestId = response.request_id;
                                                                var statusPollCount = 0;
                                                                var statusPollInterval = setInterval(function() {
                                                                    statusPollCount++;
                                                                    if (statusPollCount > 120) { // Limit to 2 minutes (120 seconds)
                                                                        clearInterval(statusPollInterval);
                                                                        document.getElementById('modalImportStatus').innerHTML +=
                                                                            '<div class="alert alert-warning">Timeout mencapai batas. Upload mungkin masih berlangsung di background.</div>';
                                                                        return;
                                                                    }

                                                                    // Poll for upload status
                                                                    pollGdriveUploadStatus(clientId, requestId, statusPollInterval);
                                                                }, 2000); // Poll every 2 seconds
                                                            } else {
                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                    '<div class="alert alert-danger">Gagal mengirim permintaan upload ke Google Drive: ' + response.message + '</div>';
                                                            }
                                                        } catch (e) {
                                                            logDebug('Error parsing GDrive upload response: ' + e.message, 'error');
                                                            document.getElementById('modalImportStatus').innerHTML =
                                                                '<div class="alert alert-danger">Error parsing GDrive upload response: ' + e.message + '</div>';
                                                        }
                                                    } else {
                                                        logDebug('HTTP error during GDrive upload request: ' + xhr.status, 'error');
                                                        document.getElementById('modalImportStatus').innerHTML =
                                                            '<div class="alert alert-danger">HTTP error during GDrive upload request: ' + xhr.status + '</div>';
                                                    }
                                                }
                                            };

                                            xhr.send('client_id=' + encodeURIComponent(clientId));
                                        }

                                        // Fungsi untuk polling status upload ke GDrive
                                        function pollGdriveUploadStatus(clientId, requestId, pollInterval) {
                                            var statusXhr = new XMLHttpRequest();
                                            statusXhr.open('GET', '/api/backup/gdrive-status?client_id=' + encodeURIComponent(clientId) + '&request_id=' + encodeURIComponent(requestId), true);
                                            statusXhr.onreadystatechange = function() {
                                                if (statusXhr.readyState === 4) {
                                                    if (statusXhr.status === 200) {
                                                        try {
                                                            var statusData = JSON.parse(statusXhr.responseText);
                                                            logDebug('GDrive status: ' + JSON.stringify(statusData));

                                                            if (statusData.success) {
                                                                updateGdriveUploadStatusDisplay(statusData, pollInterval);
                                                            } else {
                                                                // Jika belum ada status, masih menunggu respons dari client
                                                                document.getElementById('modalImportStatus').innerHTML =
                                                                    '<div class="alert alert-info">Menunggu respons dari client...</div>';

                                                                // Log untuk debugging
                                                                logDebug('Belum ada status upload GDrive: ' + JSON.stringify(statusData), 'warn');
                                                            }
                                                        } catch (e) {
                                                            logDebug('Error parsing GDrive status: ' + e.message, 'error');
                                                        }
                                                    } else if (statusXhr.status === 404) {
                                                        // Status belum tersedia, masih menunggu
                                                        document.getElementById('modalImportStatus').innerHTML =
                                                            '<div class="alert alert-info">Menunggu client memulai upload...</div>';

                                                        // Log untuk debugging
                                                        logDebug('Status upload GDrive belum tersedia (404)', 'info');
                                                    } else {
                                                        logDebug('HTTP error getting GDrive status: ' + statusXhr.status, 'error');
                                                    }
                                                }
                                            };
                                            statusXhr.send();
                                        }

                                        // Fungsi untuk menampilkan status upload GDrive
                                        function updateGdriveUploadStatusDisplay(statusData, intervalId) {
                                            var progressBar = document.querySelector('#modalProgressBar .progress-bar');
                                            var status = statusData.status;
                                            var progress = statusData.progress || 0;

                                            // Log detail status untuk debugging
                                            logDebug('Memproses status GDrive: ' + status + ', progress: ' + progress + '%', 'info');

                                            // Update progress bar
                                            if (progressBar) {
                                                progressBar.style.width = progress + '%';
                                                progressBar.textContent = progress + '%';
                                                progressBar.setAttribute('aria-valuenow', progress);
                                            }

                                            // Handle different statuses
                                            if (status === 'pending' || status === 'received') {
                                                document.getElementById('modalImportStatus').innerHTML =
                                                    '<div class="alert alert-info">Permintaan upload ke Google Drive diterima oleh client...</div>';

                                                if (progressBar) {
                                                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                                }
                                            }
                                            else if (status === 'initializing') {
                                                document.getElementById('modalImportStatus').innerHTML =
                                                    '<div class="alert alert-info">Menginisialisasi upload ke Google Drive...</div>';

                                                if (progressBar) {
                                                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                                }
                                            }
                                            else if (status === 'uploading') {
                                                document.getElementById('modalImportStatus').innerHTML =
                                                    '<div class="alert alert-info">Sedang mengupload ke Google Drive: ' + progress + '%</div>';

                                                if (progressBar) {
                                                    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
                                                }
                                            }
                                            else if (status === 'completed') {
                                                // Get result details if available
                                                var result = statusData.result || {};
                                                var fileName = result.name || 'database.fdb';
                                                var viewLink = result.web_view_link || '';
                                                var downloadLink = result.web_content_link || '';

                                                var resultHtml = '<div class="alert alert-success">' +
                                                    '<h5>Upload ke Google Drive berhasil!</h5>' +
                                                    'File: ' + fileName + '<br>';

                                                if (viewLink) {
                                                    resultHtml += 'Link: <a href="' + viewLink + '" target="_blank">' + viewLink + '</a><br>';
                                                }

                                                resultHtml += '</div>';

                                                document.getElementById('modalImportStatus').innerHTML = resultHtml;

                                                if (progressBar) {
                                                    progressBar.style.width = '100%';
                                                    progressBar.textContent = '100%';
                                                    progressBar.setAttribute('aria-valuenow', 100);
                                                    progressBar.className = 'progress-bar bg-success';
                                                }

                                                // Stop polling
                                                clearInterval(intervalId);

                                                // Log success
                                                logDebug('Upload ke Google Drive berhasil: ' + fileName, 'success');
                                            }
                                            else if (status === 'failed') {
                                                // Get error details if available
                                                var result = statusData.result || {};
                                                var errorMsg = result.error || 'Unknown error';

                                                document.getElementById('modalImportStatus').innerHTML =
                                                    '<div class="alert alert-danger">' +
                                                    '<h5>Upload ke Google Drive gagal</h5>' +
                                                    'Error: ' + errorMsg +
                                                    '</div>';

                                                if (progressBar) {
                                                    progressBar.className = 'progress-bar bg-danger';
                                                }

                                                // Stop polling
                                                clearInterval(intervalId);

                                                // Log error
                                                logDebug('Upload ke Google Drive gagal: ' + errorMsg, 'error');
                                            }
                                            else {
                                                // Unknown status
                                                document.getElementById('modalImportStatus').innerHTML =
                                                    '<div class="alert alert-warning">Status tidak dikenal: ' + status + '</div>';

                                                // Log warning
                                                logDebug('Status upload GDrive tidak dikenal: ' + status, 'warn');
                                            }
                                        }
                                        </script>
                                    </form>

                                    <div id="requestStatus" class="mt-3"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">Available Imports</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered table-hover table-sm">
                                        <thead>
                                            <tr>
                                                <th>Client</th>
                                                <th>Filename</th>
                                                <th>Size</th>
                                                <th>Date</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                        {% if backups %}
                                            {% for backup in backups %}
                                            <tr>
                                                <td>{{ backup.client_name }}</td>
                                                <td>{{ backup.filename }}</td>
                                                <td>{{ backup.size_formatted }}</td>
                                                <td>{{ backup.date }}</td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <a href="{{ backup.download_url }}" class="btn btn-sm btn-primary" download>Download</a>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        {% else %}
                                            <tr>
                                                <td colspan="5" class="text-center">No imports available</td>
                                            </tr>
                                        {% endif %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Debug Console (new) -->
                    <div class="card mt-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">Debug Console</h6>
                            <button id="clearDebugBtn" class="btn btn-sm btn-outline-secondary" onclick="document.getElementById('debugConsole').innerHTML = '';">Clear</button>
                        </div>
                        <div class="card-body">
                            <div id="debugConsole"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Backup Progress Modal - PERBAIKAN -->
<div id="importProgressModal" class="backup-modal">
    <div class="backup-modal-content">
        <span class="backup-close" onclick="document.getElementById('importProgressModal').style.display='none'">&times;</span>
        <h4 class="mb-3">Database Import Progress</h4>
        <div id="modalImportStatus">
            <div class="alert alert-info">Initializing import process...</div>
        </div>
        <div id="modalProgressBar" class="progress">
            <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
        </div>
        <div class="mt-3">
            <div class="btn-group">
                <button id="keepModalOpenBtn" class="btn btn-sm btn-info" onclick="window.keepModalOpen = true; document.getElementById('autoCloseInfo').innerText = 'Auto-close disabled';">Keep Open</button>
                <button id="cancelTransferBtn" class="btn btn-sm btn-danger" onclick="cancelCurrentTransfer()">Cancel Transfer</button>
            </div>
            <span id="autoCloseInfo" class="ml-2 small text-muted">Modal will close automatically after success/failure</span>
        </div>
        <script>
            function cancelCurrentTransfer() {
                var clientId = document.getElementById('clientSelect').value;
                if (!clientId) return;

                if (confirm('Are you sure you want to cancel the current transfer?')) {
                    cancelRobustTransfer(clientId);
                }
            }
        </script>
        <div id="modalDebugInfo" class="mt-3 small text-muted">
            <strong>Debug Info:</strong>
            <div id="modalDebugContent"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="/static/js/robust_transfer.js"></script>
<script>
console.log("Halaman backups.html dimuat");

// Logging function untuk debugging
function logDebug(message, type = 'info') {
    var timestamp = new Date().toLocaleTimeString();
    console.log(`[DEBUG ${timestamp}] ${message}`);

    // Log ke debug console jika sudah tersedia
    var debugConsole = document.getElementById('debugConsole');
    if (debugConsole) {
        var msgDiv = document.createElement('div');
        msgDiv.className = 'debug-message debug-' + type;
        msgDiv.innerHTML = `[${timestamp}] ${message}`;
        debugConsole.insertBefore(msgDiv, debugConsole.firstChild);
    }

    // Log ke modal debug jika tersedia
    var modalDebugContent = document.getElementById('modalDebugContent');
    if (modalDebugContent) {
        modalDebugContent.innerHTML = `[${timestamp}] ${message}<br>` + modalDebugContent.innerHTML;
    }
}

// Format bytes to human-readable format
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Check connection status
function checkConnectionStatus() {
    var clientId = document.getElementById('clientSelect')?.value;
    if (!clientId) {
        logDebug('No client selected, skipping connection check');
        return;
    }

    logDebug(`Checking connection status for client: ${clientId}`);

    var xhr = new XMLHttpRequest();
    xhr.open('GET', '/api/client/status?client_id=' + encodeURIComponent(clientId), true);

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var data = JSON.parse(xhr.responseText);
                    logDebug(`Connection status data: ${xhr.responseText}`);

                    var statusEl = document.getElementById('connectionStatus');
                    var buttonEl = document.getElementById('requestBackupBtn');

                    if (!statusEl || !buttonEl) {
                        logDebug('Status element or button element not found', 'warn');
                        return;
                    }

                    if (data.success) {
                        if (data.connected) {
                            statusEl.textContent = 'Connected';
                            statusEl.className = 'text-success';
                            buttonEl.disabled = false;
                            logDebug(`Client ${clientId} is connected`);
                        } else {
                            statusEl.textContent = 'Disconnected';
                            statusEl.className = 'text-danger';
                            buttonEl.disabled = true;
                            logDebug(`Client ${clientId} is disconnected`, 'warn');
                        }
                    } else {
                        statusEl.textContent = 'Unknown';
                        statusEl.className = 'text-warning';
                        buttonEl.disabled = true;
                        logDebug(`Client ${clientId} status unknown: ${data.message || 'No message'}`, 'warn');
                    }
                } catch (e) {
                    logDebug(`Error parsing connection status response: ${e.message}`, 'error');
                }
            } else {
                logDebug(`Error checking connection status: HTTP ${xhr.status}`, 'error');
            }
        }
    };

    xhr.send();
}

// Fungsi untuk mengupload dari klien langsung ke MEGA
function uploadToMegaFromClient() {
    var clientId = document.getElementById('clientSelect').value;
    if (!clientId) {
        alert('Silakan pilih client terlebih dahulu');
        return;
    }

    if (!confirm('Apakah Anda yakin ingin memicu client untuk mengupload database ke MEGA?')) {
        return;
    }

    logDebug('Mengirim permintaan upload ke MEGA untuk client: ' + clientId);

    // Tampilkan modal
    document.getElementById('importProgressModal').style.display = 'block';
    document.getElementById('modalImportStatus').innerHTML = '<div class="alert alert-info">Mengirim permintaan upload ke MEGA...</div>';

    // Reset progress bar
    var progressBar = document.querySelector('#modalProgressBar .progress-bar');
    if (progressBar) {
        progressBar.style.width = '0%';
        progressBar.textContent = 'Mengirim permintaan...';
        progressBar.setAttribute('aria-valuenow', 0);
        progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
    }

    // Disable button
    document.getElementById('megaUploadBtn').disabled = true;
    document.getElementById('megaUploadBtn').innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Mengirim...';

    // Kirim permintaan ke server untuk memicu client upload ke MEGA
    var xhr = new XMLHttpRequest();
    xhr.open('POST', '/api/trigger-mega-upload', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            // Re-enable button
            document.getElementById('megaUploadBtn').disabled = false;
            document.getElementById('megaUploadBtn').innerHTML = 'Upload to MEGA';

            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    logDebug('MEGA upload request response: ' + JSON.stringify(response));

                    if (response.success) {
                        document.getElementById('modalImportStatus').innerHTML =
                            '<div class="alert alert-success">Permintaan upload ke MEGA berhasil dikirim.</div>' +
                            '<div class="alert alert-info">Client sedang mengupload database ke MEGA. Mohon tunggu...</div>';

                        // Start polling for status
                        var requestId = response.request_id;
                        var statusPollCount = 0;
                        var statusPollInterval = setInterval(function() {
                            statusPollCount++;
                            if (statusPollCount > 120) { // Limit to 2 minutes (120 seconds)
                                clearInterval(statusPollInterval);
                                document.getElementById('modalImportStatus').innerHTML +=
                                    '<div class="alert alert-warning">Timeout mencapai batas. Upload mungkin masih berlangsung di background.</div>';
                                return;
                            }

                            // Poll for upload status
                            pollMegaUploadStatus(clientId, requestId, statusPollInterval);
                        }, 2000); // Poll every 2 seconds
                    } else {
                        document.getElementById('modalImportStatus').innerHTML =
                            '<div class="alert alert-danger">Gagal mengirim permintaan upload ke MEGA: ' + response.message + '</div>';
                    }
                } catch (e) {
                    logDebug('Error parsing MEGA upload response: ' + e.message, 'error');
                    document.getElementById('modalImportStatus').innerHTML =
                        '<div class="alert alert-danger">Error parsing MEGA upload response: ' + e.message + '</div>';
                }
            } else {
                logDebug('HTTP error during MEGA upload request: ' + xhr.status, 'error');
                document.getElementById('modalImportStatus').innerHTML =
                    '<div class="alert alert-danger">HTTP error during MEGA upload request: ' + xhr.status + '</div>';
            }
        }
    };

    xhr.send('client_id=' + encodeURIComponent(clientId));
}

// Function to upload a backup file to MEGA
function uploadToMega(clientId, filename) {
    if (!confirm('Apakah Anda yakin ingin mengupload file ' + filename + ' ke MEGA?')) {
        return;
    }

    logDebug('Mengirim permintaan upload ke MEGA untuk file: ' + filename);

    // Tampilkan modal
    document.getElementById('importProgressModal').style.display = 'block';
    document.getElementById('modalImportStatus').innerHTML = '<div class="alert alert-info">Mengirim permintaan upload ke MEGA...</div>';

    // Reset progress bar
    var progressBar = document.querySelector('#modalProgressBar .progress-bar');
    if (progressBar) {
        progressBar.style.width = '0%';
        progressBar.textContent = 'Mengirim permintaan...';
        progressBar.setAttribute('aria-valuenow', 0);
        progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
    }

    // Kirim permintaan upload ke MEGA
    var xhr = new XMLHttpRequest();
    xhr.open('POST', '/api/backup/upload-to-mega', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    logDebug('MEGA upload request response: ' + JSON.stringify(response));

                    if (response.success) {
                        document.getElementById('modalImportStatus').innerHTML =
                            '<div class="alert alert-success">Permintaan upload ke MEGA berhasil dikirim.</div>' +
                            '<div class="alert alert-info">Client sedang mengupload file ke MEGA. Mohon tunggu...</div>';

                        // Start polling for status
                        var requestId = response.request_id;
                        var statusPollCount = 0;
                        var statusPollInterval = setInterval(function() {
                            statusPollCount++;
                            if (statusPollCount > 120) { // Limit to 2 minutes (120 seconds)
                                clearInterval(statusPollInterval);
                                document.getElementById('modalImportStatus').innerHTML +=
                                    '<div class="alert alert-warning">Timeout mencapai batas. Upload mungkin masih berlangsung di background.</div>';
                                return;
                            }

                            // Poll for upload status
                            pollMegaUploadStatus(clientId, requestId, statusPollInterval);
                        }, 2000); // Poll every 2 seconds
                    } else {
                        document.getElementById('modalImportStatus').innerHTML =
                            '<div class="alert alert-danger">Gagal mengirim permintaan upload ke MEGA: ' + response.message + '</div>';
                    }
                } catch (e) {
                    logDebug('Error parsing MEGA upload response: ' + e.message, 'error');
                    document.getElementById('modalImportStatus').innerHTML =
                        '<div class="alert alert-danger">Error parsing MEGA upload response: ' + e.message + '</div>';
                }
            } else {
                logDebug('HTTP error during MEGA upload request: ' + xhr.status, 'error');
                document.getElementById('modalImportStatus').innerHTML =
                    '<div class="alert alert-danger">HTTP error during MEGA upload request: ' + xhr.status + '</div>';
            }
        }
    };

    xhr.send('client_id=' + encodeURIComponent(clientId) + '&filename=' + encodeURIComponent(filename));
}

function pollMegaUploadStatus(clientId, requestId, intervalId) {
    var statusXhr = new XMLHttpRequest();
    statusXhr.open('GET', '/api/backup/mega-status?client_id=' + encodeURIComponent(clientId) + '&request_id=' + encodeURIComponent(requestId), true);

    statusXhr.onreadystatechange = function() {
        if (statusXhr.readyState === 4) {
            if (statusXhr.status === 200) {
                try {
                    var statusData = JSON.parse(statusXhr.responseText);
                    logDebug('MEGA upload status: ' + JSON.stringify(statusData));

                    if (statusData.success) {
                        updateMegaUploadStatusDisplay(statusData, intervalId);
                    }
                } catch (e) {
                    logDebug('Error parsing MEGA status response: ' + e.message, 'error');
                }
            } else if (statusXhr.status === 404) {
                // Status not found yet, which is normal in the beginning
                logDebug('MEGA upload status not available yet');
            } else {
                logDebug('HTTP error during MEGA status request: ' + statusXhr.status, 'error');
            }
        }
    };

    statusXhr.send();
}

// Function to upload database to Google Drive directly from client
function uploadToGdriveFromClient() {
    var clientId = document.getElementById('clientSelect').value;
    if (!clientId) {
        alert('Silakan pilih client terlebih dahulu');
        return;
    }

    if (!confirm('Apakah Anda yakin ingin mengupload database dari client ke Google Drive?')) {
        return;
    }

    logDebug('Mengirim permintaan upload ke Google Drive untuk client: ' + clientId);

    // Tampilkan modal
    document.getElementById('importProgressModal').style.display = 'block';
    document.getElementById('modalImportStatus').innerHTML = '<div class="alert alert-info">Mengirim permintaan upload ke Google Drive...</div>';

    // Reset progress bar
    var progressBar = document.querySelector('#modalProgressBar .progress-bar');
    if (progressBar) {
        progressBar.style.width = '0%';
        progressBar.textContent = 'Mengirim permintaan...';
        progressBar.setAttribute('aria-valuenow', 0);
        progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated bg-info';
    }

    // Disable button
    document.getElementById('gdriveUploadBtn').disabled = true;
    document.getElementById('gdriveUploadBtn').innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Mengirim...';

    // Kirim permintaan upload ke GDrive
    var xhr = new XMLHttpRequest();
    xhr.open('POST', '/api/trigger-gdrive-upload', true);
    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
            // Re-enable button
            document.getElementById('gdriveUploadBtn').disabled = false;
            document.getElementById('gdriveUploadBtn').innerHTML = 'Upload to GDrive';

            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    logDebug('GDrive upload request response: ' + JSON.stringify(response));

                    if (response.success) {
                        document.getElementById('modalImportStatus').innerHTML =
                            '<div class="alert alert-success">Permintaan upload ke Google Drive berhasil dikirim.</div>' +
                            '<div class="alert alert-info">Client sedang mengupload database ke Google Drive. Mohon tunggu...</div>';

                        // Start polling for status
                        var requestId = response.request_id;
                        var statusPollCount = 0;
                        var statusPollInterval = setInterval(function() {
                            statusPollCount++;
                            if (statusPollCount > 120) { // Limit to 2 minutes (120 seconds)
                                clearInterval(statusPollInterval);
                                document.getElementById('modalImportStatus').innerHTML +=
                                    '<div class="alert alert-warning">Timeout mencapai batas. Upload mungkin masih berlangsung di background.</div>';
                                return;
                            }

                            // Poll for upload status
                            pollGdriveUploadStatus(clientId, requestId, statusPollInterval);
                        }, 2000); // Poll every 2 seconds
                    } else {
                        document.getElementById('modalImportStatus').innerHTML =
                            '<div class="alert alert-danger">Gagal mengirim permintaan upload ke Google Drive: ' + response.message + '</div>';
                    }
                } catch (e) {
                    logDebug('Error parsing GDrive upload response: ' + e.message, 'error');
                    document.getElementById('modalImportStatus').innerHTML =
                        '<div class="alert alert-danger">Error parsing GDrive upload response: ' + e.message + '</div>';
                }
            } else {
                logDebug('HTTP error during GDrive upload request: ' + xhr.status, 'error');
                document.getElementById('modalImportStatus').innerHTML =
                    '<div class="alert alert-danger">HTTP error during GDrive upload request: ' + xhr.status + '</div>';
            }
        }
    };

    xhr.send('client_id=' + encodeURIComponent(clientId));
}

function pollGdriveUploadStatus(clientId, requestId, intervalId) {
    var statusXhr = new XMLHttpRequest();
    statusXhr.open('GET', '/api/backup/gdrive-status?client_id=' + encodeURIComponent(clientId) + '&request_id=' + encodeURIComponent(requestId), true);

    statusXhr.onreadystatechange = function() {
        if (statusXhr.readyState === 4) {
            if (statusXhr.status === 200) {
                try {
                    var statusData = JSON.parse(statusXhr.responseText);
                    logDebug('GDrive upload status: ' + JSON.stringify(statusData));

                    if (statusData.success) {
                        updateGdriveUploadStatusDisplay(statusData, intervalId);
                    }
                } catch (e) {
                    logDebug('Error parsing GDrive status response: ' + e.message, 'error');
                }
            } else if (statusXhr.status === 404) {
                // Status not found yet, which is normal in the beginning
                logDebug('GDrive upload status not available yet');
            } else {
                logDebug('HTTP error during GDrive status request: ' + statusXhr.status, 'error');
            }
        }
    };

    statusXhr.send();
}

function updateGdriveUploadStatusDisplay(statusData, intervalId) {
    var progressBar = document.querySelector('#modalProgressBar .progress-bar');
    var status = statusData.status;
    var progressHtml = '';

    // Handle different statuses
    if (status === 'pending' || status === 'acknowledged' || status === 'initializing') {
        progressHtml = '<div class="alert alert-info">Upload ke Google Drive sedang dipersiapkan...</div>';

        if (progressBar) {
            progressBar.style.width = '10%';
            progressBar.textContent = 'Persiapan...';
            progressBar.setAttribute('aria-valuenow', 10);
        }
    }
    else if (status === 'uploading' || status === 'in_progress') {
        var progress = statusData.progress || 0;
        progressHtml = '<div class="alert alert-info">Upload ke Google Drive sedang berlangsung: ' + progress + '%</div>';

        if (progressBar) {
            progressBar.style.width = progress + '%';
            progressBar.textContent = progress + '%';
            progressBar.setAttribute('aria-valuenow', progress);
        }
    }
    else if (status === 'completed') {
        // Upload completed successfully
        var result = statusData.result || {};

        // Clear the interval since we're done
        if (intervalId) clearInterval(intervalId);

        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.textContent = '100%';
            progressBar.setAttribute('aria-valuenow', 100);
            progressBar.className = 'progress-bar bg-success';
        }

        // Display success message with links
        progressHtml =
            '<div class="alert alert-success">' +
            '<h5>Upload ke Google Drive berhasil!</h5>' +
            'File: ' + (result.name || 'database.fdb') + '<br>';

        if (result.web_view_link) {
            progressHtml += 'Link View: <a href="' + result.web_view_link + '" target="_blank">' + result.web_view_link + '</a><br>';
        }

        if (result.web_content_link) {
            progressHtml += 'Link Download: <a href="' + result.web_content_link + '" target="_blank">' + result.web_content_link + '</a><br>';
        }

        progressHtml += '</div>';
    }
    else if (status === 'failed') {
        // Upload failed
        var errorMsg = statusData.result || 'Unknown error';
        if (typeof errorMsg === 'object') {
            errorMsg = errorMsg.error || JSON.stringify(errorMsg);
        }

        // Clear the interval since we're done
        if (intervalId) clearInterval(intervalId);

        if (progressBar) {
            progressBar.className = 'progress-bar bg-danger';
        }

        // Display error message
        progressHtml =
            '<div class="alert alert-danger">' +
            '<h5>Upload ke Google Drive gagal</h5>' +
            'Error: ' + errorMsg +
            '</div>';
    }

    // Update the modal content
    if (progressHtml) {
        document.getElementById('modalImportStatus').innerHTML = progressHtml;
    }
}

function updateMegaUploadStatusDisplay(statusData, intervalId) {
    var progressBar = document.querySelector('#modalProgressBar .progress-bar');
    var status = statusData.status;
    var progressHtml = '';

    // Handle different statuses
    if (status === 'acknowledged') {
        progressHtml = '<div class="alert alert-info">Upload ke MEGA sedang dipersiapkan...</div>';

        if (progressBar) {
            progressBar.style.width = '10%';
            progressBar.textContent = 'Persiapan...';
            progressBar.setAttribute('aria-valuenow', 10);
        }
    }
    else if (status === 'in_progress') {
        var progress = statusData.progress || 0;
        progressHtml = '<div class="alert alert-info">Upload ke MEGA sedang berlangsung: ' + progress + '%</div>';

        if (progressBar) {
            progressBar.style.width = progress + '%';
            progressBar.textContent = progress + '%';
            progressBar.setAttribute('aria-valuenow', progress);
        }
    }
    else if (status === 'completed') {
        // Upload completed successfully
        var result = statusData.result || {};

        // Clear the interval since we're done
        if (intervalId) clearInterval(intervalId);

        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.textContent = '100%';
            progressBar.setAttribute('aria-valuenow', 100);
            progressBar.className = 'progress-bar bg-success';
        }

        // Display success message
        progressHtml =
            '<div class="alert alert-success">' +
            '<h5>Upload ke MEGA berhasil!</h5>' +
            'File: ' + (result.file_name || 'database.fdb') + '<br>' +
            (result.link ? 'Link: <a href="' + result.link + '" target="_blank">' + result.link + '</a><br>' : '') +
            '</div>';
    }
    else if (status === 'failed') {
        // Upload failed
        var result = statusData.result || {};
        var errorMsg = result.error || 'Unknown error';

        // Clear the interval since we're done
        if (intervalId) clearInterval(intervalId);

        if (progressBar) {
            progressBar.className = 'progress-bar bg-danger';
        }

        // Display error message
        progressHtml =
            '<div class="alert alert-danger">' +
            '<h5>Upload ke MEGA gagal</h5>' +
            'Error: ' + errorMsg +
            '</div>';
    }

    // Update the modal content
    if (progressHtml) {
        document.getElementById('modalImportStatus').innerHTML = progressHtml;
    }
}

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    logDebug('DOM Content Loaded - Initializing backup page');

    // Setup client select change handler
    var clientSelect = document.getElementById('clientSelect');
    if (clientSelect) {
        clientSelect.addEventListener('change', function() {
            logDebug('Client select changed to: ' + this.value);
            checkConnectionStatus();
        });
        logDebug('Client select change handler setup');
    } else {
        logDebug('Client select element not found', 'warn');
    }

    // Initial connection status check
    checkConnectionStatus();

    // Set up interval untuk cek status
    setInterval(checkConnectionStatus, 5000);

    // Tambahkan handler untuk global errors
    window.onerror = function(message, source, lineno, colno, error) {
        logDebug('GLOBAL ERROR: ' + message + ' at ' + source + ' line ' + lineno, 'error');
        return false;
    };

    logDebug('Backup page initialization complete');
});
</script>
{% endblock %}
