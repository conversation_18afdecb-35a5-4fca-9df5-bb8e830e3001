{% extends 'layout.html' %}

{% block title %}Monitoring - Firebird Query Server{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Monitoring Dashboard</h1>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Monitoring Dashboard</strong> allows you to run predefined SQL queries to monitor database health and identify issues.
            Select a query from the left panel to run it on all connected clients.
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>Monitoring Queries
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group" id="monitoring-queries">
                    {% if predefined_queries %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">monitor_diff_job_field</h6>
                                <button type="button" class="btn btn-sm btn-primary monitoring-query" data-query-name="monitor_diff_job_field">
                                    <i class="fas fa-play me-1"></i>Run
                                </button>
                            </div>
                            <p class="mb-0 small text-muted">
                                Query untuk menemukan data yang salah di bulan berjalan (bulan saat ini). Monitoring query to find inconsistencies between job codes and field codes.
                            </p>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No monitoring queries defined. Add SQL files to the 'queries' directory.
                        </div>
                    {% endif %}
                </div>
            </div>
            <div class="card-footer">
                <button type="button" class="btn btn-success" id="add-monitoring-query">
                    <i class="fas fa-plus me-1"></i>Add New Query
                </button>
            </div>
        </div>
    </div>

    <div class="col-md-8 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>Monitoring Results
                    <div class="spinner-border spinner-border-sm text-light ms-2 d-none" id="monitoring-loading" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </h5>
            </div>
            <div class="card-body">
                <div id="monitoring-results">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Select a monitoring query from the left panel to run it.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Recent Monitoring Results
                </h5>
            </div>
            <div class="card-body">
                <ul class="nav nav-tabs" id="history-tabs" role="tablist">
                    <!-- Tabs will be dynamically added here -->
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="history-all-tab" data-bs-toggle="tab" data-bs-target="#history-all" type="button" role="tab" aria-controls="history-all" aria-selected="true">All History</button>
                    </li>
                </ul>
                <div class="tab-content mt-3" id="history-content">
                    <!-- Tab content will be dynamically added here -->
                    <div class="tab-pane fade show active" id="history-all" role="tabpanel" aria-labelledby="history-all-tab">
                        <div id="history-all-container">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>No monitoring history yet. Run a monitoring query to see results.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Monitoring Query Modal -->
<div class="modal fade" id="addMonitoringModal" tabindex="-1" aria-labelledby="addMonitoringModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addMonitoringModalLabel">Add Monitoring Query</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="add-monitoring-form">
                    <div class="mb-3">
                        <label for="monitoring-name" class="form-label">Query Name:</label>
                        <input type="text" class="form-control" id="monitoring-name" name="query_name" required>
                        <div class="form-text">Use a descriptive name without spaces (e.g., monitor_active_users)</div>
                    </div>
                    <div class="mb-3">
                        <label for="monitoring-description" class="form-label">Description:</label>
                        <textarea class="form-control" id="monitoring-description" name="query_description" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="monitoring-query" class="form-label">SQL Query:</label>
                        <textarea class="form-control" id="monitoring-query" name="query" rows="10" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="save-monitoring-query">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- View Query Modal -->
<div class="modal fade" id="viewQueryModal" tabindex="-1" aria-labelledby="viewQueryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="viewQueryModalLabel">Query Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <h6 class="mb-3" id="view-query-name"></h6>
                <pre id="view-query-content" class="bg-light p-3 border rounded"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="edit-query-btn">Edit</button>
            </div>
        </div>
    </div>
</div>

<!-- Variables Modal -->
<div class="modal fade" id="variablesModal" tabindex="-1" aria-labelledby="variablesModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="variablesModalLabel">Enter Variable Values</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="variables-modal-body">
                <!-- Variable inputs will be dynamically added here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="variables-confirm">Confirm</button>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<footer class="footer mt-auto py-3 bg-light">
    <div class="container">
        <span class="text-muted">Firebird Database Monitoring</span>
    </div>
</footer>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Run monitoring query
        $('.monitoring-query').click(function() {
            const queryName = $(this).data('query-name');

            // Show loading indicator
            $('#monitoring-loading').removeClass('d-none');

            // Clear previous results
            $('#monitoring-results').html('');

            // Run query
            runQuery(queryName);
        });

        function runQuery(queryName) {
            if (!queryName) {
                alert('Please select a query to run.');
                return;
            }
            
            // Get the query content
            const query = predefinedQueries[queryName];
            
            // Check if query has variables
            const variables = extractVariables(query);
            if (variables.length > 0) {
                // Show modal with variable inputs
                let html = '<div class="mb-3"><p>Please enter values for the following variables:</p>';
                
                variables.forEach(variable => {
                    html += `
                        <div class="input-group input-group-sm mb-2">
                            <span class="input-group-text">#${variable}#</span>
                            <input type="text" class="form-control variable-input" 
                                   data-variable="${variable}" placeholder="Enter value for #${variable}#">
                        </div>
                    `;
                });
                
                html += '</div>';
                
                // Show modal with variable inputs
                $('#variables-modal-body').html(html);
                const variablesModal = new bootstrap.Modal(document.getElementById('variablesModal'));
                variablesModal.show();
                
                // Save reference to continue execution after variables are entered
                window.pendingQueryExecution = {
                    queryName: queryName
                };
                
                return;
            }
            
            // If no variables, run the query immediately
            runQueryWithoutVariables(queryName);
        }
        
        // Function to run a query without variables
        function runQueryWithoutVariables(queryName, variables = {}) {
            // Show loading indicator
            $('#query-loading').show();
            $('#monitoring-results').html('<div class="text-center mt-5 mb-5"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">Running query...</p></div>');

            // Send request to run query
            $.post('/api/monitoring/run', {
                query_name: queryName,
                variables: JSON.stringify(variables)
            }, function(data) {
                if (data.success) {
                    // Get query results
                    getQueryResults(queryName);
                } else {
                    // Hide loading indicator
                    $('#query-loading').hide();
                    
                    // Show error
                    $('#monitoring-results').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>Error running query: ${data.message}
                        </div>
                    `);
                }
            }).fail(function() {
                // Hide loading indicator
                $('#query-loading').hide();
                
                // Show error
                $('#monitoring-results').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>Failed to run query. Please try again.
                    </div>
                `);
            });
        }

        // Variables modal confirm button
        $('#variables-confirm').click(function() {
            // Get the pending query execution
            const pendingExecution = window.pendingQueryExecution;
            if (!pendingExecution) return;
            
            // Collect variable values
            const queryVariables = {};
            $('.variable-input').each(function() {
                const variable = $(this).data('variable');
                const value = $(this).val();
                
                if (value) {
                    queryVariables[variable] = value;
                }
            });
            
            // Hide the modal
            bootstrap.Modal.getInstance(document.getElementById('variablesModal')).hide();
            
            // Run the query with variables
            runQueryWithoutVariables(pendingExecution.queryName, queryVariables);
            
            // Clear pending execution
            window.pendingQueryExecution = null;
        });
        
        // Function to extract variables from query
        function extractVariables(query) {
            if (!query) return [];
            
            const variableRegex = /#([A-Za-z0-9_]+)#/g;
            const variables = new Set();
            let match;

            while ((match = variableRegex.exec(query)) !== null) {
                variables.add(match[1]);
            }

            return Array.from(variables);
        }

        // Function to process and display monitoring results
        function processResults(results, queryName) {
            // Group results by client
            const clientResults = {};
            let allResultsHtml = `<h4 class="mb-3">Results for: ${queryName}</h4>`;

            results.forEach(result => {
                const clientName = result.client_name || 'Unknown Client';
                const clientId = result.client_id || 'unknown';

                if (!clientResults[clientId]) {
                    clientResults[clientId] = {
                        name: clientName,
                        results: []
                    };
                }

                clientResults[clientId].results.push(result);

                // Add to all results
                allResultsHtml += generateResultHtml(result, clientName);
            });

            // Update monitoring results
            $('#monitoring-results').html(allResultsHtml);

            // Add to history
            addToHistory(results, queryName);
        }

        // Function to add results to history
        function addToHistory(results, queryName) {
            // Create a new tab for this query run
            const timestamp = new Date().toLocaleString();
            const tabId = `history-${Date.now()}`;
            const contentId = `${tabId}-content`;

            // Add tab
            $('#history-tabs').append(`
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="${tabId}-tab" data-bs-toggle="tab" data-bs-target="#${contentId}" type="button" role="tab" aria-controls="${contentId}" aria-selected="false">${queryName} (${timestamp})</button>
                </li>
            `);

            // Add tab content
            let historyHtml = `<h4 class="mb-3">Results for: ${queryName} (${timestamp})</h4>`;

            results.forEach(result => {
                const clientName = result.client_name || 'Unknown Client';
                historyHtml += generateResultHtml(result, clientName);
            });

            $('#history-content').append(`
                <div class="tab-pane fade" id="${contentId}" role="tabpanel" aria-labelledby="${tabId}-tab">
                    <div class="results-container">
                        ${historyHtml}
                    </div>
                </div>
            `);

            // Update all history tab
            $('#history-all-container').html(`
                <div class="alert alert-success mb-3">
                    <i class="fas fa-check-circle me-2"></i>Monitoring queries have been run. Select a specific tab to view detailed results.
                </div>
                <div class="list-group">
                    <button class="list-group-item list-group-item-action" data-bs-toggle="tab" data-bs-target="#${contentId}" type="button">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">${queryName}</h5>
                            <small>${timestamp}</small>
                        </div>
                        <p class="mb-1">Click to view results</p>
                    </button>
                </div>
            `);
        }

        // Function to generate HTML for a result
        function generateResultHtml(result, clientName = null) {
            let html = '';

            // Add client name if provided
            if (clientName) {
                html += `<h5 class="mb-3">${clientName}</h5>`;
            }

            // Check if result has error
            if (result.error) {
                html += `
                    <div class="alert alert-danger mb-4">
                        <i class="fas fa-exclamation-circle me-2"></i>${result.error}
                    </div>
                `;
                return html;
            }

            // Check if result has data
            if (!result.headers || !result.rows || result.rows.length === 0) {
                html += `
                    <div class="alert alert-info mb-4">
                        <i class="fas fa-info-circle me-2"></i>Query executed successfully, but no data returned.
                    </div>
                `;
                return html;
            }

            // Add result table
            html += `
                <div class="table-responsive mb-4">
                    <table class="table table-striped table-bordered table-hover">
                        <thead class="table-primary">
                            <tr>
            `;

            // Add headers
            result.headers.forEach(header => {
                html += `<th>${header}</th>`;
            });

            html += `
                            </tr>
                        </thead>
                        <tbody>
            `;

            // Add rows
            result.rows.forEach(row => {
                html += '<tr>';
                result.headers.forEach(header => {
                    const value = row[header] !== null && row[header] !== undefined ? row[header] : '';
                    html += `<td>${value}</td>`;
                });
                html += '</tr>';
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            // Add row count
            html += `
                <div class="text-muted mb-4">
                    <i class="fas fa-info-circle me-1"></i>Showing ${result.rows.length} rows.
                    ${result.truncated ? ' (Results truncated)' : ''}
                </div>
            `;

            return html;
        }

        // Add monitoring query button
        $('#add-monitoring-query').click(function() {
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('addMonitoringModal'));
            modal.show();
        });

        // Save monitoring query button
        $('#save-monitoring-query').click(function() {
            const queryName = $('#monitoring-name').val().trim();
            const queryDescription = $('#monitoring-description').val().trim();
            const query = $('#monitoring-query').val().trim();

            if (!queryName) {
                alert('Please enter a name for the query.');
                return;
            }

            if (!query) {
                alert('Please enter a SQL query.');
                return;
            }

            // Save query
            $.post('/api/monitoring/save', {
                name: queryName,
                description: queryDescription,
                query: query
            }, function(data) {
                if (data.success) {
                    // Hide modal
                    bootstrap.Modal.getInstance(document.getElementById('addMonitoringModal')).hide();

                    // Show success message
                    alert('Monitoring query saved successfully.');

                    // Reset form
                    $('#monitoring-name').val('');
                    $('#monitoring-description').val('');
                    $('#monitoring-query').val('');

                    // Reload page to show new query
                    location.reload();
                } else {
                    alert('Failed to save monitoring query: ' + data.message);
                }
            }).fail(function() {
                alert('Failed to save monitoring query. Please try again.');
            });
        });

        // Right-click on monitoring query to view details
        $('.monitoring-query').on('contextmenu', function(e) {
            e.preventDefault();

            const queryName = $(this).data('query-name');

            // Get query details
            $.get('/api/monitoring/query', {
                name: queryName
            }, function(data) {
                if (data.success) {
                    // Show modal
                    $('#view-query-name').text(queryName);
                    $('#view-query-content').text(data.query);

                    const modal = new bootstrap.Modal(document.getElementById('viewQueryModal'));
                    modal.show();
                } else {
                    alert('Failed to get query details: ' + data.message);
                }
            }).fail(function() {
                alert('Failed to get query details. Please try again.');
            });

            return false;
        });

        // Edit query button
        $('#edit-query-btn').click(function() {
            const queryName = $('#view-query-name').text();
            const query = $('#view-query-content').text();

            // Hide view modal
            bootstrap.Modal.getInstance(document.getElementById('viewQueryModal')).hide();

            // Show edit modal
            $('#monitoring-name').val(queryName);
            $('#monitoring-query').val(query);

            const modal = new bootstrap.Modal(document.getElementById('addMonitoringModal'));
            modal.show();
        });
    });
</script>
{% endblock %}
