(['D:\\Gawean Rebinmas\\Monitoring '
  'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\run_server.py'],
 ['D:\\Gawean Rebinmas\\Monitoring '
  'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build'],
 [],
 [('C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('server_web\\common\\__init__.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__init__.py',
   'DATA'),
  ('server_web\\common\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\__init__.cpython-312.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\db_utils.cpython-310.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\db_utils.cpython-310.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\db_utils.cpython-312.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\db_utils.cpython-312.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\db_utils.cpython-313.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\db_utils.cpython-313.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\network.cpython-310.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\network.cpython-310.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\network.cpython-312.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\network.cpython-312.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\network.cpython-313.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\network.cpython-313.pyc',
   'DATA'),
  ('server_web\\common\\db_utils.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\db_utils.py',
   'DATA'),
  ('server_web\\common\\network.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\network.py',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014500.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014500.fdb',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014600.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014600.fdb',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014700.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014700.fdb',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_015547.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_015547.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\PTRJ_AB1_20250423_102329.FDB',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\PTRJ_AB1_20250423_102329.FDB',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb.backup_1745388300',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb.backup_1745388300',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_110113.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_110113.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_115319.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_115319.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_131517.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_131517.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\test.txt',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\test.txt',
   'DATA'),
  ('server_web\\static\\backups\\test_client\\test_db_20250419_020051.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_client\\test_db_20250419_020051.fdb',
   'DATA'),
  ('server_web\\static\\backups\\test_client\\test_db_20250419_020554.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_client\\test_db_20250419_020554.fdb',
   'DATA'),
  ('server_web\\static\\backups\\test_client\\test_db_20250419_020828.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_client\\test_db_20250419_020828.fdb',
   'DATA'),
  ('server_web\\static\\backups\\test_db.txt',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_db.txt',
   'DATA'),
  ('server_web\\static\\css\\style.css',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\css\\style.css',
   'DATA'),
  ('server_web\\static\\js\\main.js',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\js\\main.js',
   'DATA'),
  ('server_web\\static\\js\\robust_transfer.js',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\js\\robust_transfer.js',
   'DATA'),
  ('server_web\\static\\saved_queries.db',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\saved_queries.db',
   'DATA'),
  ('server_web\\static\\sql_queries\\Employee_Overtime.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\Employee_Overtime.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\Find_Diff_Job_Date_Overtime.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\Find_Diff_Job_Date_Overtime.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\Find_Helper_Hasnt_Veh_No.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\Find_Helper_Hasnt_Veh_No.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\General_Work_Has_YY.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\General_Work_Has_YY.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\test_dynamic_variables.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\test_dynamic_variables.sql',
   'DATA'),
  ('server_web\\templates\\backups.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\backups.html',
   'DATA'),
  ('server_web\\templates\\base.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\base.html',
   'DATA'),
  ('server_web\\templates\\clients.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\clients.html',
   'DATA'),
  ('server_web\\templates\\index.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\index.html',
   'DATA'),
  ('server_web\\templates\\layout.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\layout.html',
   'DATA'),
  ('server_web\\templates\\monitoring.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\monitoring.html',
   'DATA'),
  ('server_web\\templates\\monitoring_simple.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\monitoring_simple.html',
   'DATA'),
  ('server_web\\templates\\query.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\query.html',
   'DATA'),
  ('server_web\\templates\\query_history.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\query_history.html',
   'DATA'),
  ('server_web\\templates\\standalone_monitoring.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\standalone_monitoring.html',
   'DATA'),
  ('server_web\\templates\\test_query.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\test_query.html',
   'DATA')],
 '3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('run_server',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\run_server.py',
   'PYSOURCE')],
 [('zipfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\pathlib\\__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\pathlib\\_local.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('glob',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\glob.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('pathlib._abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\pathlib\\_abc.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\contextlib.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\argparse.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\textwrap.py',
   'PYMODULE'),
  ('copy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\copy.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\gettext.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('csv',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\csv.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\string.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('random',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\bisect.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('socket',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\selectors.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\calendar.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_strptime.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\quopri.py',
   'PYMODULE'),
  ('typing',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\tempfile.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('email',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('json',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('__future__',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\__future__.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\tokenize.py',
   'PYMODULE'),
  ('token',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\token.py',
   'PYMODULE'),
  ('lzma',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\lzma.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\bz2.py',
   'PYMODULE'),
  ('threading',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('struct',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\struct.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\gzip.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\inspect.py',
   'PYMODULE'),
  ('dis',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\dis.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\opcode.py',
   'PYMODULE'),
  ('_opcode_metadata',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_opcode_metadata.py',
   'PYMODULE'),
  ('ast',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ast.py',
   'PYMODULE'),
  ('_colorize',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_colorize.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\stringprep.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('server_web', '-', 'PYMODULE'),
  ('webbrowser',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes.util',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('_ios_support',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\_ios_support.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\signal.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\shlex.py',
   'PYMODULE')],
 [('python313.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY')],
 [],
 [],
 [('server_web\\common\\__init__.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__init__.py',
   'DATA'),
  ('server_web\\common\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\__init__.cpython-312.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\db_utils.cpython-310.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\db_utils.cpython-310.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\db_utils.cpython-312.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\db_utils.cpython-312.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\db_utils.cpython-313.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\db_utils.cpython-313.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\network.cpython-310.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\network.cpython-310.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\network.cpython-312.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\network.cpython-312.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\network.cpython-313.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\network.cpython-313.pyc',
   'DATA'),
  ('server_web\\common\\db_utils.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\db_utils.py',
   'DATA'),
  ('server_web\\common\\network.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\network.py',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014500.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014500.fdb',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014600.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014600.fdb',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014700.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014700.fdb',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_015547.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_015547.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\PTRJ_AB1_20250423_102329.FDB',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\PTRJ_AB1_20250423_102329.FDB',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb.backup_1745388300',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb.backup_1745388300',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_110113.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_110113.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_115319.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_115319.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_131517.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_131517.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\test.txt',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\test.txt',
   'DATA'),
  ('server_web\\static\\backups\\test_client\\test_db_20250419_020051.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_client\\test_db_20250419_020051.fdb',
   'DATA'),
  ('server_web\\static\\backups\\test_client\\test_db_20250419_020554.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_client\\test_db_20250419_020554.fdb',
   'DATA'),
  ('server_web\\static\\backups\\test_client\\test_db_20250419_020828.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_client\\test_db_20250419_020828.fdb',
   'DATA'),
  ('server_web\\static\\backups\\test_db.txt',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_db.txt',
   'DATA'),
  ('server_web\\static\\css\\style.css',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\css\\style.css',
   'DATA'),
  ('server_web\\static\\js\\main.js',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\js\\main.js',
   'DATA'),
  ('server_web\\static\\js\\robust_transfer.js',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\js\\robust_transfer.js',
   'DATA'),
  ('server_web\\static\\saved_queries.db',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\saved_queries.db',
   'DATA'),
  ('server_web\\static\\sql_queries\\Employee_Overtime.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\Employee_Overtime.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\Find_Diff_Job_Date_Overtime.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\Find_Diff_Job_Date_Overtime.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\Find_Helper_Hasnt_Veh_No.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\Find_Helper_Hasnt_Veh_No.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\General_Work_Has_YY.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\General_Work_Has_YY.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\test_dynamic_variables.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\test_dynamic_variables.sql',
   'DATA'),
  ('server_web\\templates\\backups.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\backups.html',
   'DATA'),
  ('server_web\\templates\\base.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\base.html',
   'DATA'),
  ('server_web\\templates\\clients.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\clients.html',
   'DATA'),
  ('server_web\\templates\\index.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\index.html',
   'DATA'),
  ('server_web\\templates\\layout.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\layout.html',
   'DATA'),
  ('server_web\\templates\\monitoring.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\monitoring.html',
   'DATA'),
  ('server_web\\templates\\monitoring_simple.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\monitoring_simple.html',
   'DATA'),
  ('server_web\\templates\\query.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\query.html',
   'DATA'),
  ('server_web\\templates\\query_history.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\query_history.html',
   'DATA'),
  ('server_web\\templates\\standalone_monitoring.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\standalone_monitoring.html',
   'DATA'),
  ('server_web\\templates\\test_query.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\test_query.html',
   'DATA'),
  ('base_library.zip',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\IFESS_Server_Web\\base_library.zip',
   'DATA')])
