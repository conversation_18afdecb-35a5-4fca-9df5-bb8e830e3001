#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for Google Drive shared folder upload
This script tests uploading a file to a shared folder in Google Drive
"""

import os
import sys
import logging
import argparse
from gdrive_client_module import GDriveClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_gdrive_shared_folder')

def test_upload_to_shared_folder(file_path, client_id, credentials_file=None):
    """Test uploading a file to a shared folder in Google Drive"""
    logger.info(f"Testing upload to shared folder with file: {file_path}")
    logger.info(f"Client ID: {client_id}")
    logger.info(f"Credentials file: {credentials_file}")
    
    # Create GDrive client
    client = GDriveClient(client_id, credentials_file)
    
    # Define progress callback
    def progress_callback(progress):
        logger.info(f"Upload progress: {progress}%")
    
    # Upload file
    result = client.upload_file(file_path, progress_callback)
    
    # Print result
    logger.info(f"Upload result: {result}")
    
    return result

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Test Google Drive shared folder upload")
    parser.add_argument("file_path", help="Path to file to upload")
    parser.add_argument("--client-id", help="Client ID for folder naming", default="test_client")
    parser.add_argument("--credentials", help="Path to credentials.json file")
    
    args = parser.parse_args()
    
    test_upload_to_shared_folder(args.file_path, args.client_id, args.credentials)
