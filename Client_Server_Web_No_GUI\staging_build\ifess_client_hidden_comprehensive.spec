# -*- mode: python ; coding: utf-8 -*-
"""
IFESS Hidden Client - Comprehensive Build Specification
Builds a standalone executable with all dependencies bundled internally
"""

import os
import sys
from pathlib import Path

# Get the current directory (where this spec file is located)
CURRENT_DIR = os.path.dirname(os.path.abspath(SPEC))
PROJECT_ROOT = Path(CURRENT_DIR)

# Define all data files and external dependencies that need to be bundled
data_files = [
    # Configuration files
    (str(PROJECT_ROOT / 'client_config.json'), '.'),
    (str(PROJECT_ROOT / 'client_config_oauth_tokens.json'), '.'),
    (str(PROJECT_ROOT / 'token.json'), '.'),
    (str(PROJECT_ROOT / 'token_backup.json'), '.'),
    
    # Google Drive client modules and dependencies
    (str(PROJECT_ROOT / 'gdrive_client_oauth_simple.py'), '.'),
    (str(PROJECT_ROOT / 'gdrive_client_module.py'), '.'),
    (str(PROJECT_ROOT / 'gdrive_client_simple.py'), '.'),
    (str(PROJECT_ROOT / 'gdrive_client_oauth.py'), '.'),
    
    # Service account credentials
    (str(PROJECT_ROOT / 'ptrj-backup-services-account.json'), '.'),
    (str(PROJECT_ROOT / 'client_secret.json'), '.'),
    (str(PROJECT_ROOT / 'client_secrets.json'), '.'),
    
    # Common modules directory
    (str(PROJECT_ROOT / 'common'), 'common'),
    
    # Additional utilities if they exist
]

# Filter data files to only include existing files
filtered_data_files = []
for src, dst in data_files:
    if os.path.exists(src):
        filtered_data_files.append((src, dst))
        print(f"Including: {src} -> {dst}")
    else:
        print(f"Warning: File not found, skipping: {src}")

# Hidden imports for all potential dependencies
hidden_imports = [
    # Core networking and database
    'socket', 'json', 'ssl', 'urllib3', 'requests',
    'sqlite3', 'threading', 'queue', 'time', 'datetime',
    'os', 'sys', 'logging', 'traceback', 'uuid', 'platform',
    'ctypes', 'ctypes.wintypes',
    
    # Firebird database
    'fdb', 'firebirdsql',
    
    # Google APIs
    'googleapiclient', 'googleapiclient.discovery', 'googleapiclient.errors',
    'google.oauth2', 'google.oauth2.service_account', 'google.oauth2.credentials',
    'google.auth', 'google.auth.transport', 'google.auth.transport.requests',
    'google_auth_oauthlib', 'google_auth_oauthlib.flow',
    
    # MEGA (if used)
    'mega', 'mega.mega',
    
    # Network and web
    'http.client', 'http.server', 'urllib.parse', 'urllib.request',
    
    # Compression and file handling
    'zipfile', 'gzip', 'shutil', 'tempfile', 'glob',
    
    # Math and random for connection management
    'math', 'random', 'schedule',
    
    # Windows-specific
    'winreg', 'msvcrt',
    
    # Additional common modules
    'pickle', 'base64', 'hashlib', 'hmac',
]

a = Analysis(
    [str(PROJECT_ROOT / 'ifess_client_hidden.py')],
    pathex=[str(PROJECT_ROOT)],
    binaries=[],
    datas=filtered_data_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # Exclude unnecessary modules to reduce size
        'tkinter', 'matplotlib', 'numpy', 'scipy', 'pandas',
        'IPython', 'jupyter', 'notebook',
    ],
    noarchive=False,
    optimize=0,
)

# Remove duplicate entries
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ifess_client_hidden',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # Enable UPX compression to reduce file size
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Hidden client runs without console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(PROJECT_ROOT / 'MAINICON.ico') if (PROJECT_ROOT / 'MAINICON.ico').exists() else None,
) 