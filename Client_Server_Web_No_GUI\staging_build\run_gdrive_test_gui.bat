@echo off
echo Starting IFESS Google Drive Test GUI

REM Check if Python is available
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Please install Python.
    pause
    exit /b 1
)

REM Install required dependencies
echo Installing required dependencies...
python -m pip install mega.py google-auth google-auth-oauthlib google-api-python-client requests python-dateutil
if %errorlevel% neq 0 (
    echo Failed to install some dependencies, but continuing anyway.
    echo Some features may not work properly.
)

REM Download token from Google Drive
echo Downloading token from Google Drive...
python token_downloader.py
if %errorlevel% neq 0 (
    echo Warning: Failed to download token from Google Drive.
    echo Will try to use existing token or fall back to OAuth flow.
    echo.
)

REM Check if client_secret.json exists
if exist "client_secret.json" (
    echo Found client_secret.json
    echo Copying to client_secrets.json for compatibility...
    copy "client_secret.json" "client_secrets.json" /Y > nul
) else if exist "client_secrets.json" (
    echo Found client_secrets.json
) else (
    echo Warning: No client_secret.json or client_secrets.json found.
    echo Google Drive uploads may not work properly.
    echo.
)

REM Start the GUI
echo Starting Google Drive Test GUI...
python ifess_gdrive_test_gui.py

echo.
echo Press any key to exit...
pause > nul
