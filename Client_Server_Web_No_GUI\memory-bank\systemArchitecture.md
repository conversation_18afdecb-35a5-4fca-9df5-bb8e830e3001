# System Architecture

**Last Updated**: 2025-01-27
**Architecture Version**: 2.1 - Post Google Drive Integration

## Architecture Overview

### Component Structure
```
IFESS Client System
├── ifess_client_hidden.py (Main Service) ✅ ENHANCED
│   ├── ClientApp (Core Application Class)
│   ├── NetworkMessage Protocol
│   ├── FirebirdConnector (Database Interface)
│   ├── MegaClient (Cloud Storage) ✅ COMPLETED
│   ├── GDriveClient (Google Drive Integration) ✅ COMPLETED
│   ├── ConnectionManager (Resilience Framework) ✅ READY
│   └── ConnectionStats (Metrics Tracking) ✅ READY
├── ifess_client_gui.py (GUI Interface) ⚠️ NEEDS SYNC
│   ├── GUI Application Framework
│   ├── Basic Network Communication
│   └── Limited Cloud Integration
├── ifess_client_debug.py (Debug Interface) ⚠️ NEEDS SYNC
│   ├── DebugApp (GUI Application)
│   ├── LogMonitor (Real-time Log Viewer)
│   ├── StatusTracker (Connection Monitoring)
│   └── Limited Feature Set
├── client_config.json (Standard Configuration)
├── client_config_oauth_tokens.json (OAuth Configuration) ✅ NEW
└── Common Modules
    ├── network.py (Message Protocol)
    ├── gdrive_client_oauth_simple.py ✅ ENHANCED
    └── mega_client_*.py (Multiple Variants)
```

### Key Design Patterns

#### 1. Service-Observer Pattern
- **Hidden Client**: Acts as the main service, running continuously
- **GUI Client**: Provides user interface for configuration and monitoring
- **Debug Client**: Observes and displays system state for troubleshooting
- **Decoupled Design**: Each component can operate independently

#### 2. Enhanced Connection Management Pattern ✅ IMPLEMENTED
```python
class ConnectionManager:
    def __init__(self):
        self.base_interval = 5  # seconds
        self.max_interval = 300  # 5 minutes
        self.current_attempt = 0
        self.jitter_range = 0.2  # ±20%
    
    def get_next_interval(self):
        # Exponential backoff: 5, 10, 20, 40, 80, 160, 300
        interval = min(self.base_interval * (2 ** self.current_attempt), self.max_interval)
        # Add jitter: ±20%
        jitter = interval * self.jitter_range * (random.random() * 2 - 1)
        return max(1, interval + jitter)
```

#### 3. Connection Statistics Tracking ✅ IMPLEMENTED
```python
class ConnectionStats:
    def __init__(self):
        self.connection_start_time = None
        self.total_uptime = 0
        self.reconnection_count = 0
        self.last_disconnect_time = None
        self.disconnect_reasons = []
        self.current_backoff_interval = 0
        
    def track_connection_established(self):
        self.connection_start_time = time.time()
        self.reconnection_count += 1
```

#### 4. Google Drive Integration Pattern ✅ COMPLETED
```python
# Message Types Added
NetworkMessage.TYPE_GDRIVE_UPLOAD_REQUEST = 'gdrive_upload_request'
NetworkMessage.TYPE_GDRIVE_UPLOAD_ACK = 'gdrive_upload_ack'
NetworkMessage.TYPE_GDRIVE_UPLOAD_PROGRESS = 'gdrive_upload_progress'
NetworkMessage.TYPE_GDRIVE_UPLOAD_RESULT = 'gdrive_upload_result'

# Authentication Flow
token.json → OAuth credentials → GDrive client → Folder structure creation
```

## Component Relationships

### Data Flow Architecture
```mermaid
graph TD
    A[Server] -->|Upload Request| B[Hidden Client]
    B -->|Process Request| C[GDrive Client]
    C -->|Upload Progress| B
    B -->|Progress Updates| A
    A -->|Status Display| D[Web Interface]
    
    B -->|Debug Data| E[Debug Client]
    B -->|Status Updates| F[GUI Client]
    
    G[Config Files] -->|Settings| B
    G -->|Settings| E
    G -->|Settings| F
```

### Thread Architecture
```
Main Thread
├── GUI Thread (GUI/Debug Clients only)
├── Auto-Reconnect Thread ⚠️ NEEDS ENHANCEMENT
├── Message Receive Thread
├── Config Watcher Thread
├── Token Status Check Thread (Background)
├── Upload Progress Threads (As needed) ✅ IMPLEMENTED
└── Debug Streaming Thread (Planned)
```

### Message Protocol Enhancement ✅ COMPLETED
```python
# Enhanced Message Types
class NetworkMessage:
    # Existing types
    TYPE_REGISTER = "register"
    TYPE_QUERY = "query"
    TYPE_PING = "ping"
    TYPE_PONG = "pong"
    TYPE_DB_FILE_REQUEST = "db_file_request"
    TYPE_MEGA_UPLOAD = "mega_upload"
    
    # New Google Drive types ✅ ADDED
    TYPE_GDRIVE_UPLOAD_REQUEST = 'gdrive_upload_request'
    TYPE_GDRIVE_UPLOAD_ACK = 'gdrive_upload_ack'
    TYPE_GDRIVE_UPLOAD_PROGRESS = 'gdrive_upload_progress'
    TYPE_GDRIVE_UPLOAD_RESULT = 'gdrive_upload_result'
```

## Technical Decisions and Rationale

### Authentication Strategy ✅ IMPLEMENTED
- **Decision**: Use existing token.json file for Google Drive authentication
- **Rationale**: File already contains valid OAuth credentials with refresh_token
- **Implementation**: Updated GDrive client with `_load_credentials_from_token()` method
- **Folder Structure**: `Backup_PTRJ/Auto_Backup_App/Ifess_Database/{client_id}`

### Component Synchronization Strategy 🔄 IN PROGRESS
- **Decision**: Implement shared module approach for common functionality
- **Rationale**: Reduces code duplication and ensures consistency across components
- **Implementation Plan**: Extract common methods to shared modules
- **Priority**: High - Required for feature parity

### Debug Streaming Architecture 🔄 PLANNED
- **Decision**: Use inter-process communication for debug data sharing
- **Rationale**: Allows real-time debug information flow between processes
- **Options Considered**: Named pipes, TCP sockets, shared memory, file-based
- **Recommended**: Named pipes for Windows-native efficiency

### Connection Resilience Integration ⏳ PENDING
- **Current State**: ConnectionManager and ConnectionStats classes implemented
- **Integration Point**: Replace simple retry logic in `auto_reconnect_loop()`
- **Benefits**: Prevents connection storms, improves server stability
- **Timeline**: After component synchronization completion

## Architecture Gaps and Improvements

### Current Gaps
1. **Component Feature Inconsistency**: Google Drive integration only in hidden client
2. **Debug Information Isolation**: No real-time debug streaming between processes
3. **Simple Connection Logic**: Fixed interval reconnection instead of exponential backoff
4. **Code Duplication**: Similar functionality implemented separately in each component

### Planned Improvements
1. **Shared Module Architecture**: Extract common functionality for reuse
2. **Debug Streaming System**: Real-time debug data sharing via IPC
3. **Enhanced Connection Management**: Integrate exponential backoff with jitter
4. **Component Synchronization**: Ensure feature parity across all clients

## Security Architecture

### Current Security Model
- **Authentication**: Basic client registration without validation
- **Credential Storage**: Plain text in configuration files
- **Network Security**: TCP connections without encryption
- **Access Control**: File system permissions only

### OAuth Integration ✅ IMPLEMENTED
- **Google Drive**: Token-based authentication with refresh capability
- **Credential Management**: Secure token storage in token.json
- **Permission Scope**: Limited to necessary Google Drive operations
- **Token Refresh**: Automatic refresh handling in GDrive client

## Performance Architecture

### Current Performance Characteristics
- **Message Processing**: Single-threaded with dedicated upload threads
- **Database Operations**: Synchronous operations with connection reuse
- **File Transfers**: Chunked transfers for large database files
- **Memory Management**: Rotating log files (5MB, 3 backups)

### Optimization Opportunities
1. **Connection Pooling**: Database connection management
2. **Async Operations**: Consider asyncio for network operations
3. **Message Queuing**: Asynchronous message processing
4. **Caching**: Configuration and status caching

## Deployment Architecture

### Current Deployment
- **Platform**: Windows 10+ environment
- **Execution**: Python scripts with PyInstaller executables
- **Configuration**: JSON-based configuration files
- **Logging**: File-based with rotation

### Service Architecture
- **Hidden Client**: Runs as background service/daemon
- **GUI Client**: On-demand user interface
- **Debug Client**: Troubleshooting and monitoring tool
- **Batch Scripts**: Automated startup and management

## Cross-References
- **Current Implementation Status**: See `progressTracker.md` for detailed progress
- **Active Development**: See `currentFocus.md` for current work focus
- **Technical Constraints**: See `techStack.md` for implementation details
- **Project Objectives**: See `projectOverview.md` for overall goals
