2025-04-28 20:41:37,342 - oauth_token_generator - INFO - === OAUTH TOKEN GENERATOR ===
2025-04-28 20:41:37,343 - oauth_token_generator - INFO - Using client secret file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secret.json
2025-04-28 20:41:37,343 - oauth_token_generator - INFO - Starting OAuth token generator loop...
2025-04-28 20:41:37,343 - oauth_token_generator - INFO - Checking if token needs to be refreshed...
2025-04-28 20:41:37,343 - oauth_token_generator - INFO - Token file not found, generating new token...
2025-04-28 20:41:37,344 - oauth_token_generator - INFO - Generating a new OAuth token...
2025-04-28 20:41:37,344 - oauth_token_generator - INFO - Using client secret file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secret.json
2025-04-28 20:41:37,358 - oauth_token_generator - INFO - Client secret loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secret.json
2025-04-28 20:41:37,358 - oauth_token_generator - INFO - Authorization URL generated: https://accounts.google.com/o/oauth2/auth?client_id=************-a09bgba56h1i7iegfneh1d9dr2t28833.apps.googleusercontent.com&redirect_uri=http://localhost&scope=https://www.googleapis.com/auth/drive.file&response_type=code&access_type=offline&prompt=consent
2025-04-28 20:41:37,358 - oauth_token_generator - INFO - Opening browser for authorization...
2025-04-28 20:44:13,080 - oauth_token_generator - INFO - Token generator loop stopped by user
