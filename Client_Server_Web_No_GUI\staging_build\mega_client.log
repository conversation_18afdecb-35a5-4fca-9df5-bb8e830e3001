2025-04-24 11:33:01,684 - mega_client_py310 - INFO - Internet connection verified via www.google.com
2025-04-24 11:33:01,825 - mega_client_py310 - INFO - Successfully imported MEGA library
2025-04-24 11:33:01,825 - mega_client_py310 - INFO - Connecting to MEGA...
2025-04-24 11:33:01,825 - mega_client_py310 - INFO - Logging in user...
2025-04-24 11:33:01,825 - mega_client_py310 - INFO - Login attempt 1/3
2025-04-24 11:33:01,825 - mega.mega - INFO - Logging in user...
2025-04-24 11:33:03,277 - mega.mega - INFO - Getting all files...
2025-04-24 11:33:04,009 - mega.mega - INFO - Login complete
2025-04-24 11:33:04,009 - mega_client_py310 - INFO - Successfully logged in to <NAME_EMAIL>
2025-04-24 11:33:04,627 - mega_client_py310 - INFO - Logged in as: {'u': 'vlj9OThSMt0', 's': 0, 'since': 1745399575, 'na': 1, 'email': '<EMAIL>', 'emails': ['<EMAIL>'], 'pemails': [], 'name': 'Ifess PTRJ Backup', 'k': 'fKt0pygAcjO6haOM1KDgtQ', 'c': 1, 'pubk': 'CACNbTRDqc_1z9m9b1ATarJioZW6N8KDDW-oVctrnerImHrPtWqgWYluwricyL2GalGEMGWHA1IBH8JO8vG4t2cwpSpP8Yf1cb3FVFdXkgU132JdTwynL9OBIkxk45Wv2tHyGOln8qaX9w62PU09QrvSWlvxr0FMiqVSXP18zpHX841UbYQ0tCqg-_wnp4JoVH3HLJo8AHCT4RydQkGb3bTDOyjsz1Bhoe6OXXOcrvgXPj39HgT_GFc4j4cYDa2xHfxyWdckpESKCHSzp_RrQcUaMeky9U_udVX4ZpkSKkqyNik2B6_aoKSK8dlGsxlijBb6WErAydmwJxiUasRR1HkZACAAAAEB', 'privk': 'WwgEXtv4wfe0YbHgaYL2aTaKpHArpXw2TEwuAj-WJhSGFm0pedyneqNxN44YBYw_GPC-BUfzxV_hoJzszPWASO8prabpuviS4z-Gqa-seSSuXa0OcmbZU8MBEuC8TXTUXMK0ImzlIoNT1MR88YXmTVmq4UqLcZKY5GGtSi12kFpIhz8NGLh-zDZ2gD_aNVPYfftmBxFD5mJcxVhOl73UgXW-sWto3fsZpFHZr6uBRgiYdtTZgxrvqvDjm34k_UsjZt3AC4TXlPj6mOqESd8naeXQINckaS28K2FNDSvrTK_3abhXwOh4jLYIlt16f7R3DsXQSiKwhv1XR0_TuDQCIOWbU0Ke2m2MXS_fAp62VJhlTbcpapfKUuDak6Xt0fnxBGzjutWuueX2GsYh0Gpjd6V3BVKDjrTSP3SCenbsvT7iNzX_LHKnYQKcLFWkEZJO57DclutoDGdS-1of-vhm3piTR3EutnWVg5r4PElrIO0-ES3fLLGcO48dkdEaTUY3XZ-UaRkBUoefZKMYC2grAbq4Cmd_NVgnjUPZQDBEq6qR7gpBq8hrCv9lAQ_6oFbwBk6j9NQJCWSBLHFRR5JrTEtZOdTqZ6ahCBxDu0T6rvJXdiwNQg31aY57xE52BfOrouyNoPq3KLwpqAvUnRJU7Gow1VLMik-7yynI8fGzl61OiC9oCtoJqu4zK2AKysb1dCfjJhmpTF2ueicBmRx4yopI7kJwh4M1tB-W13U4vhEmkEOcHwbEfhHYDpEGcT5YGFKDYfhuVIZOhnq9OGFRvbi8ENGe6nM0baUONxACmwP990gY7ZGq_4rLM1Tc6tlZCOwWqfTTE6NpCI_vqAKd6Lwup00g50aMqfdzjeHHC-Y', '^!fudnxt': 'MTc0NjIwNTIwMDAwMA', '^!keys': 'FABvuL1r4bzuXgNozonDd8FuJZZBJYJm8aXI7Wom7_lXE-FSEAVMb5pWC6uSp7ViA9xjlCRA3Ii-Ps9lHNW83ZJ7-0_7JA3azjcnKH_RK9AhyCLAupw84_7gFSaz-fdoI6LU27cuJLv0g4MBz_7-5SrZdoBC71fg9yueCZmPSw8EZn43E6FIfi5O2zuQIatPNJRlIj1a9ZVVImQggcaxoEzsBuP5blk06_jF3SBs-b--bVIGkzjBOMmdmLCRnGSM9wcldao_kjc9_4X_76lk0ZQtKXWn1Rchv1SRF18scfxEIQkSb2e6hLJqrywKt-yZk1rQBk71uCkYPhjDXtdeSKqPZlPULxyN3yUOYTRNxVouOZVNfJqjuvHuDErRyQZ2g9rqQc4SH_xPwbBsXjrymSFTp6fNKSxPseNLppj_zPC650a08rbE5wAHsl0zhjkmnrQjFYhRdH7C747pQqYXeT11QZVzOdPBpEgjiCf8Khd_b9EbTfWh1H8I5QSVwKNUV0YVb9TlM-3z55-UfZfJGqRlHj1SXzo2dg_bWsRM1PlBuwAppw5X-xUaz4Uxr8uPAbX6s4TOrD0f0Q1FfH9vbg2UcWQU7c544KX3lISHBkDvSZBlRnNBn2k9F8KJjZO67YRPxOGh21WyjT-B1W9Ou_Cx-oCF4DUSXqqi2fqF4eWiGmLwJTv78FVwtGVdCoijGq4AHUlRnw4j7r6PiDlwRZNknZresAIUwpbFjPYwfyCqXBbx7X2hDc0IDWdUlJnfOwBiVaMAST4QrB22LkgqaNuUaSDeLQ1yJt43IbwPa8SNbQuy8M47-0-l56Xakt4acU_D3QtsBjaFa3xDumUX4qT2hiQ2vvDlNkrLA919wqEt6hmBVGL-LSVAy33xnFWGct2F1MBQcdw_t-7K4hTG_-o', '^!lang': 'ZW4', '^!obv4': 'AAEBAQEAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAA', '^!prd': 'MDowOjE6MDow', '^!stbmp': 'WVc0YmNXNTY4cTA', '*keyring': 'EMJZq0HpwoWfezH-CLej0M9iArM7QvqNgw7SlfzHZUV57CCl2igJ_vUtADo6bO573x2ytugShWUEYsl5E8JlaTrMIdYDILOTtNJQp9EAU2rrTjGq5F8mZH_zwqnlQUIE2WrnW1BDinyBQW3adXQB3CM', '+puCu255': 'ZmVHJs1OTJLB2zbyVUwbXSrS7QMFK3F8p_QXfIpc7kQ', '+puEd255': 'XihFJ2lVeppnMLChMC6WG6f4d2fzRhzAi3-NBfYekGs', '+sigCu255': 'AAAAAGgIrzMIin9UmwV3KVg2JaLfoZKdU-Na0ABaQHSQvcf2XTc1W8zjBql0u13bJkcLa8-0blzj_3gFDlErs2O9RhV1D9EF', '+sigPubk': 'AAAAAGgIrzPaEVm0Y7NKyrsYZNvlaVhZvoptB5rDyCjkA85CgaAVn7YijQHN9hJTJYPd7WwAkcuhGptbeiPqS3AKApFqqSID', 'firstname': 'SWZlc3MgUFRSSg', 'lastname': 'QmFja3Vw', 'name2': 'SWZlc3MgUFRSSiBCYWNrdXA', 'terms': 'Mg', 'ts': 'uN7Fm9cZp0bzHUCVOxOnb7N3EO1GkQi4ncJQ6nSvymU', 'features': [], 'flags': {'ach': 1, 'bstrg': 21474836480, 'mcs': 1, 'mfae': 1, 'nsre': 1, 'nlfe': 1, 'cspe': 1, 'smsve': 1, 'refpr': 1, 'ff_chmon': 1, 'ssrs': 1, 'aplvp': 1, 'nobp': 1, 'rw': 1, 'ab_fmpup': 1, 'ff_vpnft': 1, 'ff_npp': 1, 'ff_adse': 1, 'ff_npabm': 1, 'ff_refsun': 2, 'ff_iguhm': 1, 'pf': 1}, 'aav': 2, 'ipcc': 'ID', 'aas': 'r4CLpGpSNFMfF44FfOBFQxJfAHpjF-XXD0RAoafRrfI', 'ut': '3amjOoCRrUk'}
2025-04-24 11:33:05,930 - mega_client_py310 - WARNING - Could not get storage details: unsupported operand type(s) for /: 'dict' and 'float'
2025-04-24 11:33:31,936 - mega_client_py310 - INFO - Internet connection verified via www.google.com
2025-04-24 11:33:32,075 - mega_client_py310 - INFO - Successfully imported MEGA library
2025-04-24 11:33:32,076 - mega_client_py310 - INFO - Connecting to MEGA...
2025-04-24 11:33:32,076 - mega_client_py310 - INFO - Logging in user...
2025-04-24 11:33:32,076 - mega_client_py310 - INFO - Login attempt 1/3
2025-04-24 11:33:32,076 - mega.mega - INFO - Logging in user...
2025-04-24 11:33:33,427 - mega.mega - INFO - Getting all files...
2025-04-24 11:33:34,107 - mega.mega - INFO - Login complete
2025-04-24 11:33:34,107 - mega_client_py310 - INFO - Successfully logged in to <NAME_EMAIL>
2025-04-24 11:33:34,727 - mega_client_py310 - INFO - Logged in as: {'u': 'vlj9OThSMt0', 's': 0, 'since': 1745399575, 'na': 1, 'email': '<EMAIL>', 'emails': ['<EMAIL>'], 'pemails': [], 'name': 'Ifess PTRJ Backup', 'k': 'fKt0pygAcjO6haOM1KDgtQ', 'c': 1, 'pubk': 'CACNbTRDqc_1z9m9b1ATarJioZW6N8KDDW-oVctrnerImHrPtWqgWYluwricyL2GalGEMGWHA1IBH8JO8vG4t2cwpSpP8Yf1cb3FVFdXkgU132JdTwynL9OBIkxk45Wv2tHyGOln8qaX9w62PU09QrvSWlvxr0FMiqVSXP18zpHX841UbYQ0tCqg-_wnp4JoVH3HLJo8AHCT4RydQkGb3bTDOyjsz1Bhoe6OXXOcrvgXPj39HgT_GFc4j4cYDa2xHfxyWdckpESKCHSzp_RrQcUaMeky9U_udVX4ZpkSKkqyNik2B6_aoKSK8dlGsxlijBb6WErAydmwJxiUasRR1HkZACAAAAEB', 'privk': 'WwgEXtv4wfe0YbHgaYL2aTaKpHArpXw2TEwuAj-WJhSGFm0pedyneqNxN44YBYw_GPC-BUfzxV_hoJzszPWASO8prabpuviS4z-Gqa-seSSuXa0OcmbZU8MBEuC8TXTUXMK0ImzlIoNT1MR88YXmTVmq4UqLcZKY5GGtSi12kFpIhz8NGLh-zDZ2gD_aNVPYfftmBxFD5mJcxVhOl73UgXW-sWto3fsZpFHZr6uBRgiYdtTZgxrvqvDjm34k_UsjZt3AC4TXlPj6mOqESd8naeXQINckaS28K2FNDSvrTK_3abhXwOh4jLYIlt16f7R3DsXQSiKwhv1XR0_TuDQCIOWbU0Ke2m2MXS_fAp62VJhlTbcpapfKUuDak6Xt0fnxBGzjutWuueX2GsYh0Gpjd6V3BVKDjrTSP3SCenbsvT7iNzX_LHKnYQKcLFWkEZJO57DclutoDGdS-1of-vhm3piTR3EutnWVg5r4PElrIO0-ES3fLLGcO48dkdEaTUY3XZ-UaRkBUoefZKMYC2grAbq4Cmd_NVgnjUPZQDBEq6qR7gpBq8hrCv9lAQ_6oFbwBk6j9NQJCWSBLHFRR5JrTEtZOdTqZ6ahCBxDu0T6rvJXdiwNQg31aY57xE52BfOrouyNoPq3KLwpqAvUnRJU7Gow1VLMik-7yynI8fGzl61OiC9oCtoJqu4zK2AKysb1dCfjJhmpTF2ueicBmRx4yopI7kJwh4M1tB-W13U4vhEmkEOcHwbEfhHYDpEGcT5YGFKDYfhuVIZOhnq9OGFRvbi8ENGe6nM0baUONxACmwP990gY7ZGq_4rLM1Tc6tlZCOwWqfTTE6NpCI_vqAKd6Lwup00g50aMqfdzjeHHC-Y', '^!fudnxt': 'MTc0NjIwNTIwMDAwMA', '^!keys': 'FABvuL1r4bzuXgNozonDd8FuJZZBJYJm8aXI7Wom7_lXE-FSEAVMb5pWC6uSp7ViA9xjlCRA3Ii-Ps9lHNW83ZJ7-0_7JA3azjcnKH_RK9AhyCLAupw84_7gFSaz-fdoI6LU27cuJLv0g4MBz_7-5SrZdoBC71fg9yueCZmPSw8EZn43E6FIfi5O2zuQIatPNJRlIj1a9ZVVImQggcaxoEzsBuP5blk06_jF3SBs-b--bVIGkzjBOMmdmLCRnGSM9wcldao_kjc9_4X_76lk0ZQtKXWn1Rchv1SRF18scfxEIQkSb2e6hLJqrywKt-yZk1rQBk71uCkYPhjDXtdeSKqPZlPULxyN3yUOYTRNxVouOZVNfJqjuvHuDErRyQZ2g9rqQc4SH_xPwbBsXjrymSFTp6fNKSxPseNLppj_zPC650a08rbE5wAHsl0zhjkmnrQjFYhRdH7C747pQqYXeT11QZVzOdPBpEgjiCf8Khd_b9EbTfWh1H8I5QSVwKNUV0YVb9TlM-3z55-UfZfJGqRlHj1SXzo2dg_bWsRM1PlBuwAppw5X-xUaz4Uxr8uPAbX6s4TOrD0f0Q1FfH9vbg2UcWQU7c544KX3lISHBkDvSZBlRnNBn2k9F8KJjZO67YRPxOGh21WyjT-B1W9Ou_Cx-oCF4DUSXqqi2fqF4eWiGmLwJTv78FVwtGVdCoijGq4AHUlRnw4j7r6PiDlwRZNknZresAIUwpbFjPYwfyCqXBbx7X2hDc0IDWdUlJnfOwBiVaMAST4QrB22LkgqaNuUaSDeLQ1yJt43IbwPa8SNbQuy8M47-0-l56Xakt4acU_D3QtsBjaFa3xDumUX4qT2hiQ2vvDlNkrLA919wqEt6hmBVGL-LSVAy33xnFWGct2F1MBQcdw_t-7K4hTG_-o', '^!lang': 'ZW4', '^!obv4': 'AAEBAQEAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAA', '^!prd': 'MDowOjE6MDow', '^!stbmp': 'WHZvYU94dWg2YjQ', '*keyring': 'EMJZq0HpwoWfezH-CLej0M9iArM7QvqNgw7SlfzHZUV57CCl2igJ_vUtADo6bO573x2ytugShWUEYsl5E8JlaTrMIdYDILOTtNJQp9EAU2rrTjGq5F8mZH_zwqnlQUIE2WrnW1BDinyBQW3adXQB3CM', '+puCu255': 'ZmVHJs1OTJLB2zbyVUwbXSrS7QMFK3F8p_QXfIpc7kQ', '+puEd255': 'XihFJ2lVeppnMLChMC6WG6f4d2fzRhzAi3-NBfYekGs', '+sigCu255': 'AAAAAGgIrzMIin9UmwV3KVg2JaLfoZKdU-Na0ABaQHSQvcf2XTc1W8zjBql0u13bJkcLa8-0blzj_3gFDlErs2O9RhV1D9EF', '+sigPubk': 'AAAAAGgIrzPaEVm0Y7NKyrsYZNvlaVhZvoptB5rDyCjkA85CgaAVn7YijQHN9hJTJYPd7WwAkcuhGptbeiPqS3AKApFqqSID', 'firstname': 'SWZlc3MgUFRSSg', 'lastname': 'QmFja3Vw', 'name2': 'SWZlc3MgUFRSSiBCYWNrdXA', 'terms': 'Mg', 'ts': 'uN7Fm9cZp0bzHUCVOxOnb7N3EO1GkQi4ncJQ6nSvymU', 'features': [], 'flags': {'ach': 1, 'bstrg': 21474836480, 'mcs': 1, 'mfae': 1, 'nsre': 1, 'nlfe': 1, 'cspe': 1, 'smsve': 1, 'refpr': 1, 'ff_chmon': 1, 'ssrs': 1, 'aplvp': 1, 'nobp': 1, 'rw': 1, 'ab_fmpup': 1, 'ff_vpnft': 1, 'ff_npp': 1, 'ff_adse': 1, 'ff_npabm': 1, 'ff_refsun': 2, 'ff_iguhm': 1, 'pf': 1}, 'aav': 2, 'ipcc': 'ID', 'aas': 'r4CLpGpSNFMfF44FfOBFQxJfAHpjF-XXD0RAoafRrfI', 'ut': '3amjOoCRrUk'}
2025-04-24 11:33:35,959 - mega_client_py310 - WARNING - Could not get storage details: unsupported operand type(s) for /: 'dict' and 'float'
2025-04-24 11:35:42,994 - mega_client_py310 - INFO - Internet connection verified via www.google.com
2025-04-24 11:35:43,110 - mega_client_py310 - INFO - Successfully imported MEGA library
2025-04-24 11:35:43,110 - mega_client_py310 - INFO - Connecting to MEGA...
2025-04-24 11:35:43,110 - mega_client_py310 - INFO - Using default MEGA credentials
2025-04-24 11:35:43,111 - mega_client_py310 - INFO - Logging in user...
2025-04-24 11:35:43,111 - mega_client_py310 - INFO - Login attempt 1/3
2025-04-24 11:35:43,111 - mega.mega - INFO - Logging in user...
2025-04-24 11:35:44,544 - mega.mega - INFO - Getting all files...
2025-04-24 11:35:45,186 - mega.mega - INFO - Login complete
2025-04-24 11:35:45,186 - mega_client_py310 - INFO - Successfully logged in to <NAME_EMAIL>
2025-04-24 11:35:46,238 - mega_client_py310 - INFO - Logged in as: {'u': 'vlj9OThSMt0', 's': 0, 'since': 1745399575, 'na': 1, 'email': '<EMAIL>', 'emails': ['<EMAIL>'], 'pemails': [], 'name': 'Ifess PTRJ Backup', 'k': 'fKt0pygAcjO6haOM1KDgtQ', 'c': 1, 'pubk': 'CACNbTRDqc_1z9m9b1ATarJioZW6N8KDDW-oVctrnerImHrPtWqgWYluwricyL2GalGEMGWHA1IBH8JO8vG4t2cwpSpP8Yf1cb3FVFdXkgU132JdTwynL9OBIkxk45Wv2tHyGOln8qaX9w62PU09QrvSWlvxr0FMiqVSXP18zpHX841UbYQ0tCqg-_wnp4JoVH3HLJo8AHCT4RydQkGb3bTDOyjsz1Bhoe6OXXOcrvgXPj39HgT_GFc4j4cYDa2xHfxyWdckpESKCHSzp_RrQcUaMeky9U_udVX4ZpkSKkqyNik2B6_aoKSK8dlGsxlijBb6WErAydmwJxiUasRR1HkZACAAAAEB', 'privk': 'WwgEXtv4wfe0YbHgaYL2aTaKpHArpXw2TEwuAj-WJhSGFm0pedyneqNxN44YBYw_GPC-BUfzxV_hoJzszPWASO8prabpuviS4z-Gqa-seSSuXa0OcmbZU8MBEuC8TXTUXMK0ImzlIoNT1MR88YXmTVmq4UqLcZKY5GGtSi12kFpIhz8NGLh-zDZ2gD_aNVPYfftmBxFD5mJcxVhOl73UgXW-sWto3fsZpFHZr6uBRgiYdtTZgxrvqvDjm34k_UsjZt3AC4TXlPj6mOqESd8naeXQINckaS28K2FNDSvrTK_3abhXwOh4jLYIlt16f7R3DsXQSiKwhv1XR0_TuDQCIOWbU0Ke2m2MXS_fAp62VJhlTbcpapfKUuDak6Xt0fnxBGzjutWuueX2GsYh0Gpjd6V3BVKDjrTSP3SCenbsvT7iNzX_LHKnYQKcLFWkEZJO57DclutoDGdS-1of-vhm3piTR3EutnWVg5r4PElrIO0-ES3fLLGcO48dkdEaTUY3XZ-UaRkBUoefZKMYC2grAbq4Cmd_NVgnjUPZQDBEq6qR7gpBq8hrCv9lAQ_6oFbwBk6j9NQJCWSBLHFRR5JrTEtZOdTqZ6ahCBxDu0T6rvJXdiwNQg31aY57xE52BfOrouyNoPq3KLwpqAvUnRJU7Gow1VLMik-7yynI8fGzl61OiC9oCtoJqu4zK2AKysb1dCfjJhmpTF2ueicBmRx4yopI7kJwh4M1tB-W13U4vhEmkEOcHwbEfhHYDpEGcT5YGFKDYfhuVIZOhnq9OGFRvbi8ENGe6nM0baUONxACmwP990gY7ZGq_4rLM1Tc6tlZCOwWqfTTE6NpCI_vqAKd6Lwup00g50aMqfdzjeHHC-Y', '^!fudnxt': 'MTc0NjIwNTIwMDAwMA', '^!keys': 'FABvuL1r4bzuXgNozonDd8FuJZZBJYJm8aXI7Wom7_lXE-FSEAVMb5pWC6uSp7ViA9xjlCRA3Ii-Ps9lHNW83ZJ7-0_7JA3azjcnKH_RK9AhyCLAupw84_7gFSaz-fdoI6LU27cuJLv0g4MBz_7-5SrZdoBC71fg9yueCZmPSw8EZn43E6FIfi5O2zuQIatPNJRlIj1a9ZVVImQggcaxoEzsBuP5blk06_jF3SBs-b--bVIGkzjBOMmdmLCRnGSM9wcldao_kjc9_4X_76lk0ZQtKXWn1Rchv1SRF18scfxEIQkSb2e6hLJqrywKt-yZk1rQBk71uCkYPhjDXtdeSKqPZlPULxyN3yUOYTRNxVouOZVNfJqjuvHuDErRyQZ2g9rqQc4SH_xPwbBsXjrymSFTp6fNKSxPseNLppj_zPC650a08rbE5wAHsl0zhjkmnrQjFYhRdH7C747pQqYXeT11QZVzOdPBpEgjiCf8Khd_b9EbTfWh1H8I5QSVwKNUV0YVb9TlM-3z55-UfZfJGqRlHj1SXzo2dg_bWsRM1PlBuwAppw5X-xUaz4Uxr8uPAbX6s4TOrD0f0Q1FfH9vbg2UcWQU7c544KX3lISHBkDvSZBlRnNBn2k9F8KJjZO67YRPxOGh21WyjT-B1W9Ou_Cx-oCF4DUSXqqi2fqF4eWiGmLwJTv78FVwtGVdCoijGq4AHUlRnw4j7r6PiDlwRZNknZresAIUwpbFjPYwfyCqXBbx7X2hDc0IDWdUlJnfOwBiVaMAST4QrB22LkgqaNuUaSDeLQ1yJt43IbwPa8SNbQuy8M47-0-l56Xakt4acU_D3QtsBjaFa3xDumUX4qT2hiQ2vvDlNkrLA919wqEt6hmBVGL-LSVAy33xnFWGct2F1MBQcdw_t-7K4hTG_-o', '^!lang': 'ZW4', '^!obv4': 'AAEBAQEAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAA', '^!prd': 'MDowOjE6MDow', '^!stbmp': 'S3FtbFBHUXNoMnM', '*keyring': 'EMJZq0HpwoWfezH-CLej0M9iArM7QvqNgw7SlfzHZUV57CCl2igJ_vUtADo6bO573x2ytugShWUEYsl5E8JlaTrMIdYDILOTtNJQp9EAU2rrTjGq5F8mZH_zwqnlQUIE2WrnW1BDinyBQW3adXQB3CM', '+puCu255': 'ZmVHJs1OTJLB2zbyVUwbXSrS7QMFK3F8p_QXfIpc7kQ', '+puEd255': 'XihFJ2lVeppnMLChMC6WG6f4d2fzRhzAi3-NBfYekGs', '+sigCu255': 'AAAAAGgIrzMIin9UmwV3KVg2JaLfoZKdU-Na0ABaQHSQvcf2XTc1W8zjBql0u13bJkcLa8-0blzj_3gFDlErs2O9RhV1D9EF', '+sigPubk': 'AAAAAGgIrzPaEVm0Y7NKyrsYZNvlaVhZvoptB5rDyCjkA85CgaAVn7YijQHN9hJTJYPd7WwAkcuhGptbeiPqS3AKApFqqSID', 'firstname': 'SWZlc3MgUFRSSg', 'lastname': 'QmFja3Vw', 'name2': 'SWZlc3MgUFRSSiBCYWNrdXA', 'terms': 'Mg', 'ts': 'uN7Fm9cZp0bzHUCVOxOnb7N3EO1GkQi4ncJQ6nSvymU', 'features': [], 'flags': {'ach': 1, 'bstrg': 21474836480, 'mcs': 1, 'mfae': 1, 'nsre': 1, 'nlfe': 1, 'cspe': 1, 'smsve': 1, 'refpr': 1, 'ff_chmon': 1, 'ssrs': 1, 'aplvp': 1, 'nobp': 1, 'rw': 1, 'ab_fmpup': 1, 'ff_vpnft': 1, 'ff_npp': 1, 'ff_adse': 1, 'ff_npabm': 1, 'ff_refsun': 2, 'ff_iguhm': 1, 'pf': 1}, 'aav': 2, 'ipcc': 'ID', 'aas': 'r4CLpGpSNFMfF44FfOBFQxJfAHpjF-XXD0RAoafRrfI', 'ut': '3amjOoCRrUk'}
2025-04-24 11:35:47,525 - mega_client_py310 - WARNING - Could not get storage details: unsupported operand type(s) for /: 'dict' and 'float'
2025-04-24 11:37:51,322 - test_mega_upload - INFO - Starting MEGA upload test
2025-04-24 11:37:51,322 - test_mega_upload - INFO - Created test file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\test_upload.txt (5550 bytes)
2025-04-24 11:37:51,322 - test_mega_upload - INFO - Initializing MEGA client
2025-04-24 11:37:51,323 - test_mega_upload - ERROR - Test failed with exception: MegaClient.__init__() missing 1 required positional argument: 'client_id'
2025-04-24 11:37:51,323 - test_mega_upload - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\test_mega_upload.py", line 31, in test_upload
    mega_client = MegaClient()
TypeError: MegaClient.__init__() missing 1 required positional argument: 'client_id'

2025-04-24 11:37:51,323 - test_mega_upload - INFO - Cleaning up test file
2025-04-24 11:37:51,323 - test_mega_upload - INFO - Removed test file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\test_upload.txt
2025-04-24 11:37:51,323 - test_mega_upload - INFO - MEGA upload test completed
2025-04-24 11:37:56,150 - test_mega_upload - INFO - Starting MEGA upload test
2025-04-24 11:37:56,153 - test_mega_upload - INFO - Created test file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\test_upload.txt (5550 bytes)
2025-04-24 11:37:56,153 - test_mega_upload - INFO - Initializing MEGA client
2025-04-24 11:37:56,153 - test_mega_upload - ERROR - Test failed with exception: MegaClient.__init__() missing 1 required positional argument: 'client_id'
2025-04-24 11:37:56,153 - test_mega_upload - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\test_mega_upload.py", line 31, in test_upload
    mega_client = MegaClient()
TypeError: MegaClient.__init__() missing 1 required positional argument: 'client_id'

2025-04-24 11:37:56,154 - test_mega_upload - INFO - Cleaning up test file
2025-04-24 11:37:56,154 - test_mega_upload - INFO - Removed test file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\test_upload.txt
2025-04-24 11:37:56,154 - test_mega_upload - INFO - MEGA upload test completed
2025-04-24 11:41:03,210 - test_mega_upload - INFO - Starting MEGA upload test
2025-04-24 11:41:03,210 - test_mega_upload - INFO - Created test file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\test_upload.txt (5550 bytes)
2025-04-24 11:41:03,210 - test_mega_upload - INFO - Initializing MEGA client
2025-04-24 11:41:03,283 - mega_client_py310 - INFO - Internet connection verified via www.google.com
2025-04-24 11:41:03,433 - mega_client_py310 - INFO - Successfully imported MEGA library
2025-04-24 11:41:03,433 - mega_client_py310 - INFO - Connecting to MEGA...
2025-04-24 11:41:03,434 - mega_client_py310 - INFO - Using default MEGA credentials
2025-04-24 11:41:03,434 - mega_client_py310 - INFO - Logging in user...
2025-04-24 11:41:03,434 - mega_client_py310 - INFO - Login attempt 1/3
2025-04-24 11:41:03,434 - mega.mega - INFO - Logging in user...
2025-04-24 11:41:05,518 - mega.mega - INFO - Getting all files...
2025-04-24 11:41:06,176 - mega.mega - INFO - Login complete
2025-04-24 11:41:06,176 - mega_client_py310 - INFO - Successfully logged in to <NAME_EMAIL>
2025-04-24 11:41:06,789 - mega_client_py310 - INFO - Logged in as: {'u': 'vlj9OThSMt0', 's': 0, 'since': 1745399575, 'na': 1, 'email': '<EMAIL>', 'emails': ['<EMAIL>'], 'pemails': [], 'name': 'Ifess PTRJ Backup', 'k': 'fKt0pygAcjO6haOM1KDgtQ', 'c': 1, 'pubk': 'CACNbTRDqc_1z9m9b1ATarJioZW6N8KDDW-oVctrnerImHrPtWqgWYluwricyL2GalGEMGWHA1IBH8JO8vG4t2cwpSpP8Yf1cb3FVFdXkgU132JdTwynL9OBIkxk45Wv2tHyGOln8qaX9w62PU09QrvSWlvxr0FMiqVSXP18zpHX841UbYQ0tCqg-_wnp4JoVH3HLJo8AHCT4RydQkGb3bTDOyjsz1Bhoe6OXXOcrvgXPj39HgT_GFc4j4cYDa2xHfxyWdckpESKCHSzp_RrQcUaMeky9U_udVX4ZpkSKkqyNik2B6_aoKSK8dlGsxlijBb6WErAydmwJxiUasRR1HkZACAAAAEB', 'privk': 'WwgEXtv4wfe0YbHgaYL2aTaKpHArpXw2TEwuAj-WJhSGFm0pedyneqNxN44YBYw_GPC-BUfzxV_hoJzszPWASO8prabpuviS4z-Gqa-seSSuXa0OcmbZU8MBEuC8TXTUXMK0ImzlIoNT1MR88YXmTVmq4UqLcZKY5GGtSi12kFpIhz8NGLh-zDZ2gD_aNVPYfftmBxFD5mJcxVhOl73UgXW-sWto3fsZpFHZr6uBRgiYdtTZgxrvqvDjm34k_UsjZt3AC4TXlPj6mOqESd8naeXQINckaS28K2FNDSvrTK_3abhXwOh4jLYIlt16f7R3DsXQSiKwhv1XR0_TuDQCIOWbU0Ke2m2MXS_fAp62VJhlTbcpapfKUuDak6Xt0fnxBGzjutWuueX2GsYh0Gpjd6V3BVKDjrTSP3SCenbsvT7iNzX_LHKnYQKcLFWkEZJO57DclutoDGdS-1of-vhm3piTR3EutnWVg5r4PElrIO0-ES3fLLGcO48dkdEaTUY3XZ-UaRkBUoefZKMYC2grAbq4Cmd_NVgnjUPZQDBEq6qR7gpBq8hrCv9lAQ_6oFbwBk6j9NQJCWSBLHFRR5JrTEtZOdTqZ6ahCBxDu0T6rvJXdiwNQg31aY57xE52BfOrouyNoPq3KLwpqAvUnRJU7Gow1VLMik-7yynI8fGzl61OiC9oCtoJqu4zK2AKysb1dCfjJhmpTF2ueicBmRx4yopI7kJwh4M1tB-W13U4vhEmkEOcHwbEfhHYDpEGcT5YGFKDYfhuVIZOhnq9OGFRvbi8ENGe6nM0baUONxACmwP990gY7ZGq_4rLM1Tc6tlZCOwWqfTTE6NpCI_vqAKd6Lwup00g50aMqfdzjeHHC-Y', '^!fudnxt': 'MTc0NjIwNTIwMDAwMA', '^!keys': 'FABvuL1r4bzuXgNozonDd8FuJZZBJYJm8aXI7Wom7_lXE-FSEAVMb5pWC6uSp7ViA9xjlCRA3Ii-Ps9lHNW83ZJ7-0_7JA3azjcnKH_RK9AhyCLAupw84_7gFSaz-fdoI6LU27cuJLv0g4MBz_7-5SrZdoBC71fg9yueCZmPSw8EZn43E6FIfi5O2zuQIatPNJRlIj1a9ZVVImQggcaxoEzsBuP5blk06_jF3SBs-b--bVIGkzjBOMmdmLCRnGSM9wcldao_kjc9_4X_76lk0ZQtKXWn1Rchv1SRF18scfxEIQkSb2e6hLJqrywKt-yZk1rQBk71uCkYPhjDXtdeSKqPZlPULxyN3yUOYTRNxVouOZVNfJqjuvHuDErRyQZ2g9rqQc4SH_xPwbBsXjrymSFTp6fNKSxPseNLppj_zPC650a08rbE5wAHsl0zhjkmnrQjFYhRdH7C747pQqYXeT11QZVzOdPBpEgjiCf8Khd_b9EbTfWh1H8I5QSVwKNUV0YVb9TlM-3z55-UfZfJGqRlHj1SXzo2dg_bWsRM1PlBuwAppw5X-xUaz4Uxr8uPAbX6s4TOrD0f0Q1FfH9vbg2UcWQU7c544KX3lISHBkDvSZBlRnNBn2k9F8KJjZO67YRPxOGh21WyjT-B1W9Ou_Cx-oCF4DUSXqqi2fqF4eWiGmLwJTv78FVwtGVdCoijGq4AHUlRnw4j7r6PiDlwRZNknZresAIUwpbFjPYwfyCqXBbx7X2hDc0IDWdUlJnfOwBiVaMAST4QrB22LkgqaNuUaSDeLQ1yJt43IbwPa8SNbQuy8M47-0-l56Xakt4acU_D3QtsBjaFa3xDumUX4qT2hiQ2vvDlNkrLA919wqEt6hmBVGL-LSVAy33xnFWGct2F1MBQcdw_t-7K4hTG_-o', '^!lang': 'ZW4', '^!obv4': 'AAEBAQEAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAA', '^!prd': 'MDowOjE6MDow', '^!stbmp': 'Y190aHdoZTQwT0U', '*keyring': 'EMJZq0HpwoWfezH-CLej0M9iArM7QvqNgw7SlfzHZUV57CCl2igJ_vUtADo6bO573x2ytugShWUEYsl5E8JlaTrMIdYDILOTtNJQp9EAU2rrTjGq5F8mZH_zwqnlQUIE2WrnW1BDinyBQW3adXQB3CM', '+puCu255': 'ZmVHJs1OTJLB2zbyVUwbXSrS7QMFK3F8p_QXfIpc7kQ', '+puEd255': 'XihFJ2lVeppnMLChMC6WG6f4d2fzRhzAi3-NBfYekGs', '+sigCu255': 'AAAAAGgIrzMIin9UmwV3KVg2JaLfoZKdU-Na0ABaQHSQvcf2XTc1W8zjBql0u13bJkcLa8-0blzj_3gFDlErs2O9RhV1D9EF', '+sigPubk': 'AAAAAGgIrzPaEVm0Y7NKyrsYZNvlaVhZvoptB5rDyCjkA85CgaAVn7YijQHN9hJTJYPd7WwAkcuhGptbeiPqS3AKApFqqSID', 'firstname': 'SWZlc3MgUFRSSg', 'lastname': 'QmFja3Vw', 'name2': 'SWZlc3MgUFRSSiBCYWNrdXA', 'terms': 'Mg', 'ts': 'uN7Fm9cZp0bzHUCVOxOnb7N3EO1GkQi4ncJQ6nSvymU', 'features': [], 'flags': {'ach': 1, 'bstrg': 21474836480, 'mcs': 1, 'mfae': 1, 'nsre': 1, 'nlfe': 1, 'cspe': 1, 'smsve': 1, 'refpr': 1, 'ff_chmon': 1, 'ssrs': 1, 'aplvp': 1, 'nobp': 1, 'rw': 1, 'ab_fmpup': 1, 'ff_vpnft': 1, 'ff_npp': 1, 'ff_adse': 1, 'ff_npabm': 1, 'ff_refsun': 2, 'ff_iguhm': 1, 'pf': 1}, 'aav': 2, 'ipcc': 'ID', 'aas': 'r4CLpGpSNFMfF44FfOBFQxJfAHpjF-XXD0RAoafRrfI', 'ut': '3amjOoCRrUk'}
2025-04-24 11:41:08,139 - mega_client_py310 - INFO - Storage: 0.00 GB used of 0.00 GB total
2025-04-24 11:41:08,139 - test_mega_upload - INFO - Attempting to log in to MEGA
2025-04-24 11:41:08,139 - test_mega_upload - INFO - Uploading test file to MEGA
2025-04-24 11:41:08,140 - mega_client_py310 - INFO - Starting MEGA upload: test_upload_20250424_114108.txt (5550 bytes)
2025-04-24 11:41:08,140 - mega.mega - INFO - Getting all files...
2025-04-24 11:41:08,917 - mega_client_py310 - INFO - Found existing IFESS_Backups folder with ID: ppYR2JAL
2025-04-24 11:41:08,917 - mega_client_py310 - INFO - Uploading to IFESS_Backups folder (ID: ppYR2JAL)
2025-04-24 11:41:08,917 - mega_client_py310 - ERROR - Error during upload: Mega.upload() got an unexpected keyword argument 'dest_folder'
2025-04-24 11:41:08,917 - mega_client_py310 - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\mega_client_py310.py", line 298, in upload_file_to_mega
    upload = self.m.upload(file_path, dest_filename=upload_name, dest_folder=backup_folder)
TypeError: Mega.upload() got an unexpected keyword argument 'dest_folder'

2025-04-24 11:41:08,917 - test_mega_upload - ERROR - ==================================================
2025-04-24 11:41:08,917 - test_mega_upload - ERROR - UPLOAD FAILED!
2025-04-24 11:41:08,917 - test_mega_upload - ERROR - Error: Mega.upload() got an unexpected keyword argument 'dest_folder'
2025-04-24 11:41:08,917 - test_mega_upload - ERROR - ==================================================
2025-04-24 11:41:08,917 - test_mega_upload - INFO - Cleaning up test file
2025-04-24 11:41:08,917 - test_mega_upload - INFO - Removed test file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\test_upload.txt
2025-04-24 11:41:08,917 - test_mega_upload - INFO - MEGA upload test completed
2025-04-24 11:42:03,057 - test_mega_upload - INFO - Starting MEGA upload test
2025-04-24 11:42:03,057 - test_mega_upload - INFO - Created test file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\test_upload.txt (5550 bytes)
2025-04-24 11:42:03,057 - test_mega_upload - INFO - Initializing MEGA client
2025-04-24 11:42:03,108 - mega_client_py310 - INFO - Internet connection verified via www.google.com
2025-04-24 11:42:03,235 - mega_client_py310 - INFO - Successfully imported MEGA library
2025-04-24 11:42:03,235 - mega_client_py310 - INFO - Connecting to MEGA...
2025-04-24 11:42:03,235 - mega_client_py310 - INFO - Using default MEGA credentials
2025-04-24 11:42:03,235 - mega_client_py310 - INFO - Logging in user...
2025-04-24 11:42:03,235 - mega_client_py310 - INFO - Login attempt 1/3
2025-04-24 11:42:03,235 - mega.mega - INFO - Logging in user...
2025-04-24 11:42:04,681 - mega.mega - INFO - Getting all files...
2025-04-24 11:42:05,440 - mega.mega - INFO - Login complete
2025-04-24 11:42:05,440 - mega_client_py310 - INFO - Successfully logged in to <NAME_EMAIL>
2025-04-24 11:42:06,079 - mega_client_py310 - INFO - Logged in as: {'u': 'vlj9OThSMt0', 's': 0, 'since': 1745399575, 'na': 1, 'email': '<EMAIL>', 'emails': ['<EMAIL>'], 'pemails': [], 'name': 'Ifess PTRJ Backup', 'k': 'fKt0pygAcjO6haOM1KDgtQ', 'c': 1, 'pubk': 'CACNbTRDqc_1z9m9b1ATarJioZW6N8KDDW-oVctrnerImHrPtWqgWYluwricyL2GalGEMGWHA1IBH8JO8vG4t2cwpSpP8Yf1cb3FVFdXkgU132JdTwynL9OBIkxk45Wv2tHyGOln8qaX9w62PU09QrvSWlvxr0FMiqVSXP18zpHX841UbYQ0tCqg-_wnp4JoVH3HLJo8AHCT4RydQkGb3bTDOyjsz1Bhoe6OXXOcrvgXPj39HgT_GFc4j4cYDa2xHfxyWdckpESKCHSzp_RrQcUaMeky9U_udVX4ZpkSKkqyNik2B6_aoKSK8dlGsxlijBb6WErAydmwJxiUasRR1HkZACAAAAEB', 'privk': 'WwgEXtv4wfe0YbHgaYL2aTaKpHArpXw2TEwuAj-WJhSGFm0pedyneqNxN44YBYw_GPC-BUfzxV_hoJzszPWASO8prabpuviS4z-Gqa-seSSuXa0OcmbZU8MBEuC8TXTUXMK0ImzlIoNT1MR88YXmTVmq4UqLcZKY5GGtSi12kFpIhz8NGLh-zDZ2gD_aNVPYfftmBxFD5mJcxVhOl73UgXW-sWto3fsZpFHZr6uBRgiYdtTZgxrvqvDjm34k_UsjZt3AC4TXlPj6mOqESd8naeXQINckaS28K2FNDSvrTK_3abhXwOh4jLYIlt16f7R3DsXQSiKwhv1XR0_TuDQCIOWbU0Ke2m2MXS_fAp62VJhlTbcpapfKUuDak6Xt0fnxBGzjutWuueX2GsYh0Gpjd6V3BVKDjrTSP3SCenbsvT7iNzX_LHKnYQKcLFWkEZJO57DclutoDGdS-1of-vhm3piTR3EutnWVg5r4PElrIO0-ES3fLLGcO48dkdEaTUY3XZ-UaRkBUoefZKMYC2grAbq4Cmd_NVgnjUPZQDBEq6qR7gpBq8hrCv9lAQ_6oFbwBk6j9NQJCWSBLHFRR5JrTEtZOdTqZ6ahCBxDu0T6rvJXdiwNQg31aY57xE52BfOrouyNoPq3KLwpqAvUnRJU7Gow1VLMik-7yynI8fGzl61OiC9oCtoJqu4zK2AKysb1dCfjJhmpTF2ueicBmRx4yopI7kJwh4M1tB-W13U4vhEmkEOcHwbEfhHYDpEGcT5YGFKDYfhuVIZOhnq9OGFRvbi8ENGe6nM0baUONxACmwP990gY7ZGq_4rLM1Tc6tlZCOwWqfTTE6NpCI_vqAKd6Lwup00g50aMqfdzjeHHC-Y', '^!fudnxt': 'MTc0NjIwNTIwMDAwMA', '^!keys': 'FABvuL1r4bzuXgNozonDd8FuJZZBJYJm8aXI7Wom7_lXE-FSEAVMb5pWC6uSp7ViA9xjlCRA3Ii-Ps9lHNW83ZJ7-0_7JA3azjcnKH_RK9AhyCLAupw84_7gFSaz-fdoI6LU27cuJLv0g4MBz_7-5SrZdoBC71fg9yueCZmPSw8EZn43E6FIfi5O2zuQIatPNJRlIj1a9ZVVImQggcaxoEzsBuP5blk06_jF3SBs-b--bVIGkzjBOMmdmLCRnGSM9wcldao_kjc9_4X_76lk0ZQtKXWn1Rchv1SRF18scfxEIQkSb2e6hLJqrywKt-yZk1rQBk71uCkYPhjDXtdeSKqPZlPULxyN3yUOYTRNxVouOZVNfJqjuvHuDErRyQZ2g9rqQc4SH_xPwbBsXjrymSFTp6fNKSxPseNLppj_zPC650a08rbE5wAHsl0zhjkmnrQjFYhRdH7C747pQqYXeT11QZVzOdPBpEgjiCf8Khd_b9EbTfWh1H8I5QSVwKNUV0YVb9TlM-3z55-UfZfJGqRlHj1SXzo2dg_bWsRM1PlBuwAppw5X-xUaz4Uxr8uPAbX6s4TOrD0f0Q1FfH9vbg2UcWQU7c544KX3lISHBkDvSZBlRnNBn2k9F8KJjZO67YRPxOGh21WyjT-B1W9Ou_Cx-oCF4DUSXqqi2fqF4eWiGmLwJTv78FVwtGVdCoijGq4AHUlRnw4j7r6PiDlwRZNknZresAIUwpbFjPYwfyCqXBbx7X2hDc0IDWdUlJnfOwBiVaMAST4QrB22LkgqaNuUaSDeLQ1yJt43IbwPa8SNbQuy8M47-0-l56Xakt4acU_D3QtsBjaFa3xDumUX4qT2hiQ2vvDlNkrLA919wqEt6hmBVGL-LSVAy33xnFWGct2F1MBQcdw_t-7K4hTG_-o', '^!lang': 'ZW4', '^!obv4': 'AAEBAQEAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAA', '^!prd': 'MDowOjE6MDow', '^!stbmp': 'U196TGFJX2RSbFE', '*keyring': 'EMJZq0HpwoWfezH-CLej0M9iArM7QvqNgw7SlfzHZUV57CCl2igJ_vUtADo6bO573x2ytugShWUEYsl5E8JlaTrMIdYDILOTtNJQp9EAU2rrTjGq5F8mZH_zwqnlQUIE2WrnW1BDinyBQW3adXQB3CM', '+puCu255': 'ZmVHJs1OTJLB2zbyVUwbXSrS7QMFK3F8p_QXfIpc7kQ', '+puEd255': 'XihFJ2lVeppnMLChMC6WG6f4d2fzRhzAi3-NBfYekGs', '+sigCu255': 'AAAAAGgIrzMIin9UmwV3KVg2JaLfoZKdU-Na0ABaQHSQvcf2XTc1W8zjBql0u13bJkcLa8-0blzj_3gFDlErs2O9RhV1D9EF', '+sigPubk': 'AAAAAGgIrzPaEVm0Y7NKyrsYZNvlaVhZvoptB5rDyCjkA85CgaAVn7YijQHN9hJTJYPd7WwAkcuhGptbeiPqS3AKApFqqSID', 'firstname': 'SWZlc3MgUFRSSg', 'lastname': 'QmFja3Vw', 'name2': 'SWZlc3MgUFRSSiBCYWNrdXA', 'terms': 'Mg', 'ts': 'uN7Fm9cZp0bzHUCVOxOnb7N3EO1GkQi4ncJQ6nSvymU', 'features': [], 'flags': {'ach': 1, 'bstrg': 21474836480, 'mcs': 1, 'mfae': 1, 'nsre': 1, 'nlfe': 1, 'cspe': 1, 'smsve': 1, 'refpr': 1, 'ff_chmon': 1, 'ssrs': 1, 'aplvp': 1, 'nobp': 1, 'rw': 1, 'ab_fmpup': 1, 'ff_vpnft': 1, 'ff_npp': 1, 'ff_adse': 1, 'ff_npabm': 1, 'ff_refsun': 2, 'ff_iguhm': 1, 'pf': 1}, 'aav': 2, 'ipcc': 'ID', 'aas': 'r4CLpGpSNFMfF44FfOBFQxJfAHpjF-XXD0RAoafRrfI', 'ut': '3amjOoCRrUk'}
2025-04-24 11:42:07,456 - mega_client_py310 - INFO - Storage: 0.00 GB used of 0.00 GB total
2025-04-24 11:42:07,457 - test_mega_upload - INFO - Attempting to log in to MEGA
2025-04-24 11:42:07,457 - test_mega_upload - INFO - Uploading test file to MEGA
2025-04-24 11:42:07,457 - mega_client_py310 - INFO - Starting MEGA upload: test_upload_20250424_114207.txt (5550 bytes)
2025-04-24 11:42:07,457 - mega.mega - INFO - Getting all files...
2025-04-24 11:42:08,097 - mega_client_py310 - INFO - Found existing IFESS_Backups folder with ID: ppYR2JAL
2025-04-24 11:42:08,097 - mega_client_py310 - INFO - Uploading to IFESS_Backups folder (ID: ppYR2JAL)
2025-04-24 11:42:08,097 - mega_client_py310 - ERROR - Error during upload: Mega.upload() got an unexpected keyword argument 'dest_folder'
2025-04-24 11:42:08,098 - mega_client_py310 - ERROR - Traceback (most recent call last):
  File "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\mega_client_py310.py", line 298, in upload_file_to_mega
    upload = self.m.upload(file_path, dest_filename=upload_name, dest_folder=backup_folder)
TypeError: Mega.upload() got an unexpected keyword argument 'dest_folder'

2025-04-24 11:42:08,098 - test_mega_upload - ERROR - ==================================================
2025-04-24 11:42:08,098 - test_mega_upload - ERROR - UPLOAD FAILED!
2025-04-24 11:42:08,099 - test_mega_upload - ERROR - Error: Mega.upload() got an unexpected keyword argument 'dest_folder'
2025-04-24 11:42:08,099 - test_mega_upload - ERROR - ==================================================
2025-04-24 11:42:08,099 - test_mega_upload - INFO - Cleaning up test file
2025-04-24 11:42:08,099 - test_mega_upload - INFO - Removed test file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\test_upload.txt
2025-04-24 11:42:08,099 - test_mega_upload - INFO - MEGA upload test completed
2025-04-24 13:49:39,011 - mega_client_py310 - INFO - Connecting to MEGA...
2025-04-24 13:49:39,133 - mega_client_py310 - INFO - Successfully imported MEGA library
2025-04-24 13:49:39,134 - mega_client_py310 - INFO - Using default MEGA credentials
2025-04-24 13:49:39,134 - mega_client_py310 - INFO - Attempting to login to <NAME_EMAIL>
2025-04-24 13:49:39,134 - mega.mega - INFO - Logging in user...
2025-04-24 13:49:40,606 - mega.mega - INFO - Getting all files...
2025-04-24 13:49:41,313 - mega.mega - INFO - Login complete
2025-04-24 13:49:41,313 - mega_client_py310 - INFO - Successfully logged in to <NAME_EMAIL>
2025-04-24 13:49:41,995 - mega_client_py310 - INFO - Logged in as: {'u': 'vlj9OThSMt0', 's': 0, 'since': 1745399575, 'na': 1, 'email': '<EMAIL>', 'emails': ['<EMAIL>'], 'pemails': [], 'name': 'Ifess PTRJ Backup', 'k': 'fKt0pygAcjO6haOM1KDgtQ', 'c': 1, 'pubk': 'CACNbTRDqc_1z9m9b1ATarJioZW6N8KDDW-oVctrnerImHrPtWqgWYluwricyL2GalGEMGWHA1IBH8JO8vG4t2cwpSpP8Yf1cb3FVFdXkgU132JdTwynL9OBIkxk45Wv2tHyGOln8qaX9w62PU09QrvSWlvxr0FMiqVSXP18zpHX841UbYQ0tCqg-_wnp4JoVH3HLJo8AHCT4RydQkGb3bTDOyjsz1Bhoe6OXXOcrvgXPj39HgT_GFc4j4cYDa2xHfxyWdckpESKCHSzp_RrQcUaMeky9U_udVX4ZpkSKkqyNik2B6_aoKSK8dlGsxlijBb6WErAydmwJxiUasRR1HkZACAAAAEB', 'privk': 'WwgEXtv4wfe0YbHgaYL2aTaKpHArpXw2TEwuAj-WJhSGFm0pedyneqNxN44YBYw_GPC-BUfzxV_hoJzszPWASO8prabpuviS4z-Gqa-seSSuXa0OcmbZU8MBEuC8TXTUXMK0ImzlIoNT1MR88YXmTVmq4UqLcZKY5GGtSi12kFpIhz8NGLh-zDZ2gD_aNVPYfftmBxFD5mJcxVhOl73UgXW-sWto3fsZpFHZr6uBRgiYdtTZgxrvqvDjm34k_UsjZt3AC4TXlPj6mOqESd8naeXQINckaS28K2FNDSvrTK_3abhXwOh4jLYIlt16f7R3DsXQSiKwhv1XR0_TuDQCIOWbU0Ke2m2MXS_fAp62VJhlTbcpapfKUuDak6Xt0fnxBGzjutWuueX2GsYh0Gpjd6V3BVKDjrTSP3SCenbsvT7iNzX_LHKnYQKcLFWkEZJO57DclutoDGdS-1of-vhm3piTR3EutnWVg5r4PElrIO0-ES3fLLGcO48dkdEaTUY3XZ-UaRkBUoefZKMYC2grAbq4Cmd_NVgnjUPZQDBEq6qR7gpBq8hrCv9lAQ_6oFbwBk6j9NQJCWSBLHFRR5JrTEtZOdTqZ6ahCBxDu0T6rvJXdiwNQg31aY57xE52BfOrouyNoPq3KLwpqAvUnRJU7Gow1VLMik-7yynI8fGzl61OiC9oCtoJqu4zK2AKysb1dCfjJhmpTF2ueicBmRx4yopI7kJwh4M1tB-W13U4vhEmkEOcHwbEfhHYDpEGcT5YGFKDYfhuVIZOhnq9OGFRvbi8ENGe6nM0baUONxACmwP990gY7ZGq_4rLM1Tc6tlZCOwWqfTTE6NpCI_vqAKd6Lwup00g50aMqfdzjeHHC-Y', '^!fudnxt': 'MTc0NjIwNTIwMDAwMA', '^!keys': 'FABvuL1r4bzuXgNozonDd8FuJZZBJYJm8aXI7Wom7_lXE-FSEAVMb5pWC6uSp7ViA9xjlCRA3Ii-Ps9lHNW83ZJ7-0_7JA3azjcnKH_RK9AhyCLAupw84_7gFSaz-fdoI6LU27cuJLv0g4MBz_7-5SrZdoBC71fg9yueCZmPSw8EZn43E6FIfi5O2zuQIatPNJRlIj1a9ZVVImQggcaxoEzsBuP5blk06_jF3SBs-b--bVIGkzjBOMmdmLCRnGSM9wcldao_kjc9_4X_76lk0ZQtKXWn1Rchv1SRF18scfxEIQkSb2e6hLJqrywKt-yZk1rQBk71uCkYPhjDXtdeSKqPZlPULxyN3yUOYTRNxVouOZVNfJqjuvHuDErRyQZ2g9rqQc4SH_xPwbBsXjrymSFTp6fNKSxPseNLppj_zPC650a08rbE5wAHsl0zhjkmnrQjFYhRdH7C747pQqYXeT11QZVzOdPBpEgjiCf8Khd_b9EbTfWh1H8I5QSVwKNUV0YVb9TlM-3z55-UfZfJGqRlHj1SXzo2dg_bWsRM1PlBuwAppw5X-xUaz4Uxr8uPAbX6s4TOrD0f0Q1FfH9vbg2UcWQU7c544KX3lISHBkDvSZBlRnNBn2k9F8KJjZO67YRPxOGh21WyjT-B1W9Ou_Cx-oCF4DUSXqqi2fqF4eWiGmLwJTv78FVwtGVdCoijGq4AHUlRnw4j7r6PiDlwRZNknZresAIUwpbFjPYwfyCqXBbx7X2hDc0IDWdUlJnfOwBiVaMAST4QrB22LkgqaNuUaSDeLQ1yJt43IbwPa8SNbQuy8M47-0-l56Xakt4acU_D3QtsBjaFa3xDumUX4qT2hiQ2vvDlNkrLA919wqEt6hmBVGL-LSVAy33xnFWGct2F1MBQcdw_t-7K4hTG_-o', '^!lang': 'ZW4', '^!obv4': 'AAEBAQEAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAA', '^!prd': 'MDowOjE6MDow', '^!stbmp': 'X2dQVGdHYTdsUW8', '*keyring': 'EMJZq0HpwoWfezH-CLej0M9iArM7QvqNgw7SlfzHZUV57CCl2igJ_vUtADo6bO573x2ytugShWUEYsl5E8JlaTrMIdYDILOTtNJQp9EAU2rrTjGq5F8mZH_zwqnlQUIE2WrnW1BDinyBQW3adXQB3CM', '+puCu255': 'ZmVHJs1OTJLB2zbyVUwbXSrS7QMFK3F8p_QXfIpc7kQ', '+puEd255': 'XihFJ2lVeppnMLChMC6WG6f4d2fzRhzAi3-NBfYekGs', '+sigCu255': 'AAAAAGgIrzMIin9UmwV3KVg2JaLfoZKdU-Na0ABaQHSQvcf2XTc1W8zjBql0u13bJkcLa8-0blzj_3gFDlErs2O9RhV1D9EF', '+sigPubk': 'AAAAAGgIrzPaEVm0Y7NKyrsYZNvlaVhZvoptB5rDyCjkA85CgaAVn7YijQHN9hJTJYPd7WwAkcuhGptbeiPqS3AKApFqqSID', 'firstname': 'SWZlc3MgUFRSSg', 'lastname': 'QmFja3Vw', 'name2': 'SWZlc3MgUFRSSiBCYWNrdXA', 'terms': 'Mg', 'ts': 'uN7Fm9cZp0bzHUCVOxOnb7N3EO1GkQi4ncJQ6nSvymU', 'features': [], 'flags': {'ach': 1, 'bstrg': 21474836480, 'mcs': 1, 'mfae': 1, 'nsre': 1, 'nlfe': 1, 'cspe': 1, 'smsve': 1, 'refpr': 1, 'ff_chmon': 1, 'ssrs': 1, 'aplvp': 1, 'nobp': 1, 'rw': 1, 'ab_fmpup': 1, 'ff_vpnft': 1, 'ff_npp': 1, 'ff_adse': 1, 'ff_npabm': 1, 'ff_refsun': 2, 'ff_iguhm': 1, 'pf': 1}, 'aav': 2, 'ipcc': 'ID', 'aas': 'r4CLpGpSNFMfF44FfOBFQxJfAHpjF-XXD0RAoafRrfI', 'ut': '3amjOoCRrUk'}
2025-04-24 13:49:42,621 - mega_client_py310 - INFO - Storage: 0.00 GB used of 20.00 GB total
