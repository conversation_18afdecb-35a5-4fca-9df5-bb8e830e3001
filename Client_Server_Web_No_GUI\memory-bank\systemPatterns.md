# System Patterns

## Architecture Overview

### Component Structure
```
IFESS Client System
├── ifess_client_hidden.py (Main Service)
│   ├── ClientApp (Core Application Class)
│   ├── NetworkMessage Protocol
│   ├── FirebirdConnector (Database Interface)
│   ├── MegaClient (Cloud Storage)
│   └── GDriveClient (Google Drive Integration)
├── ifess_client_debug.py (Debug GUI)
│   ├── DebugApp (GUI Application)
│   ├── LogMonitor (Real-time Log Viewer)
│   └── StatusTracker (Connection Monitoring)
├── client_config.json (Configuration)
└── Common Modules
    ├── network.py (Message Protocol)
    └── db_utils.py (Database Utilities)
```

### Key Design Patterns

#### 1. Service-Observer Pattern
- **Hidden Client**: Acts as the main service, running continuously
- **Debug Client**: Observes and displays the hidden client's state
- **Decoupled Design**: Debug client can be closed without affecting the service

#### 2. Connection Management Pattern
- **Auto-Reconnect Loop**: Dedicated thread for connection management
- **State Machine**: Clear connection states (disconnected, connecting, connected)
- **Event-Driven**: Connection events trigger appropriate handlers

#### 3. Thread Safety Pattern
- **Mutex Protection**: Global mutex prevents multiple instances
- **Thread Synchronization**: Proper locking for shared resources
- **Daemon Threads**: Background threads that don't prevent application exit

#### 4. Configuration Management Pattern
- **File Watcher**: Monitors config changes and reloads automatically
- **Default Fallbacks**: Graceful handling of missing configuration
- **Validation**: Configuration validation with user feedback

## Current Technical Decisions

### Connection Handling
```python
# Current Simple Retry Logic
def auto_reconnect_loop(self):
    while self.running:
        if not self.connected and not self.is_connecting:
            self.connect_to_server(self.server_address, self.server_port)
            if not self.connected:
                time.sleep(self.reconnect_interval)  # Fixed 5-second interval
```

**Issues with Current Approach:**
- Fixed interval can cause connection storms
- No progressive backoff for persistent failures
- Limited visibility into retry attempts
- No jitter to prevent synchronization

### Message Protocol
```python
class NetworkMessage:
    TYPE_REGISTER = "register"
    TYPE_QUERY = "query"
    TYPE_PING = "ping"
    TYPE_PONG = "pong"
    TYPE_DB_FILE_REQUEST = "db_file_request"
    TYPE_MEGA_UPLOAD = "mega_upload"
    TYPE_GDRIVE_UPLOAD = "gdrive_upload"
```

**Strengths:**
- Clear message type definitions
- JSON-based serialization
- Extensible protocol design

### MEGA Client Integration
```python
# Multiple fallback attempts for MEGA client import
try:
    from mega_client_compiled import MegaClient
except ImportError:
    try:
        from mega_client_patched import MegaClient
    except ImportError:
        # Continue with more fallbacks...
```

**Current Issues:**
- Complex fallback chain is hard to debug
- No clear error reporting for integration failures
- Compatibility issues with Python 3.10+

## Enhanced Patterns to Implement

### 1. Exponential Backoff with Jitter
```python
class ConnectionManager:
    def __init__(self):
        self.base_interval = 5  # seconds
        self.max_interval = 300  # 5 minutes
        self.current_attempt = 0
        self.jitter_range = 0.2  # ±20%
    
    def get_next_interval(self):
        # Exponential backoff: 5, 10, 20, 40, 80, 160, 300
        interval = min(self.base_interval * (2 ** self.current_attempt), self.max_interval)
        # Add jitter: ±20%
        jitter = interval * self.jitter_range * (random.random() * 2 - 1)
        return max(1, interval + jitter)
    
    def reset_backoff(self):
        self.current_attempt = 0
```

### 2. Connection Statistics Tracking
```python
class ConnectionStats:
    def __init__(self):
        self.connection_start_time = None
        self.total_uptime = 0
        self.reconnection_count = 0
        self.last_disconnect_time = None
        self.disconnect_reasons = []
        self.current_backoff_interval = 0
        
    def track_connection_established(self):
        self.connection_start_time = time.time()
        self.reconnection_count += 1
        
    def track_disconnection(self, reason):
        if self.connection_start_time:
            self.total_uptime += time.time() - self.connection_start_time
        self.last_disconnect_time = time.time()
        self.disconnect_reasons.append((time.time(), reason))
```

### 3. Enhanced MEGA Integration
```python
class RobustMegaClient:
    def __init__(self, client_id=None):
        self.client_id = client_id
        self.login_attempts = 0
        self.max_login_attempts = 3
        self.fallback_clients = ['compiled', 'patched', 'fixed', 'py310', 'standard']
        
    def initialize_with_fallback(self):
        for client_type in self.fallback_clients:
            try:
                self.mega_client = self._load_client_type(client_type)
                logger.info(f"Successfully loaded {client_type} MEGA client")
                return True
            except ImportError as e:
                logger.debug(f"Failed to load {client_type} MEGA client: {e}")
                continue
        return False
```

### 4. Enhanced Debug Interface Patterns
```python
class RealTimeStatsDisplay:
    def __init__(self):
        self.connection_stats = ConnectionStats()
        self.update_interval = 1000  # ms
        
    def update_display(self):
        # Real-time connection information
        uptime = self.calculate_uptime()
        reconnection_count = self.connection_stats.reconnection_count
        current_interval = self.connection_stats.current_backoff_interval
        
        # Update UI with current stats
        self.update_connection_labels(uptime, reconnection_count, current_interval)
```

## Component Relationships

### Data Flow
1. **Configuration Loading**: `client_config.json` → `ClientApp.load_config()`
2. **Connection Management**: `ClientApp` → `auto_reconnect_loop()` → `connect_to_server()`
3. **Message Handling**: Server → `receive_messages()` → `process_message()` → Specific handlers
4. **Status Updates**: `ClientApp` state changes → Debug interface observers
5. **Cloud Operations**: Upload requests → MEGA/GDrive clients → Progress callbacks

### Thread Architecture
```
Main Thread
├── GUI Thread (Debug Client only)
├── Auto-Reconnect Thread
├── Message Receive Thread
├── Config Watcher Thread
├── Token Status Check Thread (Background)
└── Upload Progress Threads (As needed)
```

### Error Propagation
1. **Network Errors**: Socket exceptions → Connection state updates → UI notifications
2. **Database Errors**: SQL execution failures → Error logging → Client error responses
3. **Cloud Errors**: Upload failures → Retry mechanisms → Status updates
4. **Configuration Errors**: Validation failures → User notifications → Fallback defaults

## Security Patterns

### Credential Management
- Configuration file contains sensitive data (passwords, tokens)
- File permissions should restrict access to necessary users only
- Consider encryption for sensitive configuration values

### Network Security
- Socket connections use standard TCP (no encryption by default)
- Client authentication via registration messages
- Consider implementing SSL/TLS for production deployments

### Input Validation
- All incoming messages validated against expected schema
- Database queries parameterized to prevent injection
- File paths validated to prevent directory traversal

## Performance Patterns

### Resource Management
- Connection pooling for database operations
- Thread pool management for concurrent operations
- Memory-efficient log rotation (5MB files, 3 backups)

### Optimization Strategies
- Lazy loading of cloud clients until needed
- Background token refresh to avoid blocking operations
- Chunked file transfers for large database backups 