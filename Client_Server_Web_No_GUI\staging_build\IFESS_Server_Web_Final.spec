# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['run_server_final.py'],
    pathex=[],
    binaries=[],
    datas=[('server_web/templates', 'server_web/templates'), ('server_web/static', 'server_web/static'), ('server_web/common', 'server_web/common')],
    hiddenimports=['flask', 'flask_cors', 'fdb', 'webbrowser'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='IFESS_Server_Web_Final',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
