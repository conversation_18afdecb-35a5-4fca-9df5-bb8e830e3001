import fdb
import os
import sys

# Add parent directory to path to import common modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Database connection parameters
db_path = r"C:\Gawean Rebinmas\Monitoring Database\Ifess Monitoring\PTRJ_P1A_08042025\PTRJ_P1A.FDB"
username = "sysdba"
password = "masterkey"

# The monitoring query
query = """
SELECT 
  o.EMPID AS KaryawanID,
  o.ID AS OvertimeID,
  o.INPDATE AS TanggalOvertime,
  o.HOURS AS JamKerja,
  SUBSTRING(f.FIELDNO FROM 1 FOR 6) AS KodeField,
  (j.EXPTYPE || j.ITEMNO || j.SUBNO) AS AccCode,
  j.DESCRIPTION AS DeskripsiPekerjaan,
  o.BASICRATE AS TarifDasar,
  o.ADDRATE AS TarifTambahan,
  o.HOURS * o.BASICRATE AS NilaiDasar,
  o.HOURS * o.ADDRATE AS NilaiTambahan,
  o.REMARKS AS Catatan,
  SUBSTRING(f.FIELDNO FROM 1 FOR 2) AS KodeField_2Char,
  SUBSTRING(j.EXPTYPE FROM 1 FOR 2) AS AccCode_2Char,
  a.VEHNO AS NomorKendaraan,
  a.MODEL AS ModelKendaraan,
  o.VEHID AS VehicleID
FROM OVERTIME o
JOIN JOBCODE j ON o.JOBID = j.ID
JOIN OCFIELD f ON o.FIELDID = f.ID
LEFT JOIN VEHCODE a ON o.VEHID = a.ID
WHERE o.INPDATE BETWEEN 
  CAST(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
       EXTRACT(MONTH FROM CURRENT_DATE) || '-01' AS DATE) 
  AND
  CAST(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
       EXTRACT(MONTH FROM CURRENT_DATE) || '-' || 
       EXTRACT(DAY FROM CAST(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
                         (EXTRACT(MONTH FROM CURRENT_DATE) + 1) || '-01' AS DATE) - 1) AS DATE)
  AND (
    -- Filter 1: AccCode tidak dimulai dengan 'PT' atau 'GA', 2 karakter pertama berbeda
    (
      SUBSTRING(j.EXPTYPE FROM 1 FOR 2) NOT IN ('PT', 'GA')
      AND SUBSTRING(f.FIELDNO FROM 1 FOR 2) <> SUBSTRING(j.EXPTYPE FROM 1 FOR 2)
    )
    OR
    -- Filter 2: AccCode dimulai dengan 'PT', VEHID tidak valid
    (
      SUBSTRING(j.EXPTYPE FROM 1 FOR 2) = 'PT'
      AND a.ID IS NULL
    )
    OR
    -- Filter 3: AccCode dimulai dengan 'GA', kecuali GA9110 dengan KodeField 'YY'
    (
      SUBSTRING(j.EXPTYPE FROM 1 FOR 2) = 'GA'
      AND NOT (
        j.EXPTYPE = 'GA' AND j.ITEMNO = '91' AND j.SUBNO = '10'
        AND SUBSTRING(f.FIELDNO FROM 1 FOR 2) = 'YY'
      )
    )
  )
  AND NOT (
    -- Pengecualian tambahan: AccCode 'CL4310' dengan KodeField 'YYYY'
    j.EXPTYPE = 'CL' AND j.ITEMNO = '43' AND j.SUBNO = '10'
    AND SUBSTRING(f.FIELDNO FROM 1 FOR 4) = 'YYYY'
  )
ORDER BY o.EMPID, o.INPDATE
"""

try:
    # Connect to the database
    print(f"Connecting to database: {db_path}")
    conn = fdb.connect(
        dsn=db_path,
        user=username,
        password=password
    )
    
    # Create a cursor
    cursor = conn.cursor()
    
    # Execute the query
    print("Executing query...")
    cursor.execute(query)
    
    # Fetch the results
    rows = cursor.fetchall()
    
    # Get column names
    column_names = [desc[0] for desc in cursor.description]
    
    # Print the results
    print(f"\nQuery Results ({len(rows)} rows):")
    print("-" * 80)
    
    # Print column headers
    print(" | ".join(column_names))
    print("-" * 80)
    
    # Print rows
    for row in rows:
        print(" | ".join(str(val) for val in row))
    
    # Close the cursor and connection
    cursor.close()
    conn.close()
    
    print("\nQuery executed successfully!")
    
except Exception as e:
    print(f"Error: {e}")

input("\nPress Enter to exit...")
