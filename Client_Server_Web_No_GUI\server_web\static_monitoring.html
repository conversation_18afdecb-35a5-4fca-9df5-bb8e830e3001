<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .card {
            margin-bottom: 20px;
        }
        .table-responsive {
            max-height: 500px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Monitoring Dashboard</h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Monitoring Dashboard</strong> allows you to run predefined SQL queries to monitor database health and identify issues.
            Select a query from the left panel to run it on all connected clients.
        </div>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>Monitoring Queries
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group" id="monitoring-queries">
                            <div class="list-group-item active">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">monitor_diff_job_field</h6>
                                    <button type="button" class="btn btn-sm btn-light monitoring-query" data-query-name="monitor_diff_job_field">
                                        <i class="fas fa-play me-1"></i>Run
                                    </button>
                                </div>
                                <p class="mb-0 small text-white">
                                    Query untuk menemukan data yang salah di bulan berjalan (bulan saat ini). Monitoring query to find inconsistencies between job codes and field codes.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i>Monitoring Results
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="monitoring-results">
                            <h4 class="mb-3">Results for: monitor_diff_job_field</h4>
                            <div class="table-responsive mb-4">
                                <table class="table table-striped table-bordered table-hover">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>KaryawanID</th>
                                            <th>OvertimeID</th>
                                            <th>TanggalOvertime</th>
                                            <th>JamKerja</th>
                                            <th>KodeField</th>
                                            <th>AccCode</th>
                                            <th>DeskripsiPekerjaan</th>
                                            <th>TarifDasar</th>
                                            <th>TarifTambahan</th>
                                            <th>NilaiDasar</th>
                                            <th>NilaiTambahan</th>
                                            <th>Catatan</th>
                                            <th>KodeField_2Char</th>
                                            <th>AccCode_2Char</th>
                                            <th>NomorKendaraan</th>
                                            <th>ModelKendaraan</th>
                                            <th>VehicleID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>23</td>
                                            <td>53204</td>
                                            <td>2025-04-07</td>
                                            <td>2.00</td>
                                            <td>YYYYY</td>
                                            <td>GA9234</td>
                                            <td>UPKEEP OF BUILDINGS</td>
                                            <td>10000</td>
                                            <td>5000</td>
                                            <td>20000</td>
                                            <td>10000</td>
                                            <td>Test data</td>
                                            <td>YY</td>
                                            <td>GA</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td>23</td>
                                            <td>53210</td>
                                            <td>2025-04-07</td>
                                            <td>2.00</td>
                                            <td>YYYYY</td>
                                            <td>GA9234</td>
                                            <td>UPKEEP OF BUILDINGS</td>
                                            <td>10000</td>
                                            <td>5000</td>
                                            <td>20000</td>
                                            <td>10000</td>
                                            <td>Test data 2</td>
                                            <td>YY</td>
                                            <td>GA</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-muted mb-4">
                                <i class="fas fa-info-circle me-1"></i>Showing 2 rows.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
