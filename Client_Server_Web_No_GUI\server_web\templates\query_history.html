{% extends 'layout.html' %}

{% block title %}Query History - Firebird Query Server{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Query History</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>Recent Queries
                </h5>
            </div>
            <div class="card-body">
                {% if query_history %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="history-table">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Query</th>
                                <th>Target Clients</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in query_history|reverse %}
                            <tr>
                                <td>{{ entry.timestamp }}</td>
                                <td>
                                    <div class="query-preview">{{ entry.query[:50] }}{% if entry.query|length > 50 %}...{% endif %}</div>
                                </td>
                                <td>
                                    {% if entry.target_clients %}
                                    {{ entry.target_clients|length }} client(s)
                                    {% else %}
                                    All clients
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-primary view-query" data-query="{{ entry.query }}">
                                            <i class="fas fa-eye"></i> View
                                        </button>
                                        <button class="btn btn-sm btn-success rerun-query" data-query="{{ entry.query }}" data-clients="{{ entry.target_clients|tojson }}">
                                            <i class="fas fa-redo"></i> Re-run
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No query history available. Execute queries to see them here.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- View Query Modal -->
<div class="modal fade" id="viewQueryModal" tabindex="-1" aria-labelledby="viewQueryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="viewQueryModalLabel">Query Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <pre id="view-query-content" class="bg-light p-3 border rounded"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-success" id="copy-query-btn">
                    <i class="fas fa-copy me-1"></i>Copy to Clipboard
                </button>
                <button type="button" class="btn btn-primary" id="edit-query-btn">
                    <i class="fas fa-edit me-1"></i>Edit & Run
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Save Query Modal -->
<div class="modal fade" id="saveQueryModal" tabindex="-1" aria-labelledby="saveQueryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="saveQueryModalLabel">Save Query</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="save-query-form">
                    <div class="mb-3">
                        <label for="query-name" class="form-label">Query Name:</label>
                        <input type="text" class="form-control" id="query-name" name="query_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="query-description" class="form-label">Description (optional):</label>
                        <textarea class="form-control" id="query-description" name="query_description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="save-query-confirm">Save</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // View query button
        $('.view-query').click(function() {
            const query = $(this).data('query');
            
            // Show modal
            $('#view-query-content').text(query);
            
            const modal = new bootstrap.Modal(document.getElementById('viewQueryModal'));
            modal.show();
        });

        // Copy query to clipboard
        $('#copy-query-btn').click(function() {
            const query = $('#view-query-content').text();
            
            // Copy to clipboard
            navigator.clipboard.writeText(query).then(function() {
                // Show success message
                alert('Query copied to clipboard.');
            }, function() {
                // Fallback for older browsers
                const textarea = document.createElement('textarea');
                textarea.value = query;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('Query copied to clipboard.');
            });
        });

        // Edit and run query
        $('#edit-query-btn').click(function() {
            const query = $('#view-query-content').text();
            
            // Redirect to query page with query parameter
            window.location.href = '/query?query=' + encodeURIComponent(query);
        });

        // Re-run query button
        $('.rerun-query').click(function() {
            const query = $(this).data('query');
            const clients = $(this).data('clients');
            
            // Confirm re-run
            if (confirm('Are you sure you want to re-run this query?')) {
                // Send query
                $.post('/api/query/send', {
                    query: query,
                    clients: clients
                }, function(data) {
                    if (data.success) {
                        // Show success message
                        alert('Query re-executed successfully. Check the Query page for results.');
                    } else {
                        alert('Failed to re-execute query: ' + data.message);
                    }
                }).fail(function() {
                    alert('Failed to re-execute query. Please try again.');
                });
            }
        });

        // Save query button (in view modal)
        $('#save-query-btn').click(function() {
            const query = $('#view-query-content').text();
            
            // Show save query modal
            $('#save-query-form')[0].reset();
            
            const modal = new bootstrap.Modal(document.getElementById('saveQueryModal'));
            modal.show();
        });

        // Save query confirm button
        $('#save-query-confirm').click(function() {
            const queryName = $('#query-name').val().trim();
            const queryDescription = $('#query-description').val().trim();
            const query = $('#view-query-content').text();
            
            if (!queryName) {
                alert('Please enter a name for the query.');
                return;
            }
            
            // Save query
            $.post('/api/query/save', {
                name: queryName,
                description: queryDescription,
                query: query
            }, function(data) {
                if (data.success) {
                    // Hide modal
                    bootstrap.Modal.getInstance(document.getElementById('saveQueryModal')).hide();
                    
                    // Show success message
                    alert('Query saved successfully.');
                    
                    // Reset form
                    $('#query-name').val('');
                    $('#query-description').val('');
                } else {
                    alert('Failed to save query: ' + data.message);
                }
            }).fail(function() {
                alert('Failed to save query. Please try again.');
            });
        });
    });
</script>
{% endblock %}
