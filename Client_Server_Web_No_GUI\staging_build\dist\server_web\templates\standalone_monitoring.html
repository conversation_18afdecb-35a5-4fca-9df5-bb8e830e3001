<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Monitoring Dashboard</h1>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Monitoring Dashboard</strong> allows you to run predefined SQL queries to monitor database health and identify issues.
            Select a query from the left panel to run it on all connected clients.
        </div>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>Monitoring Queries
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group" id="monitoring-queries">
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">monitor_diff_job_field</h6>
                                    <button type="button" class="btn btn-sm btn-primary monitoring-query" data-query-name="monitor_diff_job_field">
                                        <i class="fas fa-play me-1"></i>Run
                                    </button>
                                </div>
                                <p class="mb-0 small text-muted">
                                    Query untuk menemukan data yang salah di bulan berjalan (bulan saat ini). Monitoring query to find inconsistencies between job codes and field codes.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i>Monitoring Results
                            <div class="spinner-border spinner-border-sm text-light ms-2 d-none" id="monitoring-loading" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="monitoring-results">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>Select a monitoring query from the left panel to run it.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Run monitoring query
            $('.monitoring-query').click(function() {
                const queryName = $(this).data('query-name');
                
                // Show loading indicator
                $('#monitoring-loading').removeClass('d-none');
                
                // Clear previous results
                $('#monitoring-results').html('');
                
                // Run query
                $.get('/api/run_query/' + queryName, function(data) {
                    // Hide loading indicator
                    $('#monitoring-loading').addClass('d-none');
                    
                    if (data.success) {
                        const results = data.results;
                        let html = `<h4 class="mb-3">Results for: ${queryName}</h4>`;
                        
                        if (results.rows && results.rows.length > 0) {
                            html += `
                                <div class="table-responsive mb-4">
                                    <table class="table table-striped table-bordered table-hover">
                                        <thead class="table-primary">
                                            <tr>
                            `;
                            
                            // Add headers
                            results.headers.forEach(header => {
                                html += `<th>${header}</th>`;
                            });
                            
                            html += `
                                            </tr>
                                        </thead>
                                        <tbody>
                            `;
                            
                            // Add rows
                            results.rows.forEach(row => {
                                html += '<tr>';
                                results.headers.forEach(header => {
                                    const value = row[header] !== null && row[header] !== undefined ? row[header] : '';
                                    html += `<td>${value}</td>`;
                                });
                                html += '</tr>';
                            });
                            
                            html += `
                                        </tbody>
                                    </table>
                                </div>
                            `;
                            
                            // Add row count
                            html += `
                                <div class="text-muted mb-4">
                                    <i class="fas fa-info-circle me-1"></i>Showing ${results.rows.length} rows.
                                </div>
                            `;
                        } else {
                            html += `
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>No results found for this query.
                                </div>
                            `;
                        }
                        
                        $('#monitoring-results').html(html);
                    } else {
                        $('#monitoring-results').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>${data.message}
                            </div>
                        `);
                    }
                }).fail(function() {
                    // Hide loading indicator
                    $('#monitoring-loading').addClass('d-none');
                    
                    // Show error message
                    $('#monitoring-results').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>Failed to run monitoring query. Please try again.
                        </div>
                    `);
                });
            });
        });
    </script>
</body>
</html>