@echo off
echo Starting IFESS Configuration GUI with Google Drive Token Support

REM Check if Python is available
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found. Please install Python.
    pause
    exit /b 1
)

REM Install required dependencies
echo Installing required dependencies...
python -m pip install requests python-dateutil google-auth google-auth-oauthlib google-api-python-client
if %errorlevel% neq 0 (
    echo Failed to install some dependencies, but continuing anyway.
    echo Some features may not work properly.
)

REM Start the configuration GUI
echo Starting IFESS Configuration GUI...
python ifess_config_gui.py

echo.
echo Press any key to exit...
pause > nul
