# IFESS Google Drive Auto-Login

Dokumen ini menjelaskan fitur auto-login untuk Google Drive di aplikasi IFESS.

## Fitur

1. **Auto-Login Google Drive**
   - Login otomatis ke Google Drive dengan akun pribadi (<EMAIL>)
   - Auto-refresh token OAuth yang kedaluwarsa
   - File disimpan di folder pribadi dan dimiliki oleh akun pribadi
   - Struktur folder: Backup_PTRJ/IFESS/{client_id}/{client_id}_date_time.zip

2. **Tiga Opsi Autentikasi**
   - `gdrive_client_simple.py`: Menggunakan file kredensial yang sudah ada (client_secret.json)
   - `gdrive_client_oauth.py`: Menggunakan OAuth dengan auto-login (memerlukan Selenium)
   - `gdrive_client_module.py`: Menggunakan service account (fallback)

## Cara Menggunakan

### Menjalankan Aplikasi dengan Auto-Login

Gunakan script `run_with_auto_login.bat` untuk menjalankan aplikasi dengan dukungan auto-login:

```
run_with_auto_login.bat
```

Script ini akan:
- Menginstal dependensi yang diperlukan (termasuk Selenium)
- Mengunduh ChromeDriver untuk Selenium
- Menjalankan server web, client hidden, dan client debug

### Upload ke Google Drive

1. Buka antarmuka web server di http://localhost:5000
2. Navigasi ke halaman "Backups"
3. Pilih client dari dropdown
4. Klik tombol "Upload to GDrive" untuk file yang ingin diupload
5. Jika menggunakan auto-login, browser akan terbuka dan login otomatis dengan akun Google pribadi

## Konfigurasi

### File Kredensial

Aplikasi akan mencari file kredensial dalam urutan berikut:

1. `client_secret.json` di direktori yang sama dengan aplikasi
2. File yang ditentukan dalam `gdrive_client_config.json`
3. File di direktori `Client_Transfer_Gdrive`

### Selenium untuk Auto-Login

Untuk menggunakan fitur auto-login, Anda memerlukan:

1. Selenium: `pip install selenium`
2. WebDriver Manager: `pip install webdriver-manager`
3. Chrome browser

## Troubleshooting

### Masalah "invalid_client"

Jika Anda melihat error "The OAuth client was not found" atau "Error 401: invalid_client":

1. Pastikan file `client_secrets.json` ada dan valid
2. Jalankan `run_with_auto_login.bat` untuk menginstal dependensi yang diperlukan
3. Jika masih bermasalah, coba gunakan `gdrive_client_simple.py` yang menggunakan file kredensial yang sudah ada

### Masalah Auto-Login

Jika auto-login tidak berfungsi:

1. Pastikan Chrome browser terinstal
2. Pastikan Selenium dan WebDriver Manager terinstal
3. Jika masih bermasalah, aplikasi akan fallback ke metode autentikasi manual

## Informasi Debug

Aplikasi debug (ifess_client_debug.py) menampilkan informasi tentang status upload Google Drive. Gunakan aplikasi ini untuk memantau proses upload dan melihat pesan error jika terjadi masalah.
