"""
Helper module for MEGA debug information
"""

import os
import sys
import re
import logging
import traceback

logger = logging.getLogger("MEGA-Debug")

def extract_mega_info_from_log(log_content):
    """
    Extract MEGA-related information from log content
    Returns a dictionary with MEGA status information
    """
    mega_info = {
        'status': 'Unknown',
        'last_action': None,
        'errors': [],
        'uploads': [],
        'login_status': 'Unknown',
        'last_error': None,
        'progress': 0
    }
    
    try:
        # Check if MEGA is available
        if "MEGA functionality is disabled" in log_content:
            mega_info['status'] = 'Disabled'
            mega_info['login_status'] = 'Disabled'
            
            # Find reason
            disabled_match = re.search(r"MEGA functionality is disabled[^\\n]*", log_content)
            if disabled_match:
                mega_info['last_error'] = disabled_match.group(0)
            
            return mega_info
        
        # Check login status
        if "Successfully logged in to MEGA" in log_content:
            mega_info['login_status'] = 'Logged In'
            mega_info['status'] = 'Ready'
            
            # Find login details
            login_match = re.search(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+Successfully logged in to MEGA as ([^\\n]+)", log_content)
            if login_match:
                mega_info['last_action'] = f"Logged in as {login_match.group(2)} at {login_match.group(1)}"
        
        elif "Login failed" in log_content:
            mega_info['login_status'] = 'Login Failed'
            mega_info['status'] = 'Error'
            
            # Find login error
            login_error_match = re.search(r"Login failed: ([^\\n]+)", log_content)
            if login_error_match:
                mega_info['last_error'] = f"Login failed: {login_error_match.group(1)}"
                mega_info['errors'].append(mega_info['last_error'])
        
        # Check for upload requests
        upload_requests = re.findall(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+Received MEGA upload request for file: ([^\\n]+)", log_content)
        if upload_requests:
            mega_info['status'] = 'Upload Requested'
            last_request = upload_requests[-1]
            mega_info['last_action'] = f"Upload requested for {os.path.basename(last_request[1])} at {last_request[0]}"
            
            for request in upload_requests:
                mega_info['uploads'].append({
                    'time': request[0],
                    'file': request[1],
                    'status': 'Requested'
                })
        
        # Check for upload start
        upload_starts = re.findall(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+Starting upload of ([^\\n]+) to MEGA", log_content)
        if upload_starts:
            mega_info['status'] = 'Uploading'
            last_start = upload_starts[-1]
            mega_info['last_action'] = f"Started uploading {os.path.basename(last_start[1])} at {last_start[0]}"
            
            # Update uploads list
            for start in upload_starts:
                file_name = os.path.basename(start[1])
                found = False
                for upload in mega_info['uploads']:
                    if file_name in upload['file']:
                        upload['status'] = 'Uploading'
                        break
                if not found:
                    mega_info['uploads'].append({
                        'time': start[0],
                        'file': start[1],
                        'status': 'Uploading'
                    })
        
        # Check for upload completion
        upload_completes = re.findall(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+✓ Uploaded ([^\\n]+) successfully", log_content)
        if upload_completes:
            mega_info['status'] = 'Upload Complete'
            last_complete = upload_completes[-1]
            mega_info['last_action'] = f"Completed uploading {last_complete[1]} at {last_complete[0]}"
            mega_info['progress'] = 100
            
            # Update uploads list
            for complete in upload_completes:
                file_name = complete[1]
                found = False
                for upload in mega_info['uploads']:
                    if file_name in upload['file']:
                        upload['status'] = 'Completed'
                        break
                if not found:
                    mega_info['uploads'].append({
                        'time': complete[0],
                        'file': file_name,
                        'status': 'Completed'
                    })
        
        # Check for upload errors
        upload_errors = re.findall(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+✗ Error uploading ([^:]+): ([^\\n]+)", log_content)
        if upload_errors:
            mega_info['status'] = 'Upload Failed'
            last_error = upload_errors[-1]
            mega_info['last_error'] = f"Error uploading {last_error[1]}: {last_error[2]}"
            mega_info['last_action'] = f"Failed uploading {last_error[1]} at {last_error[0]}"
            mega_info['errors'].append(mega_info['last_error'])
            
            # Update uploads list
            for error in upload_errors:
                file_name = error[1]
                found = False
                for upload in mega_info['uploads']:
                    if file_name in upload['file']:
                        upload['status'] = 'Failed'
                        upload['error'] = error[2]
                        break
                if not found:
                    mega_info['uploads'].append({
                        'time': error[0],
                        'file': file_name,
                        'status': 'Failed',
                        'error': error[2]
                    })
        
        # Check for asyncio.coroutine errors
        if "asyncio.coroutine" in log_content and "AttributeError" in log_content:
            mega_info['status'] = 'Error'
            mega_info['last_error'] = "asyncio.coroutine error - Python 3.10+ compatibility issue"
            mega_info['errors'].append(mega_info['last_error'])
        
        return mega_info
        
    except Exception as e:
        logger.error(f"Error extracting MEGA info from log: {e}")
        logger.error(traceback.format_exc())
        mega_info['status'] = 'Error'
        mega_info['last_error'] = f"Error parsing log: {str(e)}"
        return mega_info
