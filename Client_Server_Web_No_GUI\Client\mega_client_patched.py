"""
MEGA Cloud Storage Client Module with Patch for Python 3.10+

This module provides functionality for uploading database files to MEGA cloud storage.
It includes a patch for the asyncio.coroutine deprecation in Python 3.10+.
"""

import os
import sys
import time
import json
import base64
import logging
import threading
import traceback

# Apply patch for asyncio.coroutine before importing mega
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    # Import the patch module
    import mega_patch
    PATCH_APPLIED = True
except ImportError:
    print("Warning: mega_patch.py not found, MEGA functionality may not work in Python 3.10+")
    PATCH_APPLIED = False

# Now import Mega with the patch applied
try:
    from mega import Mega
    MEGA_AVAILABLE = True
except ImportError as e:
    print(f"Error importing Mega: {e}")
    print("MEGA functionality will be disabled")
    MEGA_AVAILABLE = False
    Mega = None
except Exception as e:
    print(f"Unexpected error importing Mega: {e}")
    print(f"Error details: {traceback.format_exc()}")
    print("MEGA functionality will be disabled")
    MEGA_AVAILABLE = False
    Mega = None

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.network import NetworkMessage, send_message

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mega_client.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MEGA account credentials
EMAIL = "<EMAIL>"
PASSWORD = "ptrj@123"

class MegaClient:
    """Client for uploading files to MEGA cloud storage"""
    
    def __init__(self, client_id=None):
        """Initialize the MEGA client"""
        self.client_id = client_id
        self.mega_api = None
        self.logged_in = False
        self.last_error = None
        self.upload_progress = 0
        self.upload_status = "Not initialized"
        
        # Check if MEGA is available
        if not MEGA_AVAILABLE:
            self.last_error = "MEGA functionality is disabled due to import errors"
            self.upload_status = "Disabled"
            logger.warning(self.last_error)
            return
        
        # Log patch status
        if PATCH_APPLIED:
            logger.info("MEGA patch for asyncio.coroutine was applied")
        else:
            logger.warning("MEGA patch for asyncio.coroutine was NOT applied, may cause issues in Python 3.10+")
        
        # Try to login to MEGA
        self.login_mega()
    
    def login_mega(self):
        """Login to MEGA account"""
        if not MEGA_AVAILABLE:
            self.last_error = "Cannot login to MEGA: MEGA functionality is disabled"
            self.upload_status = "Disabled"
            logger.error(self.last_error)
            return False
            
        try:
            logger.info("Connecting to MEGA...")
            self.upload_status = "Connecting"
            
            # Initialize MEGA API
            mega = Mega()
            
            # Try to login
            try:
                self.mega_api = mega.login(EMAIL, PASSWORD)
                self.logged_in = True
                self.upload_status = "Connected"
                logger.info(f"Successfully logged in to MEGA as {EMAIL}")
                
                # Get account details
                user_details = self.mega_api.get_user()
                if user_details:
                    logger.info(f"Logged in as: {user_details}")
                
                # Get storage space info
                space_info = self.mega_api.get_storage_space(giga=True)
                if space_info:
                    used = space_info['used']
                    total = space_info['total']
                    logger.info(f"Storage: {used:.2f} GB used of {total:.2f} GB total")
                
                return True
                
            except Exception as e:
                self.last_error = f"Login failed: {str(e)}"
                self.upload_status = "Login Failed"
                logger.error(self.last_error)
                logger.error(traceback.format_exc())
                return False
        
        except Exception as e:
            self.last_error = f"Error initializing MEGA API: {str(e)}"
            self.upload_status = "Initialization Failed"
            logger.error(self.last_error)
            logger.error(traceback.format_exc())
            return False
    
    def upload_file_to_mega(self, file_path):
        """Upload a file to MEGA"""
        if not MEGA_AVAILABLE:
            self.last_error = "Cannot upload to MEGA: MEGA functionality is disabled"
            self.upload_status = "Disabled"
            logger.error(self.last_error)
            return {
                'success': False,
                'error': self.last_error,
                'file_name': os.path.basename(file_path) if file_path else "unknown"
            }
            
        file_name = os.path.basename(file_path)
        
        try:
            # Check if file exists and is readable
            if not os.path.exists(file_path):
                self.last_error = f"File not found: {file_path}"
                self.upload_status = "File Not Found"
                raise FileNotFoundError(self.last_error)
            
            file_size = os.path.getsize(file_path)
            logger.info(f"Uploading file: {file_name} ({file_size} bytes)")
            self.upload_status = "Uploading"
            self.upload_progress = 0
            
            # Make sure we're logged in
            if not self.logged_in:
                logger.info("Not logged in, attempting to login...")
                self.upload_status = "Logging In"
                if not self.login_mega():
                    self.last_error = "Failed to login to MEGA"
                    self.upload_status = "Login Failed"
                    raise Exception(self.last_error)
            
            # Upload file to MEGA
            # The upload method returns the file node ID
            logger.info(f"Starting upload of {file_name} to MEGA...")
            self.upload_status = "Uploading"
            
            # Log detailed information about the upload
            logger.info(f"Upload details:")
            logger.info(f"  File: {file_path}")
            logger.info(f"  Size: {file_size} bytes ({file_size/1024/1024:.2f} MB)")
            logger.info(f"  MEGA account: {EMAIL}")
            
            # Perform the upload
            file_node = self.mega_api.upload(file_path)
            
            if file_node:
                logger.info(f"✓ Uploaded {file_name} successfully")
                logger.info(f"  Node ID: {file_node}")
                self.upload_status = "Completed"
                self.upload_progress = 100
                
                # Try to get a link to the file
                try:
                    file_link = self.mega_api.get_upload_link(file_node)
                    logger.info(f"  Link: {file_link}")
                    return {
                        'success': True,
                        'file_name': file_name,
                        'file_size': file_size,
                        'node_id': file_node,
                        'link': file_link
                    }
                except Exception as e:
                    logger.warning(f"Could not get link: {str(e)}")
                    return {
                        'success': True,
                        'file_name': file_name,
                        'file_size': file_size,
                        'node_id': file_node,
                        'link': None
                    }
            else:
                self.last_error = "Upload failed, no file node returned"
                self.upload_status = "Failed"
                raise Exception(self.last_error)
            
        except Exception as e:
            self.last_error = f"Error uploading {file_name}: {str(e)}"
            self.upload_status = "Failed"
            logger.error(f"✗ {self.last_error}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'file_name': file_name,
                'error': str(e)
            }
    
    def handle_mega_upload_request(self, socket, message):
        """Handle a request to upload a file to MEGA"""
        try:
            # Check if MEGA is available
            if not MEGA_AVAILABLE:
                self.last_error = "Cannot handle MEGA upload request: MEGA functionality is disabled"
                self.upload_status = "Disabled"
                logger.error(self.last_error)
                response = NetworkMessage(
                    NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                    {
                        'success': False,
                        'error': self.last_error,
                        'request_id': message.data.get('request_id') if hasattr(message, 'data') and isinstance(message.data, dict) else None
                    },
                    self.client_id
                )
                send_message(socket, response)
                return
                
            # Extract data from message
            data = message.data
            file_path = data.get('file_path')
            request_id = data.get('request_id')
            
            if not file_path:
                self.last_error = "No file path provided in upload request"
                self.upload_status = "Invalid Request"
                logger.error(self.last_error)
                # Send error response
                response = NetworkMessage(
                    NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                    {
                        'success': False,
                        'error': self.last_error,
                        'request_id': request_id
                    },
                    self.client_id
                )
                send_message(socket, response)
                return
            
            logger.info(f"Received MEGA upload request for file: {file_path}")
            self.upload_status = "Request Received"
            
            # Send acknowledgment
            ack_message = NetworkMessage(
                NetworkMessage.TYPE_MEGA_UPLOAD_ACK,
                {
                    'file_path': file_path,
                    'request_id': request_id,
                    'timestamp': time.time()
                },
                self.client_id
            )
            send_message(socket, ack_message)
            logger.info(f"Sent acknowledgment for MEGA upload request")
            
            # Start upload in a separate thread
            def upload_thread():
                try:
                    # Send progress update
                    self.upload_status = "Starting"
                    self.upload_progress = 0
                    progress_message = NetworkMessage(
                        NetworkMessage.TYPE_MEGA_UPLOAD_PROGRESS,
                        {
                            'file_path': file_path,
                            'request_id': request_id,
                            'progress': 0,
                            'status': 'starting',
                            'timestamp': time.time()
                        },
                        self.client_id
                    )
                    send_message(socket, progress_message)
                    logger.info(f"Starting MEGA upload for {file_path}")
                    
                    # Upload file
                    result = self.upload_file_to_mega(file_path)
                    
                    # Add request_id to result
                    result['request_id'] = request_id
                    
                    # Send result
                    if result.get('success', False):
                        logger.info(f"MEGA upload completed successfully for {file_path}")
                        self.upload_status = "Completed"
                        self.upload_progress = 100
                    else:
                        logger.error(f"MEGA upload failed for {file_path}: {result.get('error', 'Unknown error')}")
                        self.upload_status = "Failed"
                    
                    result_message = NetworkMessage(
                        NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                        result,
                        self.client_id
                    )
                    send_message(socket, result_message)
                    logger.info(f"Sent MEGA upload result to server")
                    
                except Exception as e:
                    self.last_error = f"Error in upload thread: {str(e)}"
                    self.upload_status = "Thread Error"
                    logger.error(self.last_error)
                    logger.error(traceback.format_exc())
                    
                    # Send error result
                    error_message = NetworkMessage(
                        NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                        {
                            'success': False,
                            'error': str(e),
                            'request_id': request_id
                        },
                        self.client_id
                    )
                    send_message(socket, error_message)
            
            # Start upload thread
            logger.info(f"Starting upload thread for {file_path}")
            threading.Thread(target=upload_thread, daemon=True).start()
            
        except Exception as e:
            self.last_error = f"Error handling MEGA upload request: {str(e)}"
            self.upload_status = "Handler Error"
            logger.error(self.last_error)
            logger.error(traceback.format_exc())
            
            # Try to send error response
            try:
                error_message = NetworkMessage(
                    NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                    {
                        'success': False,
                        'error': str(e),
                        'request_id': data.get('request_id') if isinstance(data, dict) else None
                    },
                    self.client_id
                )
                send_message(socket, error_message)
            except:
                logger.error("Failed to send error response")

    def get_status(self):
        """Get current status information"""
        return {
            'available': MEGA_AVAILABLE,
            'logged_in': self.logged_in,
            'status': self.upload_status,
            'progress': self.upload_progress,
            'last_error': self.last_error,
            'patch_applied': PATCH_APPLIED
        }

# For testing
if __name__ == "__main__":
    client = MegaClient()
    print(f"MEGA Status: {client.get_status()}")
    
    if client.logged_in:
        print("Successfully logged in to MEGA")
        
        # Test upload
        test_file = input("Enter path to file for testing upload: ")
        if os.path.exists(test_file):
            result = client.upload_file_to_mega(test_file)
            print(f"Upload result: {result}")
        else:
            print(f"File not found: {test_file}")
    else:
        print(f"Failed to login to MEGA: {client.last_error}")
