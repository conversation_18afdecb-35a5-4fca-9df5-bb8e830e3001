# IFESS Server Web Application

This is the IFESS Server Web Application that allows you to manage clients, execute queries, and view results.

## Requirements

- Python 3.10 or higher
- Flask
- Flask-CORS
- FDB (Firebird Database API)

## Running the Application

1. Install the required packages:

   ```
   pip install flask flask_cors fdb
   ```

2. Run the Start_IFESS_Server.bat file
3. Access the application at http://localhost:5000 in your web browser
