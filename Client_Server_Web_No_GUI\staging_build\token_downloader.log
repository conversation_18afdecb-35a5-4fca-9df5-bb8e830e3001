2025-04-28 21:23:47,915 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 21:23:47,916 - token_downloader - INFO - Token file not found, downloading from Google Drive
2025-04-28 21:23:47,916 - token_downloader - INFO - Downloading latest token from Google Drive folder (attempt 1/5)...
2025-04-28 21:23:47,916 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 21:23:47,916 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:23:47,916 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:23:47,924 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:23:48,549 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:23:48,550 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:23:48,550 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:23:48,720 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:23:48,720 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:23:48,720 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:23:53,721 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 2/5)...
2025-04-28 21:23:53,722 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:23:53,722 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:23:53,722 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:23:54,869 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:23:54,870 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:23:54,870 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:23:54,945 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:23:54,946 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:23:54,946 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:23:59,947 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 3/5)...
2025-04-28 21:23:59,948 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:23:59,949 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:23:59,950 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:01,045 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:01,045 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:01,046 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:01,141 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:01,141 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:01,142 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:24:06,142 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 4/5)...
2025-04-28 21:24:06,143 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:24:06,147 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:24:06,147 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:07,236 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:07,236 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:07,237 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:07,315 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:07,315 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:07,316 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:24:12,317 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 5/5)...
2025-04-28 21:24:12,321 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:24:12,322 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:24:12,323 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:13,384 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:13,384 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:13,385 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:13,465 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:13,466 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:13,466 - token_downloader - ERROR - Maximum number of attempts (5) reached
2025-04-28 21:24:13,477 - token_downloader - ERROR - No token files found in the folder
2025-04-28 21:24:13,477 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:13,557 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:13,557 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:13,558 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:24:18,559 - token_downloader - INFO - Downloading latest token from Google Drive folder (attempt 2/5)...
2025-04-28 21:24:18,559 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 21:24:18,560 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:24:18,561 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:24:18,561 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:19,567 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:19,568 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:19,569 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:19,666 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:19,667 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:19,667 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:24:24,668 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 2/5)...
2025-04-28 21:24:24,669 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:24:24,670 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:24:24,670 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:25,729 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:25,730 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:25,730 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:25,790 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:25,791 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:25,791 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:24:30,792 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 3/5)...
2025-04-28 21:24:30,793 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:24:30,794 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:24:30,794 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:31,944 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:31,945 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:31,945 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:32,049 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:32,050 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:32,050 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:24:37,051 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 4/5)...
2025-04-28 21:24:37,052 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:24:37,052 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:24:37,052 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:37,998 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:37,998 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:37,999 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:38,072 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:38,073 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:38,074 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:24:43,074 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 5/5)...
2025-04-28 21:24:43,075 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:24:43,075 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:24:43,075 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:44,125 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:44,125 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:44,126 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:44,213 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:44,213 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:44,214 - token_downloader - ERROR - Maximum number of attempts (5) reached
2025-04-28 21:24:44,222 - token_downloader - ERROR - No token files found in the folder
2025-04-28 21:24:44,222 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:44,291 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:44,291 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:44,292 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:24:49,292 - token_downloader - INFO - Downloading latest token from Google Drive folder (attempt 3/5)...
2025-04-28 21:24:49,293 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 21:24:49,293 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:24:49,294 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:24:49,294 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:50,278 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:50,279 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:50,279 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:50,360 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:50,361 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:50,362 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 21:24:55,363 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 2/5)...
2025-04-28 21:24:55,363 - token_downloader - WARNING - Need new token but can't get one without user interaction
2025-04-28 21:24:55,364 - token_downloader - WARNING - Could not create authenticated service, falling back to unauthenticated request
2025-04-28 21:24:55,364 - token_downloader - INFO - Using unauthenticated request to list files
2025-04-28 21:24:56,266 - token_downloader - ERROR - Failed to list files in folder: HTTP 403
2025-04-28 21:24:56,267 - token_downloader - WARNING - Got 403 Forbidden error, folder might not be publicly accessible
2025-04-28 21:24:56,267 - token_downloader - INFO - Opening folder in browser: https://drive.google.com/drive/folders/1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS
2025-04-28 21:24:56,339 - token_downloader - INFO - Folder opened in browser. Please download the latest token manually.
2025-04-28 21:24:56,339 - token_downloader - INFO - The token should be saved as 'token.json' in the application directory.
2025-04-28 21:24:56,340 - token_downloader - INFO - Retrying in 5 seconds...
2025-04-28 22:06:40,797 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:06:40,798 - token_downloader - INFO - Token expires in 53.5 minutes (threshold: 60.0 minutes)
2025-04-28 22:06:40,798 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-04-28 22:06:40,799 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:06:40,913 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:06:40,915 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:06:40,917 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-28 22:06:40,918 - token_downloader - INFO - Using service account to list files
2025-04-28 22:06:42,381 - token_downloader - INFO - Found 3 token files in the folder using service account
2025-04-28 22:06:42,394 - token_downloader - INFO - Found token file: token_********_210958.json (Date: ********, Time: 210958)
2025-04-28 22:06:42,395 - token_downloader - INFO - Found token file: token_********_210850.json (Date: ********, Time: 210850)
2025-04-28 22:06:42,395 - token_downloader - INFO - Found token file: token_********_205322.json (Date: ********, Time: 205322)
2025-04-28 22:06:42,395 - token_downloader - INFO - Sorted token files (newest first):
2025-04-28 22:06:42,395 - token_downloader - INFO -   1. token_********_210958.json (Date: ********, Time: 210958)
2025-04-28 22:06:42,395 - token_downloader - INFO -   2. token_********_210850.json (Date: ********, Time: 210850)
2025-04-28 22:06:42,395 - token_downloader - INFO -   3. token_********_205322.json (Date: ********, Time: 205322)
2025-04-28 22:06:42,396 - token_downloader - INFO - Newest token in Google Drive: token_********_210958.json (Date: ********, Time: 210958)
2025-04-28 22:06:42,396 - token_downloader - INFO - Downloading latest token from Google Drive folder (attempt 1/5)...
2025-04-28 22:06:42,396 - token_downloader - INFO - Backing up existing token to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_backup.json
2025-04-28 22:06:42,397 - token_downloader - INFO - Downloading latest token file: token_********_210958.json (Date: ********, Time: 210958, ID: 1U51S0GUmhdur_hBupDSGphOsyIWqJSXd)
2025-04-28 22:06:42,397 - token_downloader - INFO - Using service account to download token
2025-04-28 22:06:43,571 - token_downloader - INFO - Download progress: 100%
2025-04-28 22:06:43,572 - token_downloader - INFO - Processing token data from file: token_********_210958.json
2025-04-28 22:06:43,574 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:06:43,574 - token_downloader - INFO - Token expiry: 2025-04-28T14:53:21.753654Z
2025-04-28 22:06:43,579 - token_downloader - ERROR - Token is already expired
2025-04-28 22:06:43,580 - token_downloader - WARNING - Downloaded token is invalid, using existing token
2025-04-28 22:06:43,582 - token_downloader - INFO - Token expires in 53.4 minutes (threshold: 60.0 minutes)
2025-04-28 22:12:51,275 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:12:51,277 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:12:51,320 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:12:51,324 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:12:51,327 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-28 22:12:51,327 - token_downloader - INFO - Using service account to list files
2025-04-28 22:12:52,528 - token_downloader - INFO - Found 3 token files in the folder using service account
2025-04-28 22:12:52,534 - token_downloader - INFO - Found token file: token_********_210958.json (Date: ********, Time: 210958) - Possibly valid (1.0 hours old)
2025-04-28 22:12:52,534 - token_downloader - INFO - Found token file: token_********_210850.json (Date: ********, Time: 210850) - Possibly valid (1.1 hours old)
2025-04-28 22:12:52,535 - token_downloader - INFO - Found token file: token_********_205322.json (Date: ********, Time: 205322) - Possibly valid (1.3 hours old)
2025-04-28 22:12:52,535 - token_downloader - INFO - Sorted token files (newest valid first, then expired):
2025-04-28 22:12:52,536 - token_downloader - INFO -   1. token_********_210958.json (Date: ********, Time: 210958)
2025-04-28 22:12:52,536 - token_downloader - INFO -   2. token_********_210850.json (Date: ********, Time: 210850)
2025-04-28 22:12:52,536 - token_downloader - INFO -   3. token_********_205322.json (Date: ********, Time: 205322)
2025-04-28 22:12:52,538 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:12:52,539 - token_downloader - ERROR - Token is already expired
2025-04-28 22:12:52,539 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-28 22:12:52,539 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:12:52,540 - token_downloader - INFO - Downloading latest token from Google Drive folder (attempt 1/5)...
2025-04-28 22:12:52,540 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:12:52,540 - token_downloader - INFO - Using service account to list files
2025-04-28 22:12:52,883 - token_downloader - INFO - Found 3 token files in the folder using service account
2025-04-28 22:12:52,883 - token_downloader - INFO - Found token file: token_********_210958.json (Date: ********, Time: 210958) - Possibly valid (1.0 hours old)
2025-04-28 22:12:52,884 - token_downloader - INFO - Found token file: token_********_210850.json (Date: ********, Time: 210850) - Possibly valid (1.1 hours old)
2025-04-28 22:12:52,884 - token_downloader - INFO - Found token file: token_********_205322.json (Date: ********, Time: 205322) - Possibly valid (1.3 hours old)
2025-04-28 22:12:52,884 - token_downloader - INFO - Sorted token files (newest valid first, then expired):
2025-04-28 22:12:52,884 - token_downloader - INFO -   1. token_********_210958.json (Date: ********, Time: 210958)
2025-04-28 22:12:52,885 - token_downloader - INFO -   2. token_********_210850.json (Date: ********, Time: 210850)
2025-04-28 22:12:52,885 - token_downloader - INFO -   3. token_********_205322.json (Date: ********, Time: 205322)
2025-04-28 22:12:52,885 - token_downloader - INFO - Downloading latest token file: token_********_210958.json (Date: ********, Time: 210958, ID: 1U51S0GUmhdur_hBupDSGphOsyIWqJSXd)
2025-04-28 22:12:52,885 - token_downloader - INFO - Using service account to download token
2025-04-28 22:12:54,016 - token_downloader - INFO - Download progress: 100%
2025-04-28 22:12:54,017 - token_downloader - INFO - Processing token data from file: token_********_210958.json
2025-04-28 22:12:54,018 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:12:54,018 - token_downloader - INFO - Token expiry: 2025-04-28T14:53:21.753654Z
2025-04-28 22:12:54,019 - token_downloader - ERROR - Token is already expired
2025-04-28 22:12:54,020 - token_downloader - ERROR - Downloaded token is invalid
2025-04-28 22:12:54,020 - token_downloader - INFO - Retrying in 10 seconds...
2025-04-28 22:13:04,021 - token_downloader - INFO - Getting token (attempt 2/3)...
2025-04-28 22:13:04,022 - token_downloader - ERROR - Token is already expired
2025-04-28 22:13:04,023 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-28 22:13:04,023 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:13:04,024 - token_downloader - INFO - Downloading latest token from Google Drive folder (attempt 1/5)...
2025-04-28 22:13:04,025 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:13:04,025 - token_downloader - INFO - Using service account to list files
2025-04-28 22:13:04,370 - token_downloader - INFO - Found 3 token files in the folder using service account
2025-04-28 22:13:04,371 - token_downloader - INFO - Found token file: token_********_210958.json (Date: ********, Time: 210958) - Possibly valid (1.1 hours old)
2025-04-28 22:13:04,371 - token_downloader - INFO - Found token file: token_********_210850.json (Date: ********, Time: 210850) - Possibly valid (1.1 hours old)
2025-04-28 22:13:04,372 - token_downloader - INFO - Found token file: token_********_205322.json (Date: ********, Time: 205322) - Possibly valid (1.3 hours old)
2025-04-28 22:13:04,373 - token_downloader - INFO - Sorted token files (newest valid first, then expired):
2025-04-28 22:13:04,373 - token_downloader - INFO -   1. token_********_210958.json (Date: ********, Time: 210958)
2025-04-28 22:13:04,374 - token_downloader - INFO -   2. token_********_210850.json (Date: ********, Time: 210850)
2025-04-28 22:13:04,374 - token_downloader - INFO -   3. token_********_205322.json (Date: ********, Time: 205322)
2025-04-28 22:13:04,375 - token_downloader - INFO - Downloading latest token file: token_********_210958.json (Date: ********, Time: 210958, ID: 1U51S0GUmhdur_hBupDSGphOsyIWqJSXd)
2025-04-28 22:13:04,375 - token_downloader - INFO - Using service account to download token
2025-04-28 22:13:05,753 - token_downloader - INFO - Download progress: 100%
2025-04-28 22:13:05,754 - token_downloader - INFO - Processing token data from file: token_********_210958.json
2025-04-28 22:13:05,755 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:13:05,755 - token_downloader - INFO - Token expiry: 2025-04-28T14:53:21.753654Z
2025-04-28 22:13:05,758 - token_downloader - ERROR - Token is already expired
2025-04-28 22:13:05,758 - token_downloader - ERROR - Downloaded token is invalid
2025-04-28 22:13:05,759 - token_downloader - INFO - Retrying in 15 seconds...
2025-04-28 22:13:20,760 - token_downloader - INFO - Getting token (attempt 3/3)...
2025-04-28 22:13:20,762 - token_downloader - ERROR - Token is already expired
2025-04-28 22:13:20,763 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-28 22:13:20,765 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:13:20,765 - token_downloader - INFO - Downloading latest token from Google Drive folder (attempt 1/5)...
2025-04-28 22:13:20,766 - token_downloader - INFO - Getting list of token files from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:13:20,767 - token_downloader - INFO - Using service account to list files
2025-04-28 22:13:21,227 - token_downloader - INFO - Found 3 token files in the folder using service account
2025-04-28 22:13:21,228 - token_downloader - INFO - Found token file: token_********_210958.json (Date: ********, Time: 210958) - Possibly valid (1.1 hours old)
2025-04-28 22:13:21,229 - token_downloader - INFO - Found token file: token_********_210850.json (Date: ********, Time: 210850) - Possibly valid (1.1 hours old)
2025-04-28 22:13:21,229 - token_downloader - INFO - Found token file: token_********_205322.json (Date: ********, Time: 205322) - Possibly valid (1.3 hours old)
2025-04-28 22:13:21,230 - token_downloader - INFO - Sorted token files (newest valid first, then expired):
2025-04-28 22:13:21,231 - token_downloader - INFO -   1. token_********_210958.json (Date: ********, Time: 210958)
2025-04-28 22:13:21,231 - token_downloader - INFO -   2. token_********_210850.json (Date: ********, Time: 210850)
2025-04-28 22:13:21,232 - token_downloader - INFO -   3. token_********_205322.json (Date: ********, Time: 205322)
2025-04-28 22:13:21,232 - token_downloader - INFO - Downloading latest token file: token_********_210958.json (Date: ********, Time: 210958, ID: 1U51S0GUmhdur_hBupDSGphOsyIWqJSXd)
2025-04-28 22:13:21,233 - token_downloader - INFO - Using service account to download token
2025-04-28 22:13:21,935 - token_downloader - INFO - Download progress: 100%
2025-04-28 22:13:21,935 - token_downloader - INFO - Processing token data from file: token_********_210958.json
2025-04-28 22:13:21,937 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:13:21,937 - token_downloader - INFO - Token expiry: 2025-04-28T14:53:21.753654Z
2025-04-28 22:13:21,942 - token_downloader - ERROR - Token is already expired
2025-04-28 22:13:21,942 - token_downloader - ERROR - Downloaded token is invalid
2025-04-28 22:13:21,943 - token_downloader - INFO - Trying to use backup token as last resort: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_backup.json
2025-04-28 22:13:21,944 - token_downloader - INFO - Using backup token as last resort
2025-04-28 22:13:21,945 - token_downloader - INFO - Token expires in 46.8 minutes (threshold: 60.0 minutes)
2025-04-28 22:22:31,374 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:22:31,376 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:22:31,418 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json
2025-04-28 22:22:31,421 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:22:31,423 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-28 22:22:31,423 - token_downloader - INFO - Using service account to list files
2025-04-28 22:22:32,671 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:22:32,672 - token_downloader - INFO - Found file: token_********_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:22:32,674 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:22:32,675 - token_downloader - ERROR - Token is already expired
2025-04-28 22:22:32,675 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-28 22:22:32,676 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:22:32,676 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-04-28 22:22:32,677 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:22:32,678 - token_downloader - INFO - Using service account to list files
2025-04-28 22:22:33,026 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:22:33,026 - token_downloader - INFO - Found file: token_********_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:22:33,027 - token_downloader - INFO - Downloading token file: token_********_221749.json (Modified: 2025-04-28T15:17:51.723Z, ID: 1B7JhJPqXLDmpitT68yRO6kdZt-Q_4zTK)
2025-04-28 22:22:33,027 - token_downloader - INFO - Using service account to download token
2025-04-28 22:22:33,978 - token_downloader - INFO - Download progress: 100%
2025-04-28 22:22:33,979 - token_downloader - INFO - Processing token data from file: token_********_221749.json
2025-04-28 22:22:33,981 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json
2025-04-28 22:22:33,982 - token_downloader - INFO - Token expiry: 2025-04-28T16:17:13.622100Z
2025-04-28 22:22:33,985 - token_downloader - INFO - Successfully downloaded and validated new token
2025-04-28 22:22:33,987 - token_downloader - INFO - Token expires in 54.7 minutes (threshold: 60.0 minutes)
