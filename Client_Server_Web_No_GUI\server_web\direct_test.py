from flask import Flask, render_template, jsonify
import fdb
import os

app = Flask(__name__)

# Database connection parameters
db_path = r"C:\Gawean Rebinmas\Monitoring Database\Ifess Monitoring\PTRJ_P1A_08042025\PTRJ_P1A.FDB"
username = "sysdba"
password = "masterkey"

# The monitoring query
monitoring_query = """
SELECT 
  o.EMPID AS KaryawanID,
  o.ID AS OvertimeID,
  o.INPDATE AS TanggalOvertime,
  o.HOURS AS JamKerja,
  SUBSTRING(f.FIELDNO FROM 1 FOR 6) AS KodeField,
  (j.EXPTYPE || j.<PERSON><PERSON>N<PERSON> || j.SUBNO) AS AccCode,
  j.DESCRIPTION AS DeskripsiPekerjaan,
  o.BASICRATE AS TarifDasar,
  o.ADDRATE AS TarifTambahan,
  o.HOURS * o.BASICRATE AS NilaiDasar,
  o.HOURS * o.ADDRATE AS NilaiTambahan,
  o.REMARKS AS Catatan,
  SUBSTRING(f.FIELDNO FROM 1 FOR 2) AS KodeField_2Char,
  SUBSTRING(j.<PERSON>XPTYPE FROM 1 FOR 2) AS AccCode_2Char,
  a.VEHNO AS NomorKendaraan,
  a.MODEL AS ModelKendaraan,
  o.VEHID AS VehicleID
FROM OVERTIME o
JOIN JOBCODE j ON o.JOBID = j.ID
JOIN OCFIELD f ON o.FIELDID = f.ID
LEFT JOIN VEHCODE a ON o.VEHID = a.ID
WHERE o.INPDATE BETWEEN 
  CAST(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
       EXTRACT(MONTH FROM CURRENT_DATE) || '-01' AS DATE) 
  AND
  CAST(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
       EXTRACT(MONTH FROM CURRENT_DATE) || '-' || 
       EXTRACT(DAY FROM CAST(EXTRACT(YEAR FROM CURRENT_DATE) || '-' || 
                         (EXTRACT(MONTH FROM CURRENT_DATE) + 1) || '-01' AS DATE) - 1) AS DATE)
  AND (
    -- Filter 1: AccCode tidak dimulai dengan 'PT' atau 'GA', 2 karakter pertama berbeda
    (
      SUBSTRING(j.EXPTYPE FROM 1 FOR 2) NOT IN ('PT', 'GA')
      AND SUBSTRING(f.FIELDNO FROM 1 FOR 2) <> SUBSTRING(j.EXPTYPE FROM 1 FOR 2)
    )
    OR
    -- Filter 2: AccCode dimulai dengan 'PT', VEHID tidak valid
    (
      SUBSTRING(j.EXPTYPE FROM 1 FOR 2) = 'PT'
      AND a.ID IS NULL
    )
    OR
    -- Filter 3: AccCode dimulai dengan 'GA', kecuali GA9110 dengan KodeField 'YY'
    (
      SUBSTRING(j.EXPTYPE FROM 1 FOR 2) = 'GA'
      AND NOT (
        j.EXPTYPE = 'GA' AND j.ITEMNO = '91' AND j.SUBNO = '10'
        AND SUBSTRING(f.FIELDNO FROM 1 FOR 2) = 'YY'
      )
    )
  )
  AND NOT (
    -- Pengecualian tambahan: AccCode 'CL4310' dengan KodeField 'YYYY'
    j.EXPTYPE = 'CL' AND j.ITEMNO = '43' AND j.SUBNO = '10'
    AND SUBSTRING(f.FIELDNO FROM 1 FOR 4) = 'YYYY'
  )
ORDER BY o.EMPID, o.INPDATE
"""

# Simple test query
simple_query = """
SELECT 
  o.EMPID AS KaryawanID,
  o.ID AS OvertimeID,
  o.INPDATE AS TanggalOvertime,
  o.HOURS AS JamKerja
FROM OVERTIME o
LIMIT 10
"""

@app.route('/')
def index():
    return render_template('direct_test.html')

@app.route('/api/test_connection')
def test_connection():
    try:
        # Connect to the database
        conn = fdb.connect(
            dsn=db_path,
            user=username,
            password=password
        )
        
        # Close the connection
        conn.close()
        
        return jsonify({
            'success': True,
            'message': 'Connected to database successfully!'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error connecting to database: {str(e)}'
        })

@app.route('/api/run_simple_query')
def run_simple_query():
    try:
        # Connect to the database
        conn = fdb.connect(
            dsn=db_path,
            user=username,
            password=password
        )
        
        # Create a cursor
        cursor = conn.cursor()
        
        # Execute the query
        cursor.execute(simple_query)
        
        # Fetch the results
        rows = cursor.fetchall()
        
        # Get column names
        column_names = [desc[0] for desc in cursor.description]
        
        # Format the results
        results = []
        for row in rows:
            result = {}
            for i, col in enumerate(column_names):
                result[col] = row[i]
            results.append(result)
        
        # Close the cursor and connection
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'column_names': column_names,
            'rows': results,
            'row_count': len(results)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error executing query: {str(e)}'
        })

@app.route('/api/run_monitoring_query')
def run_monitoring_query():
    try:
        # Connect to the database
        conn = fdb.connect(
            dsn=db_path,
            user=username,
            password=password
        )
        
        # Create a cursor
        cursor = conn.cursor()
        
        # Execute the query
        cursor.execute(monitoring_query)
        
        # Fetch the results
        rows = cursor.fetchall()
        
        # Get column names
        column_names = [desc[0] for desc in cursor.description]
        
        # Format the results
        results = []
        for row in rows:
            result = {}
            for i, col in enumerate(column_names):
                result[col] = row[i]
            results.append(result)
        
        # Close the cursor and connection
        cursor.close()
        conn.close()
        
        return jsonify({
            'success': True,
            'column_names': column_names,
            'rows': results,
            'row_count': len(results)
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error executing query: {str(e)}'
        })

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    
    # Create the HTML template
    with open('templates/direct_test.html', 'w') as f:
        f.write("""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Database Test</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Direct Database Test</h1>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Database Connection</h5>
                    </div>
                    <div class="card-body">
                        <button id="test-connection" class="btn btn-primary">Test Connection</button>
                        <div id="connection-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Simple Query Test</h5>
                    </div>
                    <div class="card-body">
                        <button id="run-simple-query" class="btn btn-primary">Run Simple Query</button>
                        <div id="simple-query-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Monitoring Query Test</h5>
                    </div>
                    <div class="card-body">
                        <button id="run-monitoring-query" class="btn btn-primary">Run Monitoring Query</button>
                        <div id="monitoring-query-result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Test connection
            $('#test-connection').click(function() {
                $('#connection-result').html('<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>');
                
                $.get('/api/test_connection', function(data) {
                    if (data.success) {
                        $('#connection-result').html('<div class="alert alert-success">' + data.message + '</div>');
                    } else {
                        $('#connection-result').html('<div class="alert alert-danger">' + data.message + '</div>');
                    }
                }).fail(function() {
                    $('#connection-result').html('<div class="alert alert-danger">Failed to test connection</div>');
                });
            });
            
            // Run simple query
            $('#run-simple-query').click(function() {
                $('#simple-query-result').html('<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>');
                
                $.get('/api/run_simple_query', function(data) {
                    if (data.success) {
                        let html = '<div class="alert alert-success">Query executed successfully! ' + data.row_count + ' rows returned.</div>';
                        
                        if (data.rows.length > 0) {
                            html += '<div class="table-responsive"><table class="table table-striped table-bordered">';
                            
                            // Add headers
                            html += '<thead><tr>';
                            data.column_names.forEach(function(col) {
                                html += '<th>' + col + '</th>';
                            });
                            html += '</tr></thead>';
                            
                            // Add rows
                            html += '<tbody>';
                            data.rows.forEach(function(row) {
                                html += '<tr>';
                                data.column_names.forEach(function(col) {
                                    html += '<td>' + (row[col] !== null ? row[col] : '') + '</td>';
                                });
                                html += '</tr>';
                            });
                            html += '</tbody>';
                            
                            html += '</table></div>';
                        }
                        
                        $('#simple-query-result').html(html);
                    } else {
                        $('#simple-query-result').html('<div class="alert alert-danger">' + data.message + '</div>');
                    }
                }).fail(function() {
                    $('#simple-query-result').html('<div class="alert alert-danger">Failed to run query</div>');
                });
            });
            
            // Run monitoring query
            $('#run-monitoring-query').click(function() {
                $('#monitoring-query-result').html('<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>');
                
                $.get('/api/run_monitoring_query', function(data) {
                    if (data.success) {
                        let html = '<div class="alert alert-success">Query executed successfully! ' + data.row_count + ' rows returned.</div>';
                        
                        if (data.rows.length > 0) {
                            html += '<div class="table-responsive"><table class="table table-striped table-bordered">';
                            
                            // Add headers
                            html += '<thead><tr>';
                            data.column_names.forEach(function(col) {
                                html += '<th>' + col + '</th>';
                            });
                            html += '</tr></thead>';
                            
                            // Add rows
                            html += '<tbody>';
                            data.rows.forEach(function(row) {
                                html += '<tr>';
                                data.column_names.forEach(function(col) {
                                    html += '<td>' + (row[col] !== null ? row[col] : '') + '</td>';
                                });
                                html += '</tr>';
                            });
                            html += '</tbody>';
                            
                            html += '</table></div>';
                        }
                        
                        $('#monitoring-query-result').html(html);
                    } else {
                        $('#monitoring-query-result').html('<div class="alert alert-danger">' + data.message + '</div>');
                    }
                }).fail(function() {
                    $('#monitoring-query-result').html('<div class="alert alert-danger">Failed to run query</div>');
                });
            });
        });
    </script>
</body>
</html>""")
    
    # Run the Flask app
    app.run(host='0.0.0.0', port=5001, debug=True)
