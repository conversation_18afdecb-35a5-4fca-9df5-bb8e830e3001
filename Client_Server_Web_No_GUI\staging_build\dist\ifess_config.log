2025-04-24 13:26:39,407 - IFESS-Config - INFO - ===== IFESS CONFIG GUI STARTING =====
2025-04-24 13:26:39,407 - IFESS-Config - INFO - Start time: 2025-04-24 13:26:39
2025-04-24 13:26:39,407 - IFESS-Config - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-04-24 13:26:39,407 - IFESS-Config - INFO - Running from: C:\Users\<USER>\AppData\Local\Temp\_MEI96962\ifess_config_gui.py
2025-04-24 13:26:39,407 - IFESS-Config - INFO - Current directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist
2025-04-24 13:26:39,407 - IFESS-Config - INFO - Log file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ifess_config.log
2025-04-24 13:26:39,645 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-24 13:29:46,235 - IFESS-Config - INFO - ===== IFESS CONFIG GUI STARTING =====
2025-04-24 13:29:46,235 - IFESS-Config - INFO - Start time: 2025-04-24 13:29:46
2025-04-24 13:29:46,236 - IFESS-Config - INFO - Python version: 3.13.3 (tags/v3.13.3:6280bb5, Apr  8 2025, 14:47:33) [MSC v.1943 64 bit (AMD64)]
2025-04-24 13:29:46,236 - IFESS-Config - INFO - Running from: C:\Users\<USER>\AppData\Local\Temp\_MEI40002\ifess_config_gui.py
2025-04-24 13:29:46,236 - IFESS-Config - INFO - Current directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist
2025-04-24 13:29:46,236 - IFESS-Config - INFO - Log file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ifess_config.log
2025-04-24 13:29:46,543 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-24 21:43:43,592 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-24 21:43:43,819 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-24 21:43:43,820 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-24 21:44:53,076 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-24 21:44:53,077 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-24 21:45:01,882 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-24 21:45:01,882 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-24 21:45:21,506 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-24 21:45:21,510 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-26 10:15:28,221 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-26 10:15:28,536 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-26 10:15:28,536 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-26 10:15:48,489 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-26 10:15:48,489 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-26 10:15:50,612 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-26 10:15:50,613 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-27 20:46:00,130 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-27 20:46:00,553 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-27 20:46:00,553 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-27 20:46:10,287 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-27 20:46:10,288 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-27 20:46:12,047 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-27 20:46:12,048 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-27 20:51:31,717 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-27 20:51:32,257 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-27 20:51:32,259 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-27 20:51:40,840 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-27 20:51:40,840 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-27 20:51:42,497 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-27 20:51:42,497 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-28 10:56:22,594 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 10:56:22,831 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 10:56:22,832 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 10:57:21,548 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 10:57:21,548 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-28 10:57:51,494 - IFESS-Config - INFO - Testing Google Drive connection with:
2025-04-28 10:57:51,495 - IFESS-Config - INFO -   Credentials file: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/client_secret.json
2025-04-28 10:57:52,012 - IFESS-Config - INFO - Loading service account credentials from: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/client_secret.json
2025-04-28 10:57:52,015 - IFESS-Config - ERROR - Error authenticating with Google Drive: Service account info was not in the expected format, missing fields token_uri, client_email.
2025-04-28 10:57:52,016 - IFESS-Config - ERROR - Traceback (most recent call last):
  File "ifess_config_gui.py", line 671, in run_test
  File "google\oauth2\service_account.py", line 260, in from_service_account_file
  File "google\auth\_service_account_info.py", line 80, in from_filename
  File "google\auth\_service_account_info.py", line 50, in from_dict
google.auth.exceptions.MalformedError: Service account info was not in the expected format, missing fields token_uri, client_email.

2025-04-28 10:57:57,108 - IFESS-Config - INFO - Testing Google Drive connection with:
2025-04-28 10:57:57,118 - IFESS-Config - INFO -   Credentials file: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/ptrj-backup-services-account.json
2025-04-28 10:57:57,119 - IFESS-Config - INFO - Loading service account credentials from: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/ptrj-backup-services-account.json
2025-04-28 10:57:57,187 - IFESS-Config - INFO - Service account credentials loaded successfully
2025-04-28 10:57:57,187 - IFESS-Config - INFO - Building Google Drive API service...
2025-04-28 10:57:57,194 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 10:57:57,197 - IFESS-Config - INFO - Google Drive API service built successfully
2025-04-28 10:57:58,482 - IFESS-Config - INFO - Logged in as: <EMAIL> (<EMAIL>)
2025-04-28 10:57:58,482 - IFESS-Config - INFO - Storage: 14.13 GB used of 15.00 GB total
2025-04-28 10:58:06,214 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 10:58:06,214 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-28 14:26:35,667 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 14:26:36,106 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 14:26:36,107 - IFESS-Config - INFO - Loaded Google Drive credentials file: ptrj-backup-services-account.json
2025-04-28 14:26:36,108 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 15:49:33,891 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 15:49:34,176 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 15:49:34,177 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 15:49:54,428 - IFESS-Config - INFO - Testing Google Drive connection with:
2025-04-28 15:49:54,429 - IFESS-Config - INFO -   Credentials file: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/client_secret.json
2025-04-28 15:49:54,656 - IFESS-Config - INFO - Loading service account credentials from: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/client_secret.json
2025-04-28 15:49:54,657 - IFESS-Config - ERROR - Error authenticating with Google Drive: Service account info was not in the expected format, missing fields token_uri, client_email.
2025-04-28 15:49:54,658 - IFESS-Config - ERROR - Traceback (most recent call last):
  File "ifess_config_gui.py", line 671, in run_test
  File "google\oauth2\service_account.py", line 260, in from_service_account_file
  File "google\auth\_service_account_info.py", line 82, in from_filename
  File "google\auth\_service_account_info.py", line 52, in from_dict
google.auth.exceptions.MalformedError: Service account info was not in the expected format, missing fields token_uri, client_email.

2025-04-28 15:50:00,489 - IFESS-Config - INFO - Testing Google Drive connection with:
2025-04-28 15:50:00,489 - IFESS-Config - INFO -   Credentials file: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/token.json
2025-04-28 15:50:00,490 - IFESS-Config - INFO - Loading service account credentials from: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/token.json
2025-04-28 15:50:00,499 - IFESS-Config - ERROR - Error authenticating with Google Drive: Service account info was not in the expected format, missing fields client_email.
2025-04-28 15:50:00,501 - IFESS-Config - ERROR - Traceback (most recent call last):
  File "ifess_config_gui.py", line 671, in run_test
  File "google\oauth2\service_account.py", line 260, in from_service_account_file
  File "google\auth\_service_account_info.py", line 82, in from_filename
  File "google\auth\_service_account_info.py", line 52, in from_dict
google.auth.exceptions.MalformedError: Service account info was not in the expected format, missing fields client_email.

2025-04-28 15:50:02,026 - IFESS-Config - INFO - Testing Google Drive connection with:
2025-04-28 15:50:02,028 - IFESS-Config - INFO -   Credentials file: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/token.json
2025-04-28 15:50:02,029 - IFESS-Config - INFO - Loading service account credentials from: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/token.json
2025-04-28 15:50:02,030 - IFESS-Config - ERROR - Error authenticating with Google Drive: Service account info was not in the expected format, missing fields client_email.
2025-04-28 15:50:02,032 - IFESS-Config - ERROR - Traceback (most recent call last):
  File "ifess_config_gui.py", line 671, in run_test
  File "google\oauth2\service_account.py", line 260, in from_service_account_file
  File "google\auth\_service_account_info.py", line 82, in from_filename
  File "google\auth\_service_account_info.py", line 52, in from_dict
google.auth.exceptions.MalformedError: Service account info was not in the expected format, missing fields client_email.

2025-04-28 15:50:12,767 - IFESS-Config - INFO - Testing Google Drive connection with:
2025-04-28 15:50:12,768 - IFESS-Config - INFO -   Credentials file: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/client_secret.json
2025-04-28 15:50:12,768 - IFESS-Config - INFO - Loading service account credentials from: D:/Gawean Rebinmas/Monitoring Database/ifess/Client_Server_Web_No_GUI/staging_build/dist/client_secret.json
2025-04-28 15:50:12,769 - IFESS-Config - ERROR - Error authenticating with Google Drive: Service account info was not in the expected format, missing fields token_uri, client_email.
2025-04-28 15:50:12,771 - IFESS-Config - ERROR - Traceback (most recent call last):
  File "ifess_config_gui.py", line 671, in run_test
  File "google\oauth2\service_account.py", line 260, in from_service_account_file
  File "google\auth\_service_account_info.py", line 82, in from_filename
  File "google\auth\_service_account_info.py", line 52, in from_dict
google.auth.exceptions.MalformedError: Service account info was not in the expected format, missing fields token_uri, client_email.

2025-04-28 15:50:48,380 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 15:50:48,381 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-28 15:50:50,708 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 15:50:50,709 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-28 22:52:30,153 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 22:52:30,400 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-28 22:52:30,400 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 22:52:30,403 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-04-28 22:52:30,941 - IFESS-Config - INFO - token_downloader module imported successfully
2025-04-28 22:52:30,942 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-04-28 22:52:30,942 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:52:30,942 - token_downloader - ERROR - Token is already expired
2025-04-28 22:52:30,943 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-28 22:52:30,943 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-04-28 22:52:30,943 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-04-28 22:52:30,944 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:52:31,003 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-04-28 22:52:31,005 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:52:31,008 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-28 22:52:31,008 - token_downloader - INFO - Using service account to list files
2025-04-28 22:52:32,247 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:52:32,247 - token_downloader - INFO - Found file: token_20250428_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:52:32,248 - token_downloader - INFO - Downloading token file: token_20250428_221749.json (Modified: 2025-04-28T15:17:51.723Z, ID: 1B7JhJPqXLDmpitT68yRO6kdZt-Q_4zTK)
2025-04-28 22:52:32,248 - token_downloader - INFO - Using service account to download token
2025-04-28 22:52:33,275 - token_downloader - INFO - Download progress: 100%
2025-04-28 22:52:33,276 - token_downloader - INFO - Processing token data from file: token_20250428_221749.json
2025-04-28 22:52:33,277 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-04-28 22:52:33,278 - token_downloader - INFO - Token expiry: 2025-04-28T16:17:13.622100Z
2025-04-28 22:52:33,298 - token_downloader - INFO - Successfully downloaded and validated new token
2025-04-28 22:52:33,298 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-04-28 22:52:33,299 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-04-28 22:53:04,113 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-04-28 22:53:04,116 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-28 22:53:04,117 - token_downloader - INFO - Token expires in 24.2 minutes (threshold: 60.0 minutes)
2025-04-28 22:53:04,117 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-04-28 22:53:04,118 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-28 22:53:04,118 - token_downloader - INFO - Using service account to list files
2025-04-28 22:53:04,483 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-28 22:53:04,483 - token_downloader - INFO - Found file: token_20250428_221749.json (Modified: 2025-04-28T15:17:51.723Z)
2025-04-28 22:53:04,483 - token_downloader - WARNING - Newest token file doesn't have date/time information, using existing token
2025-04-28 22:53:04,484 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-04-28 22:53:04,491 - IFESS-Config - INFO - Building Google Drive API service...
2025-04-28 22:53:04,492 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-28 22:53:04,498 - IFESS-Config - INFO - Google Drive API service built successfully
2025-04-28 22:53:05,144 - IFESS-Config - INFO - Logged in as: atha rizki p .developer (<EMAIL>)
2025-04-28 22:53:05,144 - IFESS-Config - INFO - Storage: 7.04 GB used of 15.00 GB total
2025-04-28 22:53:05,146 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-04-28 22:53:17,270 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 22:53:17,278 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-28 22:53:19,483 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-28 22:53:19,484 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-04-30 07:56:02,207 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-30 07:56:02,537 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-04-30 07:56:02,538 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-04-30 07:56:02,539 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-04-30 07:56:03,084 - IFESS-Config - INFO - token_downloader module imported successfully
2025-04-30 07:56:03,084 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-04-30 07:56:03,085 - token_downloader - INFO - Getting token (attempt 1/3)...
2025-04-30 07:56:03,101 - token_downloader - ERROR - Token is already expired
2025-04-30 07:56:03,101 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-04-30 07:56:03,102 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-04-30 07:56:03,102 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/5)...
2025-04-30 07:56:03,103 - token_downloader - INFO - Getting token file from folder ID: 1lIeakc0D6r7QYu09wvey9X_xaX-ZOmtS (attempt 1/5)...
2025-04-30 07:56:03,190 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-04-30 07:56:03,193 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-04-30 07:56:03,196 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-04-30 07:56:03,196 - token_downloader - INFO - Using service account to list files
2025-04-30 07:56:04,736 - token_downloader - INFO - Found 1 files in the folder using service account
2025-04-30 07:56:04,737 - token_downloader - INFO - Found file: token_20250430_074904.json (Modified: 2025-04-30T00:49:06.127Z)
2025-04-30 07:56:04,738 - token_downloader - INFO - Downloading token file: token_20250430_074904.json (Modified: 2025-04-30T00:49:06.127Z, ID: 1LcWJC6D1TM1efayRGOfgW2wCQ2hdCc8x)
2025-04-30 07:56:04,739 - token_downloader - INFO - Using service account to download token
2025-04-30 07:56:05,743 - token_downloader - INFO - Download progress: 100%
2025-04-30 07:56:05,744 - token_downloader - INFO - Processing token data from file: token_20250430_074904.json
2025-04-30 07:56:05,753 - token_downloader - INFO - Token downloaded and saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-04-30 07:56:05,754 - token_downloader - INFO - Token expiry: 2025-04-30T01:49:01.599857Z
2025-04-30 07:56:05,770 - token_downloader - INFO - Successfully downloaded and validated new token
2025-04-30 07:56:05,770 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-04-30 07:56:05,771 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-05-20 09:00:54,221 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-05-20 09:00:54,363 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-05-20 09:00:54,363 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-05-20 09:00:54,364 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-05-20 09:00:54,529 - IFESS-Config - INFO - token_downloader module imported successfully
2025-05-20 09:00:54,529 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-05-20 09:00:54,530 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-05-20 09:00:54,530 - token_downloader - INFO - Token expires in 27.8 minutes (threshold: 60.0 minutes)
2025-05-20 09:00:54,530 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-05-20 09:00:54,530 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 09:00:54,563 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-05-20 09:00:54,566 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-20 09:00:54,567 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-05-20 09:00:54,568 - token_downloader - INFO - Using service account to list files
2025-05-20 09:00:56,808 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 09:00:56,809 - token_downloader - INFO - Found file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z)
2025-05-20 09:00:56,809 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-05-20 09:00:56,810 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-05-20 09:00:56,812 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-05-20 09:00:56,812 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-05-20 09:00:56,813 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-05-20 09:00:57,092 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-05-20 09:00:57,093 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-05-20 09:00:57,093 - token_downloader - INFO - Token expires in 27.8 minutes (threshold: 60.0 minutes)
2025-05-20 09:00:57,093 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-05-20 09:00:57,094 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-05-20 09:00:57,094 - token_downloader - INFO - Using service account to list files
2025-05-20 09:00:57,548 - token_downloader - INFO - Found 1 files in the folder using service account
2025-05-20 09:00:57,550 - token_downloader - INFO - Found file: token_20250520_082848.json (Modified: 2025-05-20T01:28:49.330Z)
2025-05-20 09:00:57,550 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-05-20 09:00:57,551 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-05-20 09:00:57,551 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-05-20 09:00:57,552 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-05-20 09:00:57,555 - IFESS-Config - INFO - Building Google Drive API service...
2025-05-20 09:00:57,558 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-05-20 09:00:57,564 - IFESS-Config - INFO - Google Drive API service built successfully
2025-05-20 09:00:58,448 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-05-20 09:00:58,449 - IFESS-Config - INFO - Storage: 70.75 GB used of 100.00 GB total
2025-05-20 09:00:58,450 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-05-20 09:00:59,923 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-05-20 09:00:59,924 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-05-20 09:01:12,533 - IFESS-Config - INFO - Testing MEGA connection with:
2025-05-20 09:01:12,535 - IFESS-Config - INFO -   Email: <EMAIL>
2025-05-20 09:01:12,535 - IFESS-Config - ERROR - MEGA library is not compatible with Python 3.13+
2025-05-20 09:01:18,779 - IFESS-Config - INFO - Configuration saved to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-05-20 09:01:18,780 - IFESS-Config - INFO - Saved MEGA credentials for: <EMAIL>
2025-06-05 08:38:08,775 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-05 08:38:08,910 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-05 08:38:08,910 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-05 08:38:08,911 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-05 08:38:09,063 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-05 08:38:09,063 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-05 08:38:09,063 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 08:38:09,064 - token_downloader - ERROR - Token is already expired
2025-06-05 08:38:09,064 - token_downloader - WARNING - Existing token is invalid, deleting and downloading new token
2025-06-05 08:38:09,073 - token_downloader - INFO - Deleted invalid token file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-05 08:38:09,074 - token_downloader - INFO - Downloading token from Google Drive folder (attempt 1/10)...
2025-06-05 08:38:09,074 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 08:38:09,108 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-06-05 08:38:09,110 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 08:38:09,112 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-05 08:38:09,112 - token_downloader - INFO - Using service account to list files
2025-06-05 08:38:10,360 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 08:38:10,360 - token_downloader - INFO - Found file: token_20250605_080716.json (Modified: 2025-06-05T01:07:17.514Z)
2025-06-05 08:38:10,361 - token_downloader - INFO - Downloading token file: token_20250605_080716.json (Modified: 2025-06-05T01:07:17.514Z, ID: 1Lqudn_rOCieFssjJ6C8yehWppIITiuls)
2025-06-05 08:38:10,361 - token_downloader - INFO - Using service account to download token
2025-06-05 08:39:41,800 - IFESS-Config - INFO - Using configuration file: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-05 08:39:41,929 - IFESS-Config - INFO - Loaded MEGA credentials for: <EMAIL>
2025-06-05 08:39:41,929 - IFESS-Config - INFO - Configuration loaded from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\client_config.json
2025-06-05 08:39:41,930 - IFESS-Config - INFO - Initializing token_downloader in the background
2025-06-05 08:39:42,107 - IFESS-Config - INFO - token_downloader module imported successfully
2025-06-05 08:39:42,108 - IFESS-Config - INFO - Getting token from Google Drive in the background
2025-06-05 08:39:42,108 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 08:39:42,109 - token_downloader - INFO - Token expires in 26.7 minutes (threshold: 60.0 minutes)
2025-06-05 08:39:42,109 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 08:39:42,109 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 08:39:42,141 - token_downloader - INFO - Successfully loaded service account credentials from D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ptrj-backup-services-account.json
2025-06-05 08:39:42,144 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 08:39:42,146 - token_downloader - INFO - Created authenticated Google Drive service using service account
2025-06-05 08:39:42,146 - token_downloader - INFO - Using service account to list files
2025-06-05 08:39:43,170 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 08:39:43,171 - token_downloader - INFO - Found file: token_20250605_080716.json (Modified: 2025-06-05T01:07:17.514Z)
2025-06-05 08:39:43,171 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 08:39:43,173 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 08:39:43,173 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 08:39:43,177 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 08:39:43,179 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
2025-06-05 08:39:59,422 - IFESS-Config - INFO - Using token_downloader to get token from Google Drive
2025-06-05 08:39:59,423 - token_downloader - INFO - Getting token (attempt 1/5)...
2025-06-05 08:39:59,425 - token_downloader - INFO - Token expires in 26.4 minutes (threshold: 60.0 minutes)
2025-06-05 08:39:59,426 - token_downloader - INFO - Token is expiring soon, downloading fresh token from Google Drive
2025-06-05 08:39:59,426 - token_downloader - INFO - Getting token file from folder ID: 1ccPHX17WYxfFay5a6aMWSugHuSFn63tv (attempt 1/10)...
2025-06-05 08:39:59,426 - token_downloader - INFO - Using service account to list files
2025-06-05 08:39:59,872 - token_downloader - INFO - Found 1 files in the folder using service account
2025-06-05 08:39:59,875 - token_downloader - INFO - Found file: token_20250605_080716.json (Modified: 2025-06-05T01:07:17.514Z)
2025-06-05 08:39:59,877 - token_downloader - ERROR - Error processing token files: type object 'datetime.datetime' has no attribute 'timedelta'
2025-06-05 08:39:59,878 - token_downloader - ERROR - Traceback (most recent call last):
  File "token_downloader.py", line 286, in process_token_files
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-06-05 08:39:59,878 - token_downloader - WARNING - No valid tokens found after processing, using existing token
2025-06-05 08:39:59,879 - IFESS-Config - INFO - Token retrieved successfully from Google Drive
2025-06-05 08:39:59,881 - IFESS-Config - INFO - Building Google Drive API service...
2025-06-05 08:39:59,882 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-06-05 08:39:59,889 - IFESS-Config - INFO - Google Drive API service built successfully
2025-06-05 08:40:00,572 - IFESS-Config - INFO - Logged in as: PTRJ IFES (<EMAIL>)
2025-06-05 08:40:00,574 - IFESS-Config - INFO - Storage: 70.81 GB used of 100.00 GB total
2025-06-05 08:40:00,575 - IFESS-Config - INFO - Updated credentials file path to: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\token.json
