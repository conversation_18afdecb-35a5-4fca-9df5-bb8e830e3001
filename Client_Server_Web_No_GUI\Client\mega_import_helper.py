"""
Helper module to import MEGA client with proper error handling
"""

import os
import sys
import logging
import traceback

logger = logging.getLogger("MEGA-Helper")

def get_mega_client():
    """
    Try to import MegaClient from various sources with proper error handling
    Returns a MegaClient class that can be instantiated
    """
    # First try the patched version
    try:
        # Add parent directory to path to import mega_patch
        parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if parent_dir not in sys.path:
            sys.path.append(parent_dir)
            
        # Try to import mega_patch
        try:
            import mega_patch
            logger.info("Imported mega_patch successfully")
        except ImportError:
            logger.warning("mega_patch.py not found, MEGA functionality may not work in Python 3.10+")
        except Exception as e:
            logger.error(f"Error importing mega_patch: {e}")
            logger.error(traceback.format_exc())
            
        # Try to import patched client
        from mega_client_patched import MegaClient
        logger.info("Using patched MEGA client for Python 3.10+ compatibility")
        return MegaClient
    except ImportError:
        logger.warning("Patched MEGA client not found, trying alternative clients")
    except Exception as e:
        logger.error(f"Error importing patched MEGA client: {e}")
        logger.error(traceback.format_exc())
    
    # Try Python 3.10 specific client
    try:
        from mega_client_py310 import MegaClient
        logger.info("Using Python 3.10 specific MEGA client")
        return MegaClient
    except ImportError:
        logger.warning("Python 3.10 specific MEGA client not found, trying standard client")
    except Exception as e:
        logger.error(f"Error importing Python 3.10 specific MEGA client: {e}")
        logger.error(traceback.format_exc())
    
    # Try standard client
    try:
        from mega_client import MegaClient
        logger.info("Using standard MEGA client")
        return MegaClient
    except ImportError:
        logger.warning("Standard MEGA client not found")
    except Exception as e:
        logger.error(f"Error importing standard MEGA client: {e}")
        logger.error(traceback.format_exc())
    
    # If all imports fail, create a dummy class
    logger.warning("No MEGA client found, creating dummy client")
    
    class DummyMegaClient:
        """Dummy MEGA client that does nothing but logs errors"""
        
        def __init__(self, client_id=None):
            self.client_id = client_id
            self.logged_in = False
            self.last_error = "MEGA functionality is disabled - no client available"
            self.upload_status = "Disabled"
            logger.warning("Using dummy MEGA client - MEGA functionality is disabled")
            
        def login_mega(self):
            logger.error("Cannot login to MEGA: MEGA functionality is disabled")
            return False
            
        def upload_file_to_mega(self, file_path):
            logger.error("Cannot upload to MEGA: MEGA functionality is disabled")
            return {
                'success': False,
                'error': "MEGA functionality is disabled",
                'file_name': os.path.basename(file_path) if file_path else "unknown"
            }
            
        def handle_mega_upload_request(self, socket, message):
            logger.error("Cannot handle MEGA upload request: MEGA functionality is disabled")
            return
            
        def get_status(self):
            return {
                'available': False,
                'logged_in': False,
                'status': 'Disabled',
                'progress': 0,
                'last_error': self.last_error,
                'patch_applied': False
            }
    
    return DummyMegaClient
