"""
IFESS Google Drive Test GUI
--------------------------
A simple GUI for testing Google Drive uploads.
"""

import os
import sys
import json
import time
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import logging

# Setup logging
log_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(log_dir, "gdrive_test_gui.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("gdrive_test_gui")

class GDriveTestGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("IFESS Google Drive Test")
        self.root.geometry("600x500")
        self.root.minsize(600, 500)

        # Set icon if available
        try:
            self.root.iconbitmap("ifess_icon.ico")
        except:
            pass

        # Variables
        self.file_path = tk.StringVar()
        self.client_id = tk.StringVar(value="test_client")
        self.status = tk.StringVar(value="Ready")
        self.progress = tk.IntVar(value=0)

        # Create UI
        self.create_widgets()

        # Initialize GDrive client
        self.gdrive_client = None
        self.init_gdrive_client()

    def create_widgets(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # File selection
        file_frame = ttk.LabelFrame(main_frame, text="File Selection", padding=10)
        file_frame.pack(fill=tk.X, pady=5)

        ttk.Label(file_frame, text="File Path:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(file_frame, textvariable=self.file_path, width=50).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Button(file_frame, text="Browse", command=self.browse_file).grid(row=0, column=2, sticky=tk.E, pady=5)

        # Client ID
        client_frame = ttk.LabelFrame(main_frame, text="Client Information", padding=10)
        client_frame.pack(fill=tk.X, pady=5)

        ttk.Label(client_frame, text="Client ID:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(client_frame, textvariable=self.client_id, width=30).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)

        # Token information
        token_frame = ttk.LabelFrame(main_frame, text="Token Information", padding=10)
        token_frame.pack(fill=tk.X, pady=5)

        self.token_text = tk.Text(token_frame, height=5, width=50, wrap=tk.WORD)
        self.token_text.pack(fill=tk.X, pady=5)
        self.token_text.config(state=tk.DISABLED)

        # Actions
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill=tk.X, pady=10)

        ttk.Button(action_frame, text="Download Token", command=self.download_token).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="Check Token", command=self.check_token).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="Upload File", command=self.upload_file).pack(side=tk.LEFT, padx=5)

        # Progress
        progress_frame = ttk.LabelFrame(main_frame, text="Upload Progress", padding=10)
        progress_frame.pack(fill=tk.X, pady=5)

        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)

        ttk.Label(progress_frame, textvariable=self.status).pack(fill=tk.X, pady=5)

        # Results
        result_frame = ttk.LabelFrame(main_frame, text="Upload Results", padding=10)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.result_text = tk.Text(result_frame, height=10, width=50, wrap=tk.WORD)
        self.result_text.pack(fill=tk.BOTH, expand=True, pady=5)
        self.result_text.config(state=tk.DISABLED)

    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="Select File to Upload",
            filetypes=[("All Files", "*.*"), ("Database Files", "*.fdb")]
        )
        if file_path:
            self.file_path.set(file_path)

    def init_gdrive_client(self):
        """Initialize Google Drive client"""
        try:
            # First try to import token_downloader
            try:
                import token_downloader
                logger.info("Using token_downloader to get token from Google Drive")

                # Download token if needed
                if not os.path.exists("token.json"):
                    self.status.set("Downloading token from Google Drive...")
                    token_downloader.download_token()
            except ImportError:
                logger.warning("token_downloader module not found")

            # Try to import GDriveClient
            try:
                from gdrive_client_oauth_simple import GDriveClient
                self.status.set("Initializing Google Drive client...")
                self.gdrive_client = GDriveClient(self.client_id.get())
                self.status.set("Google Drive client initialized")
                self.check_token()
            except ImportError:
                logger.error("gdrive_client_oauth_simple module not found")
                self.status.set("Error: Google Drive client module not found")
                messagebox.showerror("Error", "Google Drive client module not found")
        except Exception as e:
            logger.error(f"Error initializing Google Drive client: {e}")
            self.status.set(f"Error: {str(e)}")
            messagebox.showerror("Error", f"Failed to initialize Google Drive client: {str(e)}")

    def download_token(self):
        """Download token from Google Drive with robust recovery"""
        self.status.set("Downloading token from Google Drive...")
        self.progress.set(0)

        def download_thread():
            try:
                import token_downloader

                # Update progress during download attempts
                def progress_updater():
                    progress_value = 0
                    while progress_value < 90:
                        time.sleep(0.5)
                        progress_value += 1
                        self.root.after(0, lambda v=progress_value: self.progress.set(v))
                        self.root.after(0, lambda v=progress_value: self.status.set(f"Downloading token... {v}%"))

                # Start progress updater in separate thread
                progress_thread = threading.Thread(target=progress_updater, daemon=True)
                progress_thread.start()

                # Get token with robust recovery
                token_data = token_downloader.get_token(max_attempts=5)

                if token_data:
                    self.root.after(0, lambda: self.status.set("Token downloaded and validated successfully"))
                    self.root.after(0, lambda: self.progress.set(100))
                    self.root.after(0, self.check_token)
                else:
                    self.root.after(0, lambda: self.status.set("Failed to get valid token"))
                    self.root.after(0, lambda: self.progress.set(0))
                    self.root.after(0, lambda: messagebox.showerror("Error", "Failed to get valid token after multiple attempts"))
            except Exception as e:
                logger.error(f"Error downloading token: {e}")
                self.root.after(0, lambda: self.status.set(f"Error: {str(e)}"))
                self.root.after(0, lambda: self.progress.set(0))
                self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to download token: {str(e)}"))

        threading.Thread(target=download_thread, daemon=True).start()

    def check_token(self):
        """Check token information"""
        self.status.set("Checking token...")

        try:
            token_file = "token.json"
            if os.path.exists(token_file):
                with open(token_file, 'r') as f:
                    token_data = json.load(f)

                # Update token text
                self.token_text.config(state=tk.NORMAL)
                self.token_text.delete(1.0, tk.END)

                # Format token info
                token_info = f"Client ID: {token_data.get('client_id', 'N/A')}\n"

                # Check validity
                try:
                    import token_downloader
                    is_valid = token_downloader.validate_token(token_data)

                    if is_valid:
                        # Check expiry
                        if 'expiry' in token_data:
                            token_info += f"Expiry: {token_data['expiry']}\n"

                            # Check if token is expiring soon
                            if token_downloader.is_token_expiring_soon(token_data):
                                token_info += "Status: Valid but expiring soon\n"
                            else:
                                token_info += "Status: Valid\n"
                        else:
                            token_info += "Expiry: Not available\n"
                            token_info += "Status: Valid (expiry unknown)\n"
                    else:
                        token_info += "Status: INVALID\n"
                        token_info += "Reason: Token validation failed\n"
                except Exception as validate_error:
                    logger.error(f"Error validating token: {validate_error}")
                    token_info += "Status: Unknown (validation error)\n"
                    token_info += f"Error: {str(validate_error)}\n"

                # Add scopes
                if 'scopes' in token_data:
                    scopes = token_data['scopes']
                    if isinstance(scopes, list):
                        token_info += f"Scopes: {', '.join(scopes)}\n"
                    else:
                        token_info += f"Scopes: {scopes}\n"

                # Add token source info
                backup_exists = os.path.exists("token_backup.json")
                token_info += f"\nBackup token: {'Available' if backup_exists else 'Not available'}\n"

                # Add recovery info
                token_info += "\nRecovery: If token becomes invalid, the application will:\n"
                token_info += "1. Try to download a fresh token from Google Drive\n"
                token_info += "2. Try to restore from backup if download fails\n"
                token_info += "3. Retry multiple times with increasing delays\n"

                self.token_text.insert(tk.END, token_info)
                self.token_text.config(state=tk.DISABLED)

                self.status.set("Token checked")
            else:
                self.token_text.config(state=tk.NORMAL)
                self.token_text.delete(1.0, tk.END)
                self.token_text.insert(tk.END, "Token file not found\n\nClick 'Download Token' to get token from Google Drive")
                self.token_text.config(state=tk.DISABLED)

                self.status.set("Token file not found")
        except Exception as e:
            logger.error(f"Error checking token: {e}")
            self.status.set(f"Error checking token: {str(e)}")

            self.token_text.config(state=tk.NORMAL)
            self.token_text.delete(1.0, tk.END)
            self.token_text.insert(tk.END, f"Error: {str(e)}")
            self.token_text.config(state=tk.DISABLED)

    def upload_file(self):
        """Upload file to Google Drive"""
        file_path = self.file_path.get()

        if not file_path:
            messagebox.showerror("Error", "Please select a file to upload")
            return

        if not os.path.exists(file_path):
            messagebox.showerror("Error", f"File not found: {file_path}")
            return

        if not self.gdrive_client:
            self.init_gdrive_client()
            if not self.gdrive_client:
                messagebox.showerror("Error", "Google Drive client not initialized")
                return

        # Update client ID
        self.gdrive_client.client_id = self.client_id.get()

        self.status.set("Uploading file...")
        self.progress.set(0)
        self.result_text.config(state=tk.NORMAL)
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, "Starting upload...\n")
        self.result_text.config(state=tk.DISABLED)

        def progress_callback(progress):
            self.root.after(0, lambda: self.progress.set(progress))

        def upload_thread():
            try:
                result = self.gdrive_client.upload_file(file_path, progress_callback)

                self.root.after(0, lambda: self.result_text.config(state=tk.NORMAL))
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))

                if result.get('success', False):
                    self.root.after(0, lambda: self.status.set("Upload completed successfully"))
                    self.root.after(0, lambda: self.progress.set(100))

                    # Format result
                    result_text = "Upload completed successfully\n\n"
                    result_text += f"File: {result.get('name', 'N/A')}\n"
                    result_text += f"File ID: {result.get('file_id', 'N/A')}\n"

                    if 'web_view_link' in result:
                        result_text += f"View Link: {result['web_view_link']}\n"

                    if 'web_content_link' in result:
                        result_text += f"Download Link: {result['web_content_link']}\n"

                    if 'upload_duration' in result:
                        result_text += f"Duration: {result['upload_duration']:.2f} seconds\n"

                    self.root.after(0, lambda: self.result_text.insert(tk.END, result_text))
                else:
                    self.root.after(0, lambda: self.status.set("Upload failed"))

                    # Format error
                    error_text = "Upload failed\n\n"
                    error_text += f"Error: {result.get('error', 'Unknown error')}\n"

                    self.root.after(0, lambda: self.result_text.insert(tk.END, error_text))

                self.root.after(0, lambda: self.result_text.config(state=tk.DISABLED))
            except Exception as e:
                logger.error(f"Error during upload: {e}")
                self.root.after(0, lambda: self.status.set(f"Error: {str(e)}"))
                self.root.after(0, lambda: self.progress.set(0))

                self.root.after(0, lambda: self.result_text.config(state=tk.NORMAL))
                self.root.after(0, lambda: self.result_text.delete(1.0, tk.END))
                self.root.after(0, lambda: self.result_text.insert(tk.END, f"Error during upload: {str(e)}"))
                self.root.after(0, lambda: self.result_text.config(state=tk.DISABLED))

        threading.Thread(target=upload_thread, daemon=True).start()

def main():
    root = tk.Tk()
    app = GDriveTestGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
