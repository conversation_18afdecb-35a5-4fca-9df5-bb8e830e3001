@echo off
echo Building IFESS Client Debug Application
echo ===================================
echo.

REM Menghentikan aplikasi yang sedang berjalan
echo Menghentikan aplikasi yang sedang berjalan...
taskkill /f /im ifess_client_debug.exe 2>nul
taskkill /f /fi "IMAGENAME eq python.exe" /fi "COMMANDLINE eq *ifess_client_debug.py*" 2>nul
timeout /t 2 /nobreak > nul

REM Mendapatkan direktori saat ini
set CURRENT_DIR=%CD%
echo Direktori saat ini: %CURRENT_DIR%

REM Mendefinisikan direktori output yang baru
set OUTPUT_DIR=D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist
echo Output direktori: %OUTPUT_DIR%

REM Membersihkan direktori dist
echo Membersihkan direktori dist...
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"
if exist "%OUTPUT_DIR%\ifess_client_debug.exe" (
    echo Menghapus executable yang ada...
    del /f /q "%OUTPUT_DIR%\ifess_client_debug.exe" 2>nul
)

REM Membersihkan direktori build
echo Membersihkan direktori build...
if not exist build mkdir build
if exist build\ifess_client_debug (
    rmdir /s /q build\ifess_client_debug 2>nul
)

REM Membersihkan spec file
echo Membersihkan spec file...
if exist ifess_client_debug.spec (
    del /f /q ifess_client_debug.spec 2>nul
)

REM Memeriksa apakah Python terinstall
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python tidak terinstall atau tidak ada di PATH. Harap install Python 3.8 atau lebih tinggi.
    goto :exit
)

REM Menginstall PyInstaller jika belum terinstall
echo Menginstall PyInstaller...
python -m pip install pyinstaller
if %ERRORLEVEL% NEQ 0 (
    echo Gagal menginstall PyInstaller. Harap periksa koneksi internet Anda.
    goto :exit
)

REM Menginstall paket yang diperlukan
echo Menginstall paket yang diperlukan...
python -m pip install mega.py
python -m pip install fdb
python -m pip install requests
python -m pip install google-auth google-api-python-client
if %ERRORLEVEL% NEQ 0 (
    echo Gagal menginstall paket yang diperlukan. Harap periksa koneksi internet Anda.
    goto :exit
)

REM Mengatasi masalah pathlib yang konflik dengan PyInstaller
echo Mengatasi masalah pathlib yang tidak kompatibel dengan PyInstaller...
python -m pip uninstall -y pathlib
echo Mencoba cara alternatif untuk menghapus pathlib...
"C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\python.exe" -m pip uninstall -y pathlib
echo Paket pathlib berhasil dihapus.

REM ========== BUILD CLIENT DEBUG ==========
echo.
echo Membangun Aplikasi Client Debug...
echo Ini mungkin membutuhkan beberapa menit...

python -m PyInstaller --name=ifess_client_debug --onefile --clean --console --noupx ^
    --hidden-import=mega ^
    --hidden-import=fdb ^
    --hidden-import=socket ^
    --hidden-import=json ^
    --hidden-import=requests ^
    --hidden-import=googleapiclient ^
    --hidden-import=googleapiclient.discovery ^
    --hidden-import=google.oauth2 ^
    --hidden-import=google.oauth2.service_account ^
    --add-data "%CURRENT_DIR%\client_config.json;." ^
    --add-data "%CURRENT_DIR%\mega_client_py310.py;." ^
    --add-data "%CURRENT_DIR%\gdrive_client_module.py;." ^
    --add-data "%CURRENT_DIR%\ptrj-backup-services-account.json;." ^
    --distpath "%OUTPUT_DIR%" ^
    --workpath "%CURRENT_DIR%\build" ^
    "%CURRENT_DIR%\ifess_client_debug.py"

if %ERRORLEVEL% NEQ 0 (
    echo Gagal membangun Client Debug.
    goto :exit
)

REM Menyalin file konfigurasi default dan kredensial jika belum ada
echo.
echo Menyalin file konfigurasi default dan kredensial...
if not exist "%OUTPUT_DIR%\client_config.json" (
    copy "%CURRENT_DIR%\client_config.json" "%OUTPUT_DIR%\client_config.json"
)
if not exist "%OUTPUT_DIR%\ptrj-backup-services-account.json" (
    copy "%CURRENT_DIR%\ptrj-backup-services-account.json" "%OUTPUT_DIR%\ptrj-backup-services-account.json"
)

echo.
echo Build Client Debug selesai dengan sukses!
echo Executable tersedia di folder '%OUTPUT_DIR%'.
echo.

:exit
pause