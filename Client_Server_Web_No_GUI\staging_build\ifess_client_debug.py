#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IFESS Client Debug Application

This application provides a GUI for viewing logs and status of the hidden client.
It does not affect the hidden client if closed.
"""

import os
import sys
import json
import socket
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import logging
import traceback
import subprocess
import ctypes
import re
import random
import schedule
import math

# Constants
CONFIG_FILE = "client_config.json"
LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.log")
MUTEX_NAME = "Global\\IFESS_Hidden_Client_Running"
REFRESH_INTERVAL = 1000  # milliseconds

# Try to find log file in multiple locations
def find_log_file():
    """Find log file in multiple locations"""
    possible_paths = [
        # Same directory as debug app
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.log"),
        # Current directory
        "ifess_client_hidden.log",
        # Parent directory
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.log"),
        # dist directory
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_client_hidden.log"),
    ]

    # Find the first path that exists
    for path in possible_paths:
        if os.path.exists(path):
            return path

    # If not found, return the default path
    return LOG_FILE

# Parse command line arguments
verbose_mode = False
for arg in sys.argv[1:]:
    if arg == '--verbose':
        verbose_mode = True

# Setup logging
log_level = logging.DEBUG if verbose_mode else logging.INFO
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ifess_debug.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("IFESS-Debug")

# Log startup information
logger.info("===== IFESS DEBUG CLIENT STARTING =====")
logger.info(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
logger.info(f"Python version: {sys.version}")
logger.info(f"Running from: {os.path.abspath(__file__)}")
logger.info(f"Current directory: {os.getcwd()}")
logger.info(f"Log file: {os.path.abspath('ifess_debug.log')}")
logger.info(f"Verbose mode: {verbose_mode}")

class ConnectionStats:
    """Track connection statistics and metrics"""
    def __init__(self):
        self.connection_start_time = None
        self.total_uptime = 0
        self.reconnection_attempts = 0
        self.last_disconnection_time = None
        self.disconnection_reason = "Unknown"
        self.connection_history = []

    def track_connection_established(self):
        """Record when connection is established"""
        self.connection_start_time = time.time()
        self.connection_history.append(('connected', time.time()))

    def track_disconnection(self, reason="Unknown"):
        """Record when connection is lost"""
        if self.connection_start_time:
            session_duration = time.time() - self.connection_start_time
            self.total_uptime += session_duration
            self.connection_start_time = None
        
        self.last_disconnection_time = time.time()
        self.disconnection_reason = reason
        self.connection_history.append(('disconnected', time.time(), reason))
        
        # Keep only last 50 connection events
        if len(self.connection_history) > 50:
            self.connection_history = self.connection_history[-50:]

    def track_connection_attempt(self):
        """Record connection attempt"""
        self.reconnection_attempts += 1

class ConnectionManager:
    """Manage connection backoff strategy with exponential backoff and jitter"""
    def __init__(self, base_interval=5, max_interval=300, jitter_percent=20):
        self.base_interval = base_interval
        self.max_interval = max_interval
        self.jitter_percent = jitter_percent
        self.current_attempt = 0

    def get_next_interval(self):
        """Calculate next reconnection interval with exponential backoff and jitter"""
        # Calculate exponential backoff: base * (2 ^ attempt)
        interval = self.base_interval * (2 ** self.current_attempt)
        interval = min(interval, self.max_interval)
        
        # Add jitter (±jitter_percent)
        jitter_range = interval * (self.jitter_percent / 100)
        jitter = random.uniform(-jitter_range, jitter_range)
        interval_with_jitter = max(1, interval + jitter)
        
        return interval_with_jitter

    def increment_attempt(self):
        """Increment attempt counter"""
        self.current_attempt += 1

    def reset_backoff(self):
        """Reset backoff strategy after successful connection"""
        self.current_attempt = 0

    def get_current_attempt(self):
        """Get current attempt number"""
        return self.current_attempt

def format_file_size(size_bytes):
    """Format file size in human readable format"""
    if size_bytes == 0:
        return "0B"
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

class DebugApp:
    """Debug application for IFESS hidden client"""
    def __init__(self, root):
        self.root = root
        self.root.title("IFESS Client Debug" + (" (Verbose Mode)" if verbose_mode else ""))
        self.root.geometry("900x700")
        self.root.minsize(700, 500)

        # Set icon if available
        try:
            self.root.iconbitmap("MAINICON.ico")
        except Exception as e:
            if verbose_mode:
                logger.debug(f"Could not load icon: {e}")

        # Variables
        self.client_running = False
        self.log_position = 0
        self.auto_scroll = tk.BooleanVar(value=True)
        self.auto_refresh = tk.BooleanVar(value=True)
        self.backup_status = "Unknown"
        self.backup_progress = 0
        self.backup_details = {}
        self.last_backup_time = None
        self.connection_status = "Unknown"
        self.last_connection_drop = None
        self.reconnection_attempts = 0
        self.verbose_mode = verbose_mode

        # MEGA upload tracking
        self.mega_upload_status = "Unknown"
        self.mega_upload_progress = 0
        self.mega_upload_details = {}
        self.last_mega_upload_time = None

        # GDrive upload tracking
        self.gdrive_upload_status = "Unknown"
        self.gdrive_upload_progress = 0
        self.gdrive_upload_details = {}
        self.last_gdrive_upload_time = None

        # Connection management and statistics
        self.connection_stats = ConnectionStats()
        self.connection_manager = ConnectionManager()
        self.enhanced_connection_tracking = True

        # Log initialization
        logger.info("Initializing debug application")
        if verbose_mode:
            logger.debug("Verbose mode enabled, additional debug information will be shown")
            logger.debug(f"Auto-scroll: {self.auto_scroll.get()}")
            logger.debug(f"Auto-refresh: {self.auto_refresh.get()}")
            logger.debug(f"Refresh interval: {REFRESH_INTERVAL} ms")

        # Create UI
        self.create_widgets()

        # Initial check and refresh
        logger.info("Performing initial status check and log refresh")
        self.check_client_status()
        self.refresh_log()

        # Start auto-refresh if enabled
        if self.auto_refresh.get():
            logger.info("Starting auto-refresh")
            self.schedule_refresh()

    def create_widgets(self):
        """Create UI widgets"""
        # Main frame with padding
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Client Status", padding="5")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        # Status indicators
        self.status_label = ttk.Label(status_frame, text="Checking...", foreground="blue")
        self.status_label.pack(side=tk.LEFT, padx=5)

        # Connection status label (new)
        self.connection_label = ttk.Label(status_frame, text="Connection: Unknown", foreground="gray")
        self.connection_label.pack(side=tk.LEFT, padx=20)

        # Control buttons
        control_frame = ttk.Frame(status_frame)
        control_frame.pack(side=tk.RIGHT, padx=5)

        ttk.Button(control_frame, text="Start Client", command=self.start_client).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="Stop Client", command=self.stop_client).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="Refresh", command=self.manual_refresh).pack(side=tk.LEFT, padx=2)

        # Connection Statistics frame
        conn_stats_frame = ttk.LabelFrame(main_frame, text="Connection Statistics", padding="5")
        conn_stats_frame.pack(fill=tk.X, pady=(0, 10))

        # Connection stats display
        conn_stats_left = ttk.Frame(conn_stats_frame)
        conn_stats_left.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.uptime_label = ttk.Label(conn_stats_left, text="Uptime: Unknown", foreground="gray")
        self.uptime_label.pack(anchor='w', padx=5)

        self.reconnect_attempts_label = ttk.Label(conn_stats_left, text="Reconnection attempts: 0", foreground="gray")
        self.reconnect_attempts_label.pack(anchor='w', padx=5)

        conn_stats_right = ttk.Frame(conn_stats_frame)
        conn_stats_right.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        self.backoff_interval_label = ttk.Label(conn_stats_right, text="Backoff interval: N/A", foreground="gray")
        self.backoff_interval_label.pack(anchor='w', padx=5)

        self.last_disconnect_label = ttk.Label(conn_stats_right, text="Last disconnect: N/A", foreground="gray")
        self.last_disconnect_label.pack(anchor='w', padx=5)

        # Backup status frame
        backup_frame = ttk.LabelFrame(main_frame, text="Backup Status", padding="5")
        backup_frame.pack(fill=tk.X, pady=(0, 10))

        # Backup status indicators
        self.backup_status_label = ttk.Label(backup_frame, text="Not monitoring backup activity", foreground="gray")
        self.backup_status_label.pack(side=tk.TOP, padx=5, anchor='w')

        # Progress bar
        self.progress_frame = ttk.Frame(backup_frame)
        self.progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.progress_bar = ttk.Progressbar(self.progress_frame, orient="horizontal", length=100, mode="determinate")
        self.progress_bar.pack(fill=tk.X, side=tk.TOP)

        self.progress_label = ttk.Label(self.progress_frame, text="0%")
        self.progress_label.pack(side=tk.TOP, pady=2)

        # Backup details
        self.backup_details_text = scrolledtext.ScrolledText(backup_frame, wrap=tk.WORD, width=80, height=5)
        self.backup_details_text.pack(fill=tk.X, expand=False, padx=5, pady=5)
        self.backup_details_text.insert(tk.END, "No backup details available")
        self.backup_details_text.config(state=tk.DISABLED)

        # MEGA Upload status frame
        mega_frame = ttk.LabelFrame(main_frame, text="MEGA Upload Status", padding="5")
        mega_frame.pack(fill=tk.X, pady=(0, 10))

        # MEGA Upload status indicators
        self.mega_status_label = ttk.Label(mega_frame, text="Not monitoring MEGA upload activity", foreground="gray")
        self.mega_status_label.pack(side=tk.TOP, padx=5, anchor='w')

        # MEGA Progress bar
        self.mega_progress_frame = ttk.Frame(mega_frame)
        self.mega_progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.mega_progress_bar = ttk.Progressbar(self.mega_progress_frame, orient="horizontal", length=100, mode="determinate")
        self.mega_progress_bar.pack(fill=tk.X, side=tk.TOP)

        self.mega_progress_label = ttk.Label(self.mega_progress_frame, text="0%")
        self.mega_progress_label.pack(side=tk.TOP, pady=2)

        # MEGA Upload details
        self.mega_details_text = scrolledtext.ScrolledText(mega_frame, wrap=tk.WORD, width=80, height=5)
        self.mega_details_text.pack(fill=tk.X, expand=False, padx=5, pady=5)
        self.mega_details_text.insert(tk.END, "No MEGA upload details available")
        self.mega_details_text.config(state=tk.DISABLED)

        # GDrive Upload status frame
        gdrive_frame = ttk.LabelFrame(main_frame, text="Google Drive Upload Status", padding="5")
        gdrive_frame.pack(fill=tk.X, pady=(0, 10))

        # GDrive Upload status indicators
        self.gdrive_status_label = ttk.Label(gdrive_frame, text="Not monitoring GDrive upload activity", foreground="gray")
        self.gdrive_status_label.pack(side=tk.TOP, padx=5, anchor='w')

        # GDrive token status
        self.gdrive_token_label = ttk.Label(gdrive_frame, text="Token status: Unknown", foreground="gray")
        self.gdrive_token_label.pack(side=tk.TOP, padx=5, anchor='w')

        # GDrive Progress bar
        self.gdrive_progress_frame = ttk.Frame(gdrive_frame)
        self.gdrive_progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.gdrive_progress_bar = ttk.Progressbar(self.gdrive_progress_frame, orient="horizontal", length=100, mode="determinate")
        self.gdrive_progress_bar.pack(fill=tk.X, side=tk.TOP)

        self.gdrive_progress_label = ttk.Label(self.gdrive_progress_frame, text="0%")
        self.gdrive_progress_label.pack(side=tk.TOP, pady=2)

        # GDrive Upload details
        self.gdrive_details_text = scrolledtext.ScrolledText(gdrive_frame, wrap=tk.WORD, width=80, height=5)
        self.gdrive_details_text.pack(fill=tk.X, expand=False, padx=5, pady=5)
        self.gdrive_details_text.insert(tk.END, "No Google Drive upload details available")
        self.gdrive_details_text.config(state=tk.DISABLED)

        # GDrive token actions
        gdrive_token_frame = ttk.Frame(gdrive_frame)
        gdrive_token_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(gdrive_token_frame, text="Check Token", command=self.check_gdrive_token).pack(side=tk.LEFT, padx=2)
        ttk.Button(gdrive_token_frame, text="Download Latest Token", command=self.download_gdrive_token).pack(side=tk.LEFT, padx=2)

        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Client Log", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # Filters frame
        filters_frame = ttk.Frame(log_frame)
        filters_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(filters_frame, text="Filter:").pack(side=tk.LEFT, padx=(0, 5))
        self.filter_var = tk.StringVar(value="All")
        ttk.Radiobutton(filters_frame, text="All", variable=self.filter_var, value="All", command=self.apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filters_frame, text="Backup Only", variable=self.filter_var, value="Backup", command=self.apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filters_frame, text="MEGA Only", variable=self.filter_var, value="MEGA", command=self.apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filters_frame, text="GDrive Only", variable=self.filter_var, value="GDRIVE", command=self.apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filters_frame, text="Errors Only", variable=self.filter_var, value="Error", command=self.apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filters_frame, text="Connection Issues", variable=self.filter_var, value="Connection", command=self.apply_filter).pack(side=tk.LEFT, padx=5)

        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, width=80, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Options frame
        options_frame = ttk.Frame(main_frame)
        options_frame.pack(fill=tk.X, pady=(10, 0))

        # Auto-scroll checkbox
        ttk.Checkbutton(options_frame, text="Auto-scroll", variable=self.auto_scroll).pack(side=tk.LEFT, padx=5)

        # Auto-refresh checkbox
        ttk.Checkbutton(options_frame, text="Auto-refresh", variable=self.auto_refresh,
                        command=self.toggle_auto_refresh).pack(side=tk.LEFT, padx=5)

        # Open config button
        ttk.Button(options_frame, text="Open Config Tool", command=self.open_config_tool).pack(side=tk.RIGHT, padx=5)

        # Clear log button
        ttk.Button(options_frame, text="Clear Log Display", command=self.clear_log_display).pack(side=tk.RIGHT, padx=5)

    def check_client_status(self):
        """Check if the hidden client is running"""
        try:
            # Try to create the mutex without taking ownership
            mutex = ctypes.windll.kernel32.OpenMutexW(0x00100000, 0, MUTEX_NAME)

            if mutex:
                # Mutex exists, client is running
                ctypes.windll.kernel32.CloseHandle(mutex)
                self.client_running = True
                self.status_label.config(text="Client is running", foreground="green")
            else:
                # Mutex doesn't exist, client is not running
                self.client_running = False
                self.status_label.config(text="Client is not running", foreground="red")
        except Exception as e:
            logger.error(f"Error checking client status: {e}")
            self.status_label.config(text=f"Error checking status: {str(e)}", foreground="red")

    def refresh_log(self):
        """Refresh log display"""
        try:
            # Find log file
            log_file_path = find_log_file()

            if not os.path.exists(log_file_path):
                self.log_text.delete(1.0, tk.END)
                self.log_text.insert(tk.END, "Log file not found. Searched in:\n")
                for path in [
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.log"),
                    "ifess_client_hidden.log",
                    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.log"),
                    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_client_hidden.log"),
                ]:
                    self.log_text.insert(tk.END, f"- {path}\n")
                return

            # Get file size
            file_size = os.path.getsize(log_file_path)

            # If file size is the same as last time, no need to refresh
            if file_size == self.log_position and self.log_position > 0:
                return

            # Open log file
            with open(log_file_path, 'r', encoding='utf-8', errors='replace') as f:
                # If auto-scroll is enabled, read the whole file
                if self.auto_scroll.get() or self.log_position == 0:
                    log_content = f.read()
                    self.log_text.delete(1.0, tk.END)
                    self.log_text.insert(tk.END, log_content)
                    self.log_text.see(tk.END)
                else:
                    # Otherwise, just read new content
                    f.seek(self.log_position)
                    new_content = f.read()
                    if new_content:
                        self.log_text.insert(tk.END, new_content)

                # Update position
                self.log_position = file_size

                # Check for various activities in the log content
                self.check_for_backup_activities()
                self.check_for_connection_issues()
                self.check_for_mega_upload_activities()
                self.check_for_gdrive_upload_activities()
                
                # Update connection statistics display
                self.update_connection_statistics()

                # Apply filter if not set to All
                if self.filter_var.get() != "All":
                    self.apply_filter()

        except Exception as e:
            logger.error(f"Error refreshing log: {e}")
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, f"Error refreshing log: {str(e)}\n\n{traceback.format_exc()}")

    def check_for_connection_issues(self):
        """Check for connection issues in logs and update connection statistics"""
        try:
            # Get current log content
            log_content = self.log_text.get(1.0, tk.END)

            # Check for connection lost or disconnect messages
            if "Connection may be lost" in log_content or "Connection lost with server" in log_content or "Disconnected from server" in log_content:
                self.connection_status = "Disconnected"
                self.connection_label.config(text="Connection: Disconnected", foreground="red")

                # Try to find the time of the last connection drop
                disconnect_patterns = [
                    r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+Connection may be lost",
                    r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+Disconnected from server",
                    r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+Connection lost with server"
                ]
                
                for pattern in disconnect_patterns:
                    match = re.search(pattern, log_content)
                    if match:
                        self.last_connection_drop = match.group(1)
                        self.connection_stats.track_disconnection("Server disconnected")
                        logger.info(f"Detected connection drop at {self.last_connection_drop}")
                        break

            # Check for reconnection attempts with enhanced patterns
            reconnect_patterns = [
                "Attempting to reconnect",
                "Attempting to connect to",
                "Connection failed, waiting"
            ]
            
            total_attempts = 0
            for pattern in reconnect_patterns:
                total_attempts += log_content.count(pattern)
            
            if total_attempts > self.reconnection_attempts:
                self.reconnection_attempts = total_attempts
                self.connection_stats.track_connection_attempt()
                logger.info(f"Detected {total_attempts} total reconnection attempts")

            # Check for successful connection
            if "Connected to server" in log_content and "Registered to server" in log_content:
                last_connect_match = re.search(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+Connected to server", log_content)
                if last_connect_match:
                    self.connection_status = "Connected"
                    self.connection_label.config(text="Connection: Connected", foreground="green")
                    self.connection_stats.track_connection_established()
                    self.connection_manager.reset_backoff()
                    logger.info(f"Detected successful connection at {last_connect_match.group(1)}")

            # Check for exponential backoff messages
            backoff_match = re.search(r"waiting (\d+\.\d+) seconds before next attempt", log_content)
            if backoff_match:
                current_interval = float(backoff_match.group(1))
                self.connection_manager.current_attempt = max(0, int(math.log2(current_interval / self.connection_manager.base_interval)))

            # Update connection statistics display
            self.update_connection_statistics()

            # Update connection details text
            details = ""
            if self.last_connection_drop:
                details += f"Last connection drop: {self.last_connection_drop}\n"
            if self.reconnection_attempts > 0:
                details += f"Reconnection attempts: {self.reconnection_attempts}\n"
            
            # Add connection backoff information
            if self.connection_manager.current_attempt > 0:
                next_interval = self.connection_manager.get_next_interval()
                details += f"Next backoff interval: {next_interval:.1f}s\n"

            # Add to backup details text
            self.update_backup_details(additional_info=details if details else None)

        except Exception as e:
            logger.error(f"Error checking for connection issues: {e}")

    def update_connection_statistics(self):
        """Update connection statistics display"""
        try:
            # Update uptime
            if self.connection_stats.connection_start_time:
                current_uptime = time.time() - self.connection_stats.connection_start_time
                total_uptime = self.connection_stats.total_uptime + current_uptime
                hours = int(total_uptime // 3600)
                minutes = int((total_uptime % 3600) // 60)
                seconds = int(total_uptime % 60)
                self.uptime_label.config(text=f"Uptime: {hours:02d}:{minutes:02d}:{seconds:02d}", foreground="green")
            else:
                total_uptime = self.connection_stats.total_uptime
                if total_uptime > 0:
                    hours = int(total_uptime // 3600)
                    minutes = int((total_uptime % 3600) // 60)
                    seconds = int(total_uptime % 60)
                    self.uptime_label.config(text=f"Total uptime: {hours:02d}:{minutes:02d}:{seconds:02d}", foreground="orange")
                else:
                    self.uptime_label.config(text="Uptime: Not connected", foreground="gray")

            # Update reconnection attempts
            self.reconnect_attempts_label.config(
                text=f"Reconnection attempts: {self.connection_stats.reconnection_attempts}",
                foreground="blue" if self.connection_stats.reconnection_attempts > 0 else "gray"
            )

            # Update backoff interval
            if self.connection_manager.current_attempt > 0:
                next_interval = self.connection_manager.get_next_interval()
                self.backoff_interval_label.config(
                    text=f"Next backoff: {next_interval:.1f}s (attempt {self.connection_manager.current_attempt})",
                    foreground="orange"
                )
            else:
                self.backoff_interval_label.config(text="Backoff interval: N/A", foreground="gray")

            # Update last disconnect
            if self.connection_stats.last_disconnection_time:
                disconnect_time = time.strftime('%H:%M:%S', time.localtime(self.connection_stats.last_disconnection_time))
                self.last_disconnect_label.config(
                    text=f"Last disconnect: {disconnect_time} ({self.connection_stats.disconnection_reason})",
                    foreground="red"
                )
            else:
                self.last_disconnect_label.config(text="Last disconnect: N/A", foreground="gray")

        except Exception as e:
            logger.error(f"Error updating connection statistics: {e}")

    def check_for_backup_activities(self):
        """Check log for backup activities and update status"""
        try:
            # Get current log content
            log_content = self.log_text.get(1.0, tk.END)

            # Check for backup request received
            if "[BACKUP] DATABASE BACKUP REQUEST RECEIVED" in log_content or "===== DATABASE BACKUP REQUEST RECEIVED =====" in log_content:
                self.backup_status = "Backup request received"
                self.backup_status_label.config(text=self.backup_status, foreground="blue")
                self.last_backup_time = time.time()

                # Extract detailed information if available
                match = re.search(r"\[BACKUP\] Database path: (.+)", log_content)
                if match:
                    self.backup_details["Database Path"] = match.group(1)

                # Update progress
                self.backup_progress = 5
                self.progress_bar["value"] = 5
                self.progress_label.config(text="5% (Request received)")

                # Update details text
                self.update_backup_details()

            # Check for backup started
            if "Starting backup of database:" in log_content:
                self.backup_status = "Backup started"
                self.backup_status_label.config(text=self.backup_status, foreground="blue")

                # Extract file information
                match = re.search(r"Starting backup of database: (.+), size: (\d+) bytes", log_content)
                if match:
                    self.backup_details["Filename"] = match.group(1)
                    self.backup_details["Size"] = match.group(2) + " bytes"

                # Update progress
                self.backup_progress = 10
                self.progress_bar["value"] = 10
                self.progress_label.config(text="10% (Backup started)")

                # Update details text
                self.update_backup_details()

            # Check for chunk progress
            chunk_matches = re.findall(r"\[BACKUP\] Sent chunk (\d+)/(\d+) \((\d+)%\)", log_content)
            if chunk_matches:
                last_match = chunk_matches[-1]
                current_chunk = int(last_match[0])
                total_chunks = int(last_match[1])
                progress = int(last_match[2])

                self.backup_status = f"Sending chunk {current_chunk}/{total_chunks}"
                self.backup_status_label.config(text=self.backup_status, foreground="blue")

                self.backup_details["Current Chunk"] = str(current_chunk)
                self.backup_details["Total Chunks"] = str(total_chunks)
                self.backup_details["Progress"] = f"{progress}%"

                # Update progress bar
                self.backup_progress = progress
                self.progress_bar["value"] = progress
                self.progress_label.config(text=f"{progress}% (Chunk {current_chunk}/{total_chunks})")

                # Update details text
                self.update_backup_details()

            # Check for completion
            if "===== DATABASE BACKUP COMPLETED SUCCESSFULLY =====" in log_content:
                self.backup_status = "Backup completed successfully"
                self.backup_status_label.config(text=self.backup_status, foreground="green")

                # Find completion details
                file_match = re.search(r"\[BACKUP\] File: (.+), Size: (.+)", log_content)
                time_match = re.search(r"\[BACKUP\] Time: (.+) seconds, Speed: (.+) KB/s", log_content)

                if file_match:
                    self.backup_details["Filename"] = file_match.group(1)
                    self.backup_details["Size"] = file_match.group(2)

                if time_match:
                    self.backup_details["Duration"] = time_match.group(1) + " seconds"
                    self.backup_details["Speed"] = time_match.group(2) + " KB/s"

                # Update progress to 100%
                self.backup_progress = 100
                self.progress_bar["value"] = 100
                self.progress_label.config(text="100% (Completed)")

                # Update details text
                self.update_backup_details()

            # Check for failure
            if "===== DATABASE BACKUP FAILED =====" in log_content:
                self.backup_status = "Backup failed"
                self.backup_status_label.config(text=self.backup_status, foreground="red")

                # Find error details
                error_match = re.search(r"\[BACKUP\] Error sending database file: (.+)", log_content)
                if error_match:
                    self.backup_details["Error"] = error_match.group(1)

                # Update progress (keep previous progress)
                self.progress_label.config(text=f"{self.backup_progress}% (Failed)")

                # Update details text
                self.update_backup_details()

        except Exception as e:
            logger.error(f"Error checking for backup activities: {e}")

    def update_backup_details(self, additional_info=None):
        """Update backup details text"""
        try:
            self.backup_details_text.config(state=tk.NORMAL)
            self.backup_details_text.delete(1.0, tk.END)

            if self.backup_details:
                details_text = "Backup Details:\n"
                for key, value in self.backup_details.items():
                    details_text += f"{key}: {value}\n"

                if self.last_backup_time:
                    elapsed = time.time() - self.last_backup_time
                    details_text += f"Elapsed Time: {elapsed:.1f} seconds\n"

                if additional_info:
                    details_text += "\nConnection Info:\n" + additional_info

                self.backup_details_text.insert(tk.END, details_text)
            else:
                self.backup_details_text.insert(tk.END, "No backup details available")

            self.backup_details_text.config(state=tk.DISABLED)
        except Exception as e:
            logger.error(f"Error updating backup details: {e}")

    def update_mega_details(self, additional_info=None):
        """Update MEGA upload details text"""
        try:
            self.mega_details_text.config(state=tk.NORMAL)
            self.mega_details_text.delete(1.0, tk.END)

            if self.mega_upload_details:
                details_text = "MEGA Upload Details:\n"
                for key, value in self.mega_upload_details.items():
                    details_text += f"{key}: {value}\n"

                if self.last_mega_upload_time:
                    elapsed = time.time() - self.last_mega_upload_time
                    details_text += f"Elapsed Time: {elapsed:.1f} seconds\n"

                if additional_info:
                    details_text += "\n" + additional_info

                self.mega_details_text.insert(tk.END, details_text)
            else:
                self.mega_details_text.insert(tk.END, "No MEGA upload details available")

            self.mega_details_text.config(state=tk.DISABLED)
        except Exception as e:
            logger.error(f"Error updating MEGA upload details: {e}")

    def update_gdrive_details(self, additional_info=None):
        """Update Google Drive upload details text"""
        try:
            self.gdrive_details_text.config(state=tk.NORMAL)
            self.gdrive_details_text.delete(1.0, tk.END)

            if self.gdrive_upload_details:
                details_text = "Google Drive Upload Details:\n"
                for key, value in self.gdrive_upload_details.items():
                    details_text += f"{key}: {value}\n"

                if self.last_gdrive_upload_time:
                    elapsed = time.time() - self.last_gdrive_upload_time
                    details_text += f"Elapsed Time: {elapsed:.1f} seconds\n"

                if additional_info:
                    details_text += "\n" + additional_info

                self.gdrive_details_text.insert(tk.END, details_text)
            else:
                self.gdrive_details_text.insert(tk.END, "No Google Drive upload details available")

            self.gdrive_details_text.config(state=tk.DISABLED)
        except Exception as e:
            logger.error(f"Error updating Google Drive upload details: {e}")

    def check_gdrive_token(self):
        """Check Google Drive token status"""
        try:
            # Try to import token_downloader
            try:
                import token_downloader

                # Show status message
                self.gdrive_token_label.config(text="Token status: Checking...", foreground="blue")
                self.root.update()

                # Get token data
                token_data = token_downloader.get_token()

                if token_data:
                    # Check if token has expiry information
                    if 'expiry' in token_data:
                        import datetime

                        # Parse expiry time
                        expiry_str = token_data['expiry']
                        try:
                            # Try ISO format first
                            expiry_time = datetime.datetime.fromisoformat(expiry_str.replace('Z', '+00:00'))
                        except ValueError:
                            try:
                                # Try RFC 3339 format
                                import dateutil.parser
                                expiry_time = dateutil.parser.parse(expiry_str)
                            except:
                                # Fall back to simple format
                                expiry_time = datetime.datetime.strptime(expiry_str, "%Y-%m-%dT%H:%M:%S.%fZ")

                        # Get current time in UTC
                        now = datetime.datetime.now(datetime.timezone.utc)

                        # Make sure expiry_time has timezone info
                        if expiry_time.tzinfo is None:
                            expiry_time = expiry_time.replace(tzinfo=datetime.timezone.utc)

                        # Calculate time until expiry
                        time_until_expiry = expiry_time - now

                        # Format the expiry information
                        if time_until_expiry.total_seconds() > 0:
                            hours_until_expiry = time_until_expiry.total_seconds() / 3600
                            if hours_until_expiry > 24:
                                days = hours_until_expiry / 24
                                expiry_info = f"Valid for {days:.1f} days"
                                self.gdrive_token_label.config(text=f"Token status: {expiry_info}", foreground="green")
                            else:
                                expiry_info = f"Valid for {hours_until_expiry:.1f} hours"
                                color = "green" if hours_until_expiry > 1 else "orange"
                                self.gdrive_token_label.config(text=f"Token status: {expiry_info}", foreground=color)
                        else:
                            self.gdrive_token_label.config(text="Token status: Expired", foreground="red")

                        # Update details
                        self.gdrive_upload_details["Token Expiry"] = expiry_str
                        self.gdrive_upload_details["Token Status"] = expiry_info if time_until_expiry.total_seconds() > 0 else "Expired"
                        self.update_gdrive_details()
                    else:
                        self.gdrive_token_label.config(text="Token status: No expiry information", foreground="orange")
                else:
                    self.gdrive_token_label.config(text="Token status: Not found", foreground="red")
            except ImportError:
                self.gdrive_token_label.config(text="Token status: token_downloader not available", foreground="red")
                messagebox.showerror("Error", "token_downloader module not found")
            except Exception as e:
                self.gdrive_token_label.config(text=f"Token status: Error checking", foreground="red")
                logger.error(f"Error checking token: {e}")
                logger.error(traceback.format_exc())
                messagebox.showerror("Error", f"Error checking token: {str(e)}")
        except Exception as e:
            logger.error(f"Error in check_gdrive_token: {e}")
            logger.error(traceback.format_exc())
            messagebox.showerror("Error", f"Error checking token: {str(e)}")

    def download_gdrive_token(self):
        """Download the latest token from Google Drive"""
        try:
            # Try to import token_downloader
            try:
                import token_downloader

                # Show status message
                self.gdrive_token_label.config(text="Token status: Downloading...", foreground="blue")
                self.root.update()

                # Download token
                success = token_downloader.download_token()

                if success:
                    # Get token data to check expiry
                    token_data = token_downloader.get_token()

                    if token_data and 'expiry' in token_data:
                        import datetime

                        # Parse expiry time
                        expiry_str = token_data['expiry']
                        try:
                            # Try ISO format first
                            expiry_time = datetime.datetime.fromisoformat(expiry_str.replace('Z', '+00:00'))
                        except ValueError:
                            try:
                                # Try RFC 3339 format
                                import dateutil.parser
                                expiry_time = dateutil.parser.parse(expiry_str)
                            except:
                                # Fall back to simple format
                                expiry_time = datetime.datetime.strptime(expiry_str, "%Y-%m-%dT%H:%M:%S.%fZ")

                        # Get current time in UTC
                        now = datetime.datetime.now(datetime.timezone.utc)

                        # Make sure expiry_time has timezone info
                        if expiry_time.tzinfo is None:
                            expiry_time = expiry_time.replace(tzinfo=datetime.timezone.utc)

                        # Calculate time until expiry
                        time_until_expiry = expiry_time - now

                        # Format the expiry information
                        if time_until_expiry.total_seconds() > 0:
                            hours_until_expiry = time_until_expiry.total_seconds() / 3600
                            if hours_until_expiry > 24:
                                days = hours_until_expiry / 24
                                expiry_info = f"Valid for {days:.1f} days"
                                self.gdrive_token_label.config(text=f"Token status: {expiry_info} (Downloaded)", foreground="green")
                            else:
                                expiry_info = f"Valid for {hours_until_expiry:.1f} hours"
                                color = "green" if hours_until_expiry > 1 else "orange"
                                self.gdrive_token_label.config(text=f"Token status: {expiry_info} (Downloaded)", foreground=color)
                        else:
                            self.gdrive_token_label.config(text="Token status: Expired (Downloaded)", foreground="red")

                        # Update details
                        self.gdrive_upload_details["Token Expiry"] = expiry_str
                        self.gdrive_upload_details["Token Status"] = f"{expiry_info} (Downloaded)" if time_until_expiry.total_seconds() > 0 else "Expired (Downloaded)"
                        self.update_gdrive_details()

                        # Show success message
                        messagebox.showinfo("Success", f"Token downloaded successfully. {expiry_info}")
                    else:
                        self.gdrive_token_label.config(text="Token status: Downloaded (No expiry info)", foreground="orange")
                        messagebox.showinfo("Success", "Token downloaded successfully, but no expiry information found.")
                else:
                    self.gdrive_token_label.config(text="Token status: Download failed", foreground="red")
                    messagebox.showerror("Error", "Failed to download token from Google Drive")
            except ImportError:
                self.gdrive_token_label.config(text="Token status: token_downloader not available", foreground="red")
                messagebox.showerror("Error", "token_downloader module not found")
            except Exception as e:
                self.gdrive_token_label.config(text=f"Token status: Error downloading", foreground="red")
                logger.error(f"Error downloading token: {e}")
                logger.error(traceback.format_exc())
                messagebox.showerror("Error", f"Error downloading token: {str(e)}")
        except Exception as e:
            logger.error(f"Error in download_gdrive_token: {e}")
            logger.error(traceback.format_exc())
            messagebox.showerror("Error", f"Error downloading token: {str(e)}")

    def check_for_gdrive_upload_activities(self):
        """Check log for Google Drive upload activities and update status"""
        try:
            # Get current log content
            log_content = self.log_text.get(1.0, tk.END)

            # Check for token status
            token_expiry_match = re.search(r"Token expiry: (.+)", log_content)
            if token_expiry_match:
                expiry_str = token_expiry_match.group(1)
                try:
                    # Try to parse the expiry date
                    import datetime
                    try:
                        # Try ISO format first
                        expiry_time = datetime.datetime.fromisoformat(expiry_str.replace('Z', '+00:00'))
                    except ValueError:
                        # Fall back to simple format
                        expiry_time = datetime.datetime.strptime(expiry_str, "%Y-%m-%dT%H:%M:%S.%fZ")

                    # Get current time in UTC
                    now = datetime.datetime.now(datetime.timezone.utc)

                    # Make sure expiry_time has timezone info
                    if expiry_time.tzinfo is None:
                        expiry_time = expiry_time.replace(tzinfo=datetime.timezone.utc)

                    # Calculate time until expiry
                    time_until_expiry = expiry_time - now

                    # Format the expiry information
                    if time_until_expiry.total_seconds() > 0:
                        hours_until_expiry = time_until_expiry.total_seconds() / 3600
                        if hours_until_expiry > 24:
                            days = hours_until_expiry / 24
                            expiry_info = f"Valid for {days:.1f} days"
                            self.gdrive_token_label.config(text=f"Token status: {expiry_info}", foreground="green")
                        else:
                            expiry_info = f"Valid for {hours_until_expiry:.1f} hours"
                            color = "green" if hours_until_expiry > 1 else "orange"
                            self.gdrive_token_label.config(text=f"Token status: {expiry_info}", foreground=color)
                    else:
                        self.gdrive_token_label.config(text="Token status: Expired", foreground="red")
                except Exception as e:
                    logger.error(f"Error parsing token expiry: {e}")
                    self.gdrive_token_label.config(text=f"Token status: Unknown format ({expiry_str})", foreground="orange")

            # Check for token download activities
            if "Downloading latest token from Google Drive folder" in log_content:
                self.gdrive_token_label.config(text="Token status: Downloading...", foreground="blue")

            if "Token downloaded and saved to" in log_content:
                self.gdrive_token_label.config(text="Token status: Recently downloaded", foreground="green")

            if "Successfully downloaded and validated" in log_content:
                self.gdrive_token_label.config(text="Token status: Valid (recently validated)", foreground="green")

            if "Failed to download token" in log_content:
                self.gdrive_token_label.config(text="Token status: Download failed", foreground="red")

            # Log the raw content for debugging if in verbose mode
            if self.verbose_mode:
                logger.debug(f"Processing log content for GDrive activities, content length: {len(log_content)} bytes")
                # Find all lines with GDrive information
                gdrive_lines = [line for line in log_content.split('\n') if "[GDRIVE]" in line]
                if gdrive_lines:
                    logger.debug(f"Found {len(gdrive_lines)} GDrive-related lines in log")
                    for line in gdrive_lines[-10:]:  # Show last 10 lines only to avoid flooding
                        logger.debug(f"GDrive log line: {line}")

            # Check for GDrive upload request received
            if "[GDRIVE] ===== RECEIVED GDRIVE UPLOAD REQUEST =====" in log_content:
                self.gdrive_upload_status = "GDrive upload request received"
                self.gdrive_status_label.config(text=self.gdrive_upload_status, foreground="blue")
                self.last_gdrive_upload_time = time.time()

                # Extract detailed information if available
                match = re.search(r"\[GDRIVE\] Request data: (.+)", log_content)
                if match:
                    self.gdrive_upload_details["Request Data"] = match.group(1)

                # Update progress
                self.gdrive_upload_progress = 5
                self.gdrive_progress_bar["value"] = 5
                self.gdrive_progress_label.config(text="5% (Request received)")

                # Update details text
                self.update_gdrive_details()

            # Check for GDrive upload started
            if "[GDRIVE] Starting upload to GDrive:" in log_content:
                self.gdrive_upload_status = "GDrive upload started"
                self.gdrive_status_label.config(text=self.gdrive_upload_status, foreground="blue")

                # Extract file information
                match = re.search(r"\[GDRIVE\] Preparing to upload file: (.+) \((.+)\)", log_content)
                if match:
                    self.gdrive_upload_details["Filename"] = match.group(1)
                    self.gdrive_upload_details["Size"] = match.group(2)

                # Update progress
                self.gdrive_upload_progress = 10
                self.gdrive_progress_bar["value"] = 10
                self.gdrive_progress_label.config(text="10% (Upload started)")

                # Update details text
                self.update_gdrive_details()

            # Check for upload progress
            progress_matches = re.findall(r"\[GDRIVE\] Upload progress: (\d+)%", log_content)
            if progress_matches:
                last_match = progress_matches[-1]
                progress = int(last_match)

                self.gdrive_upload_status = f"Uploading to GDrive: {progress}%"
                self.gdrive_status_label.config(text=self.gdrive_upload_status, foreground="blue")

                self.gdrive_upload_details["Progress"] = f"{progress}%"

                # Update progress bar
                self.gdrive_upload_progress = progress
                self.gdrive_progress_bar["value"] = progress
                self.gdrive_progress_label.config(text=f"{progress}%")

                # Update details text
                self.update_gdrive_details()

            # Check for detailed upload information
            uploaded_matches = re.findall(r"\[GDRIVE\] Uploaded: ([\d\.]+) MB of ([\d\.]+) MB", log_content)
            if uploaded_matches:
                last_match = uploaded_matches[-1]
                mb_uploaded = float(last_match[0])
                mb_total = float(last_match[1])

                # Calculate percentage
                if mb_total > 0:
                    percent = (mb_uploaded / mb_total) * 100

                    # Update status
                    self.gdrive_upload_status = f"Uploading to GDrive: {mb_uploaded} MB of {mb_total} MB"
                    self.gdrive_status_label.config(text=self.gdrive_upload_status, foreground="blue")

                    # Add detailed information
                    self.gdrive_upload_details["Uploaded"] = f"{mb_uploaded} MB"
                    self.gdrive_upload_details["Total Size"] = f"{mb_total} MB"
                    self.gdrive_upload_details["Progress"] = f"{percent:.1f}%"

                    # Look for speed information
                    speed_match = re.search(r"\[GDRIVE\] Speed: ([\d\.]+) KB/s", log_content)
                    if speed_match:
                        speed = float(speed_match.group(1))
                        self.gdrive_upload_details["Transfer Rate"] = f"{speed} KB/s"

                    # Look for ETA information
                    eta_match = re.search(r"\[GDRIVE\] Speed: [\d\.]+ KB/s, ETA: (.+)", log_content)
                    if eta_match:
                        eta = eta_match.group(1)
                        self.gdrive_upload_details["ETA"] = eta

                    # Update progress bar
                    self.gdrive_upload_progress = percent
                    self.gdrive_progress_bar["value"] = percent
                    self.gdrive_progress_label.config(text=f"{percent:.1f}% ({mb_uploaded} MB of {mb_total} MB)")

                    # Update details text
                    self.update_gdrive_details()

            # Check for completion
            if "[GDRIVE] ===== GDRIVE UPLOAD COMPLETED =====" in log_content:
                self.gdrive_upload_status = "GDrive upload completed successfully"
                self.gdrive_status_label.config(text=self.gdrive_upload_status, foreground="green")

                # Find completion details
                result_match = re.search(r"\[GDRIVE\] Upload result: (.+)", log_content)
                if result_match:
                    self.gdrive_upload_details["Result"] = result_match.group(1)

                # Look for file ID and link
                file_id_match = re.search(r"\[GDRIVE\] File ID: (.+)", log_content)
                if file_id_match:
                    self.gdrive_upload_details["File ID"] = file_id_match.group(1)

                link_match = re.search(r"\[GDRIVE\] View Link: (.+)", log_content)
                if link_match:
                    self.gdrive_upload_details["View Link"] = link_match.group(1)

                download_match = re.search(r"\[GDRIVE\] Download Link: (.+)", log_content)
                if download_match:
                    self.gdrive_upload_details["Download Link"] = download_match.group(1)

                duration_match = re.search(r"\[GDRIVE\] Duration: ([\d\.]+) seconds", log_content)
                if duration_match:
                    self.gdrive_upload_details["Duration"] = f"{duration_match.group(1)} seconds"

                # Update progress to 100%
                self.gdrive_upload_progress = 100
                self.gdrive_progress_bar["value"] = 100
                self.gdrive_progress_label.config(text="100% (Completed)")

                # Update details text
                self.update_gdrive_details()

            # Check for failure
            if "[GDRIVE] ===== UPLOAD ERROR =====" in log_content:
                self.gdrive_upload_status = "GDrive upload failed"
                self.gdrive_status_label.config(text=self.gdrive_upload_status, foreground="red")

                # Find error details
                error_match = re.search(r"\[GDRIVE\] Error uploading file: (.+)", log_content)
                if error_match:
                    self.gdrive_upload_details["Error"] = error_match.group(1)

                # Update progress (keep previous progress)
                self.gdrive_progress_label.config(text=f"{self.gdrive_upload_progress:.1f}% (Failed)")

                # Update details text
                self.update_gdrive_details()

        except Exception as e:
            logger.error(f"Error checking for GDrive upload activities: {e}")
            logger.error(traceback.format_exc())

    def check_for_mega_upload_activities(self):
        """Check log for MEGA upload activities and update status"""
        try:
            # Get current log content
            log_content = self.log_text.get(1.0, tk.END)

            # Check for asyncio.coroutine errors
            if "asyncio.coroutine" in log_content and "AttributeError" in log_content:
                self.mega_upload_status = "Error: asyncio.coroutine issue"
                self.mega_status_label.config(text="MEGA: Python 3.10+ compatibility issue", foreground="red")
                self.mega_upload_details["Error"] = "Python 3.10+ compatibility issue with asyncio.coroutine"
                self.mega_upload_details["Solution"] = "Run with run_with_mega_patch.bat to apply patch"
                self.update_mega_details()

                # Update progress to show error
                self.mega_progress_bar["value"] = 0
                self.mega_progress_label.config(text="Error: Python 3.10+ compatibility issue")
                return

            # Check for MEGA client initialization
            if "Using patched MEGA client for Python 3.10+ compatibility" in log_content:
                self.mega_status_label.config(text="MEGA: Using patched client", foreground="green")
                self.mega_upload_details["Client Type"] = "Patched MEGA client (Python 3.10+ compatible)"
                self.update_mega_details()
            elif "MEGA patch for asyncio.coroutine was applied" in log_content:
                self.mega_status_label.config(text="MEGA: Patch applied", foreground="green")
                self.mega_upload_details["Patch Status"] = "asyncio.coroutine patch applied successfully"
                self.update_mega_details()
            elif "MEGA patch for asyncio.coroutine was NOT applied" in log_content:
                self.mega_status_label.config(text="MEGA: Patch not applied", foreground="orange")
                self.mega_upload_details["Warning"] = "asyncio.coroutine patch not applied, may cause issues"
                self.update_mega_details()

            # Log the raw content for debugging if in verbose mode
            if self.verbose_mode:
                logger.debug(f"Processing log content for MEGA activities, content length: {len(log_content)} bytes")
                # Find all lines with MEGA or upload information
                mega_lines = [line for line in log_content.split('\n') if "mega.mega" in line or "[MEGA]" in line or "uploaded" in line.lower()]
                if mega_lines:
                    logger.debug(f"Found {len(mega_lines)} MEGA-related lines in log")
                    for line in mega_lines[-10:]:  # Show last 10 lines only to avoid flooding
                        logger.debug(f"MEGA log line: {line}")

            # Check for MEGA upload request received
            if "[MEGA] ===== RECEIVED MEGA UPLOAD REQUEST =====" in log_content:
                self.mega_upload_status = "MEGA upload request received"
                self.mega_status_label.config(text=self.mega_upload_status, foreground="blue")
                self.last_mega_upload_time = time.time()

                # Extract detailed information if available
                match = re.search(r"\[MEGA\] Request data: (.+)", log_content)
                if match:
                    self.mega_upload_details["Request Data"] = match.group(1)

                # Update progress
                self.mega_upload_progress = 5
                self.mega_progress_bar["value"] = 5
                self.mega_progress_label.config(text="5% (Request received)")

                # Update details text
                self.update_mega_details()

            # Check for MEGA upload started
            if "[MEGA] Starting upload to MEGA:" in log_content:
                self.mega_upload_status = "MEGA upload started"
                self.mega_status_label.config(text=self.mega_upload_status, foreground="blue")

                # Extract file information
                match = re.search(r"\[MEGA\] Starting upload to MEGA: (.+) \((\d+) bytes\)", log_content)
                if match:
                    self.mega_upload_details["Filename"] = match.group(1)
                    self.mega_upload_details["Size"] = match.group(2) + " bytes"

                # Update progress
                self.mega_upload_progress = 10
                self.mega_progress_bar["value"] = 10
                self.mega_progress_label.config(text="10% (Upload started)")

                # Update details text
                self.update_mega_details()

            # Check for upload progress
            progress_matches = re.findall(r"\[PROGRESS\] Estimated progress: (\d+\.\d+)%", log_content)
            if progress_matches:
                last_match = progress_matches[-1]
                progress = float(last_match)

                self.mega_upload_status = f"Uploading to MEGA: {progress:.1f}%"
                self.mega_status_label.config(text=self.mega_upload_status, foreground="blue")

                self.mega_upload_details["Progress"] = f"{progress:.1f}%"

                # Extract time information
                time_match = re.search(r"\[PROGRESS\] Upload sedang berjalan... \((\d+) detik\)", log_content)
                if time_match:
                    self.mega_upload_details["Elapsed Time"] = f"{time_match.group(1)} seconds"

                # Update progress bar
                self.mega_upload_progress = progress
                self.mega_progress_bar["value"] = progress
                self.mega_progress_label.config(text=f"{progress:.1f}%")

                # Update details text
                self.update_mega_details()

            # Helper function to format bytes
            def format_bytes(size):
                for unit in ['B', 'KB', 'MB', 'GB']:
                    if size < 1024.0:
                        return f"{size:.2f} {unit}"
                    size /= 1024.0
                return f"{size:.2f} TB"

            # Check for bytes uploaded (direct from mega.mega logs)
            bytes_matches = re.findall(r"mega\.mega - INFO - (\d+) of (\d+) uploaded", log_content)

            # Also check for estimated progress from mega_client_fixed logs
            estimated_progress_matches = re.findall(r"\[PROGRESS\] Estimated progress: (\d+\.\d+)%", log_content)

            # Track if we have any progress information
            has_progress_info = False
            percent = 0

            if bytes_matches:
                has_progress_info = True
                last_match = bytes_matches[-1]
                bytes_uploaded = int(last_match[0])
                total_bytes = int(last_match[1])

                # Calculate percentage
                if total_bytes > 0:
                    percent = (bytes_uploaded / total_bytes) * 100

                    # Always update to show the latest transfer information
                    self.mega_upload_status = f"Uploading to MEGA: {format_bytes(bytes_uploaded)} of {format_bytes(total_bytes)}"
                    self.mega_status_label.config(text=self.mega_upload_status, foreground="blue")

                    # Add detailed information
                    self.mega_upload_details["Bytes Uploaded"] = f"{bytes_uploaded:,} bytes ({format_bytes(bytes_uploaded)})"
                    self.mega_upload_details["Total Size"] = f"{total_bytes:,} bytes ({format_bytes(total_bytes)})"
                    self.mega_upload_details["Progress"] = f"{percent:.1f}%"

                    # Calculate transfer rate if we have time information
                    time_match = re.search(r"\[PROGRESS\] Upload sedang berjalan... \((\d+) detik\)", log_content)
                    if time_match and time_match.group(1):
                        elapsed_seconds = int(time_match.group(1))
                        if elapsed_seconds > 0:
                            bytes_per_second = bytes_uploaded / elapsed_seconds
                            self.mega_upload_details["Transfer Rate"] = f"{format_bytes(bytes_per_second)}/s"

                            # Estimate remaining time
                            remaining_bytes = total_bytes - bytes_uploaded
                            if bytes_per_second > 0:
                                remaining_seconds = remaining_bytes / bytes_per_second
                                remaining_minutes = int(remaining_seconds / 60)
                                remaining_seconds = int(remaining_seconds % 60)
                                self.mega_upload_details["Estimated Time Remaining"] = f"{remaining_minutes} min {remaining_seconds} sec"

                    # Update progress bar
                    self.mega_upload_progress = percent
                    self.mega_progress_bar["value"] = percent
                    self.mega_progress_label.config(text=f"{percent:.1f}% ({format_bytes(bytes_uploaded)} of {format_bytes(total_bytes)})")

                    # Update details text
                    self.update_mega_details()

                    # Log the update for debugging
                    logger.debug(f"Updated MEGA progress: {bytes_uploaded} of {total_bytes} bytes ({percent:.1f}%)")

            # Check for estimated progress if we don't have bytes information or if it's more recent
            if estimated_progress_matches:
                # Get the latest estimated progress
                estimated_percent = float(estimated_progress_matches[-1])

                # Check if this is more recent or we don't have bytes info
                if not bytes_matches or estimated_percent > percent:
                    has_progress_info = True
                    percent = estimated_percent

                    # Update status with estimated progress
                    self.mega_upload_status = f"Uploading to MEGA: Estimated progress {percent:.1f}%"
                    self.mega_status_label.config(text=self.mega_upload_status, foreground="blue")

                    # Add detailed information
                    self.mega_upload_details["Progress"] = f"{percent:.1f}% (estimated)"

                    # Get elapsed time if available
                    time_match = re.search(r"\[PROGRESS\] Upload sedang berjalan... \((\d+) detik\)", log_content)
                    if time_match and time_match.group(1):
                        elapsed_seconds = int(time_match.group(1))
                        elapsed_minutes = elapsed_seconds // 60
                        elapsed_seconds_remainder = elapsed_seconds % 60
                        self.mega_upload_details["Elapsed Time"] = f"{elapsed_minutes} min {elapsed_seconds_remainder} sec"

                    # Update progress bar
                    self.mega_upload_progress = percent
                    self.mega_progress_bar["value"] = percent
                    self.mega_progress_label.config(text=f"{percent:.1f}% (estimated)")

                    # Update details text
                    self.update_mega_details()

                    # Log the update for debugging
                    logger.debug(f"Updated MEGA progress from estimated progress: {percent:.1f}%")

            # Reset progress display if we have no progress info but there's an active upload
            if not has_progress_info and "[MEGA] Calling upload_file_to_mega" in log_content:
                # Check if there's a recent upload request
                upload_times = re.findall(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d{3}.+\[MEGA\] Calling upload_file_to_mega", log_content)
                if upload_times:
                    last_upload_time_str = upload_times[-1]
                    try:
                        last_upload_time = time.mktime(time.strptime(last_upload_time_str, "%Y-%m-%d %H:%M:%S"))
                        current_time = time.time()

                        # If upload started within the last 5 minutes, show it as in progress
                        if current_time - last_upload_time < 300:  # 5 minutes
                            self.mega_upload_status = "Uploading to MEGA: Waiting for progress information..."
                            self.mega_status_label.config(text=self.mega_upload_status, foreground="blue")

                            # Reset progress to 0 if we don't have any info yet
                            self.mega_upload_progress = 0
                            self.mega_progress_bar["value"] = 0
                            self.mega_progress_label.config(text="0% (waiting for progress)")

                            # Update details
                            self.mega_upload_details["Status"] = "Upload in progress"
                            self.update_mega_details()
                    except Exception as e:
                        logger.error(f"Error parsing upload time: {e}")

            # Also check for detailed upload information in the hidden client logs
            # This captures the [MEGA] Uploaded: X.XX MB of Y.YY MB format
            detailed_upload_matches = re.findall(r"\[MEGA\] Uploaded: ([\d\.]+) MB of ([\d\.]+) MB", log_content)
            if detailed_upload_matches:
                last_match = detailed_upload_matches[-1]
                mb_uploaded = float(last_match[0])
                mb_total = float(last_match[1])

                # Convert MB to bytes for consistency
                bytes_uploaded = int(mb_uploaded * 1024 * 1024)
                total_bytes = int(mb_total * 1024 * 1024)

                # Calculate percentage
                if total_bytes > 0:
                    percent = (bytes_uploaded / total_bytes) * 100

                    # Update status
                    self.mega_upload_status = f"Uploading to MEGA: {mb_uploaded} MB of {mb_total} MB"
                    self.mega_status_label.config(text=self.mega_upload_status, foreground="blue")

                    # Add detailed information
                    self.mega_upload_details["Bytes Uploaded"] = f"{bytes_uploaded:,} bytes ({mb_uploaded} MB)"
                    self.mega_upload_details["Total Size"] = f"{total_bytes:,} bytes ({mb_total} MB)"
                    self.mega_upload_details["Progress"] = f"{percent:.1f}%"

                    # Look for status and elapsed time information
                    status_match = re.search(r"\[MEGA\] Status: (\w+), Elapsed time: (\d+) seconds", log_content)
                    if status_match:
                        status = status_match.group(1)
                        elapsed_seconds = int(status_match.group(2))

                        self.mega_upload_details["Status"] = status
                        self.mega_upload_details["Elapsed Time"] = f"{elapsed_seconds} seconds"

                        # Calculate transfer rate
                        if elapsed_seconds > 0:
                            bytes_per_second = bytes_uploaded / elapsed_seconds
                            self.mega_upload_details["Transfer Rate"] = f"{format_bytes(bytes_per_second)}/s"

                            # Estimate remaining time
                            remaining_bytes = total_bytes - bytes_uploaded
                            if bytes_per_second > 0:
                                remaining_seconds = remaining_bytes / bytes_per_second
                                remaining_minutes = int(remaining_seconds / 60)
                                remaining_seconds = int(remaining_seconds % 60)
                                self.mega_upload_details["Estimated Time Remaining"] = f"{remaining_minutes} min {remaining_seconds} sec"

                    # Update progress bar
                    self.mega_upload_progress = percent
                    self.mega_progress_bar["value"] = percent
                    self.mega_progress_label.config(text=f"{percent:.1f}% ({mb_uploaded} MB of {mb_total} MB)")

                    # Update details text
                    self.update_mega_details()

                    # Log the update for debugging
                    logger.debug(f"Updated MEGA progress from detailed logs: {mb_uploaded} MB of {mb_total} MB ({percent:.1f}%)")

            # Check for completion - only if we don't have active upload progress
            if "[MEGA] ===== MEGA UPLOAD COMPLETED =====" in log_content:
                # Check if there's an active upload by looking for recent progress messages
                recent_upload = False

                # Look for recent progress messages (within the last 120 seconds)
                current_time = time.time()

                # Check for progress from mega.mega logs
                mega_progress_times = re.findall(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d{3} - mega\.mega - INFO - \d+ of \d+ uploaded", log_content)

                # Check for progress from mega_client_fixed logs
                client_progress_times = re.findall(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d{3} - mega_client_fixed - INFO - \[PROGRESS\]", log_content)

                # Combine all progress times
                all_progress_times = mega_progress_times + client_progress_times

                if all_progress_times:
                    # Sort by time to get the most recent
                    all_progress_times.sort(reverse=True)
                    last_progress_time_str = all_progress_times[0]

                    try:
                        last_progress_time = time.mktime(time.strptime(last_progress_time_str, "%Y-%m-%d %H:%M:%S"))

                        # Check if we have a recent progress update (within the last 2 minutes)
                        if current_time - last_progress_time < 120:  # Within the last 120 seconds
                            recent_upload = True
                            logger.debug(f"Found recent progress update at {last_progress_time_str}, not marking as completed")

                            # Also check if the completion message is older than the progress update
                            completion_times = re.findall(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d{3}.+\[MEGA\] ===== MEGA UPLOAD COMPLETED =====", log_content)
                            if completion_times:
                                last_completion_time_str = completion_times[-1]
                                try:
                                    last_completion_time = time.mktime(time.strptime(last_completion_time_str, "%Y-%m-%d %H:%M:%S"))
                                    if last_completion_time < last_progress_time:
                                        logger.debug(f"Completion message at {last_completion_time_str} is older than progress update at {last_progress_time_str}")
                                        recent_upload = True
                                except Exception as e:
                                    logger.error(f"Error parsing completion time: {e}")
                    except Exception as e:
                        logger.error(f"Error parsing progress time: {e}")

                # Only mark as completed if there's no active upload
                if not recent_upload and not has_progress_info:
                    self.mega_upload_status = "MEGA upload completed successfully"
                    self.mega_status_label.config(text=self.mega_upload_status, foreground="green")

                    # Find completion details
                    result_match = re.search(r"\[MEGA\] Upload result: (.+)", log_content)
                    if result_match:
                        self.mega_upload_details["Result"] = result_match.group(1)

                    # Update progress to 100%
                    self.mega_upload_progress = 100
                    self.mega_progress_bar["value"] = 100
                    self.mega_progress_label.config(text="100% (Completed)")

                    # Update details text
                    self.update_mega_details()

            # Check for failure
            if "Error uploading" in log_content and "[MEGA]" in log_content:
                self.mega_upload_status = "MEGA upload failed"
                self.mega_status_label.config(text=self.mega_upload_status, foreground="red")

                # Find error details
                error_match = re.search(r"\[MEGA\] Error .+: (.+)", log_content)
                if error_match:
                    self.mega_upload_details["Error"] = error_match.group(1)

                # Update progress (keep previous progress)
                self.mega_progress_label.config(text=f"{self.mega_upload_progress:.1f}% (Failed)")

                # Update details text
                self.update_mega_details()

        except Exception as e:
            logger.error(f"Error checking for MEGA upload activities: {e}")

    def apply_filter(self):
        """Apply filter to log display"""
        filter_value = self.filter_var.get()

        if filter_value == "All":
            # Refresh without filtering
            self.log_position = 0
            self.refresh_log()
            return

        try:
            # Store current content
            current_content = self.log_text.get(1.0, tk.END)
            lines = current_content.split('\n')
            filtered_lines = []

            for line in lines:
                if filter_value == "Backup" and ("[BACKUP]" in line or "DATABASE BACKUP" in line):
                    filtered_lines.append(line)
                elif filter_value == "MEGA" and ("[MEGA]" in line or "mega.mega" in line or "[PROGRESS]" in line or "MEGA" in line.upper() or "uploaded" in line.lower()):
                    filtered_lines.append(line)
                elif filter_value == "GDRIVE" and ("[GDRIVE]" in line or "gdrive" in line.lower() or "google drive" in line.lower() or "drive.google" in line.lower()):
                    filtered_lines.append(line)
                elif filter_value == "Error" and ("ERROR" in line or "Error" in line or "error" in line or "Failed" in line):
                    filtered_lines.append(line)
                elif filter_value == "Connection" and ("Connection" in line or "connected" in line or "disconnected" in line or "socket" in line.lower()):
                    filtered_lines.append(line)

            # Update display
            self.log_text.delete(1.0, tk.END)
            if filtered_lines:
                self.log_text.insert(tk.END, '\n'.join(filtered_lines))
            else:
                self.log_text.insert(tk.END, f"No {filter_value.lower()} entries found in log")
        except Exception as e:
            logger.error(f"Error applying filter: {e}")
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, f"Error applying filter: {str(e)}")

    def schedule_refresh(self):
        """Schedule automatic refresh"""
        if self.auto_refresh.get():
            self.check_client_status()
            self.refresh_log()
            self.root.after(REFRESH_INTERVAL, self.schedule_refresh)

    def toggle_auto_refresh(self):
        """Toggle auto-refresh"""
        if self.auto_refresh.get():
            self.schedule_refresh()

    def manual_refresh(self):
        """Manually refresh status and log"""
        self.check_client_status()
        self.refresh_log()

    def clear_log_display(self):
        """Clear the log display, but not the file"""
        self.log_text.delete(1.0, tk.END)

    def start_client(self):
        """Start hidden client application"""
        if self.client_running:
            messagebox.showinfo("Info", "Client is already running")
            return

        try:
            # Try to find the client application in multiple locations
            possible_paths = [
                # Same directory as debug app
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.py"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.exe"),
                # Current directory
                "ifess_client_hidden.py",
                "ifess_client_hidden.exe",
                # Parent directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.py"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.exe"),
                # dist directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_client_hidden.exe"),
            ]

            # Find the first path that exists
            client_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    client_path = path
                    logger.info(f"Found client application at: {client_path}")
                    break

            if client_path is None:
                messagebox.showerror("Error", "Client application not found. Please make sure ifess_client_hidden.exe is in the same directory.")
                logger.error("Client application not found in any of the possible locations.")
                return

            # Check file extension to determine how to run it
            if client_path.endswith('.py'):
                # Run as Python script
                python_path = sys.executable
                cmd = [python_path, client_path]
                subprocess.Popen(cmd, creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                # Run as executable
                subprocess.Popen([client_path], creationflags=subprocess.CREATE_NO_WINDOW)

            # Wait a moment for client to start
            time.sleep(1)

            # Check status
            self.check_client_status()

            if self.client_running:
                messagebox.showinfo("Success", "Client started successfully")
            else:
                messagebox.showwarning("Warning", "Client may not have started properly. Check logs for details.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start client: {str(e)}")
            logger.error(f"Error starting client: {e}")
            logger.debug(traceback.format_exc())

    def stop_client(self):
        """Stop hidden client application"""
        if not self.client_running:
            messagebox.showinfo("Info", "Client is not running")
            return

        try:
            # Find the process and terminate it
            if sys.platform == 'win32':
                # On Windows, use taskkill to find and kill by image name
                logger.info("Attempting to stop client using taskkill /im ifess_client_hidden.exe")
                subprocess.run(['taskkill', '/f', '/im', 'ifess_client_hidden.exe'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Also try to kill the Python process if running as script
                logger.info("Attempting to stop client using taskkill for Python process")
                subprocess.run(['taskkill', '/f', '/fi', 'WINDOWTITLE eq ifess_client_hidden.py'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Also try by Python command line
                logger.info("Attempting to stop client using taskkill for Python with command line")
                subprocess.run(['taskkill', '/f', '/fi', 'IMAGENAME eq python.exe', '/fi', 'COMMANDLINE eq *ifess_client_hidden.py*'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Also try by pythonw.exe (for hidden Python processes)
                logger.info("Attempting to stop client using taskkill for pythonw.exe")
                subprocess.run(['taskkill', '/f', '/fi', 'IMAGENAME eq pythonw.exe', '/fi', 'COMMANDLINE eq *ifess_client_hidden.py*'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # Wait a moment for client to stop
            time.sleep(1)

            # Check status
            self.check_client_status()

            if not self.client_running:
                messagebox.showinfo("Success", "Client stopped successfully")
            else:
                messagebox.showwarning("Warning", "Failed to stop client. Try closing it manually.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop client: {str(e)}")
            logger.error(f"Error stopping client: {e}")
            logger.debug(traceback.format_exc())

    def open_config_tool(self):
        """Open the configuration tool"""
        try:
            # Try to find the config tool in multiple locations
            possible_paths = [
                # Same directory as debug app
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_config_gui.py"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_config_gui.exe"),
                # Current directory
                "ifess_config_gui.py",
                "ifess_config_gui.exe",
                # Parent directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_config_gui.py"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_config_gui.exe"),
                # dist directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_config_gui.exe"),
            ]

            # Find the first path that exists
            config_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    logger.info(f"Found config tool at: {config_path}")
                    break

            if config_path is None:
                # If config tool not found, open config file directly
                if os.path.exists(CONFIG_FILE):
                    # Open with default application
                    logger.info(f"Opening config file directly: {CONFIG_FILE}")
                    os.startfile(CONFIG_FILE)
                else:
                    messagebox.showerror("Error", "Configuration tool and file not found")
                    logger.error("Configuration tool and file not found")
                return

            # Check file extension to determine how to run it
            if config_path.endswith('.py'):
                # Run as Python script
                python_path = sys.executable
                subprocess.Popen([python_path, config_path])
            else:
                # Run as executable
                subprocess.Popen([config_path])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open configuration tool: {str(e)}")
            logger.error(f"Error opening config tool: {e}")
            logger.debug(traceback.format_exc())

def main():
    """Main entry point"""
    try:
        # Create main window
        root = tk.Tk()
        # Create the app instance and keep a reference to it
        # to prevent it from being garbage collected
        DebugApp(root)

        # Start Tkinter event loop
        root.mainloop()
    except Exception as e:
        logger.critical(f"Error in main: {e}")
        logger.debug(traceback.format_exc())
        messagebox.showerror("Critical Error", f"Application error: {str(e)}")

if __name__ == "__main__":
    main()
