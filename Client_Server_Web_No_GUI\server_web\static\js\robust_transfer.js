/**
 * Robust Transfer Client
 * 
 * This module provides client-side functionality for the robust transfer system.
 * It handles chunked file transfers with retry logic and progress tracking.
 */

// Global variables
let isTransferInProgress = false;
let currentTransferId = null;

/**
 * Format bytes to human-readable format
 * @param {number} bytes - The number of bytes
 * @param {number} decimals - Number of decimal places
 * @returns {string} - Formatted string
 */
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Update progress bar
 * @param {number} progress - Progress percentage (0-100)
 */
function updateProgressBar(progress) {
    const progressBar = document.querySelector('#modalProgressBar .progress-bar');
    if (progressBar) {
        progressBar.style.width = progress + '%';
        progressBar.textContent = progress + '%';
        progressBar.setAttribute('aria-valuenow', progress);
    }
}

/**
 * Initiate a robust transfer
 * @param {string} clientId - Client ID
 * @param {object} fileInfo - File information
 * @returns {Promise} - Promise that resolves when transfer is initiated
 */
function initiateRobustTransfer(clientId, fileInfo) {
    return new Promise((resolve, reject) => {
        // Set default chunk size if not provided
        if (!fileInfo.chunk_size) {
            fileInfo.chunk_size = 131072; // 128KB chunks for better reliability
        }
        
        // Log initiation
        console.log(`Initiating robust transfer for client ${clientId}`);
        
        // Send initiation request
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'http://localhost:5001/api/robust-transfer/initiate/' + encodeURIComponent(clientId), true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.timeout = 30000; // 30 second timeout
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        console.log('Robust transfer initiation response:', response);
                        
                        if (response.success) {
                            // Store current transfer ID
                            currentTransferId = clientId;
                            resolve(response.transfer_info);
                        } else {
                            reject(new Error(response.message || 'Unknown error'));
                        }
                    } catch (e) {
                        reject(new Error(`Error parsing response: ${e.message}`));
                    }
                } else {
                    reject(new Error(`HTTP error: ${xhr.status}`));
                }
            }
        };
        
        xhr.ontimeout = function() {
            reject(new Error('Request timed out'));
        };
        
        xhr.onerror = function() {
            reject(new Error('Network error'));
        };
        
        xhr.send(JSON.stringify(fileInfo));
    });
}

/**
 * Send a chunk of data
 * @param {string} clientId - Client ID
 * @param {object} transferInfo - Transfer information
 * @param {number} chunkNumber - Chunk number
 * @param {number} offset - Offset in the file
 * @param {ArrayBuffer} fileData - File data
 * @param {number} retryCount - Retry count
 * @returns {Promise} - Promise that resolves when chunk is sent
 */
function sendChunk(clientId, transferInfo, chunkNumber, offset, fileData, retryCount = 0) {
    return new Promise((resolve, reject) => {
        const maxRetries = 5;
        const chunkSize = transferInfo.chunk_size;
        const fileSize = transferInfo.file_size;
        
        // Calculate end of this chunk
        const end = Math.min(offset + chunkSize, fileSize);
        const chunkData = fileData.slice(offset, end);
        const isLastChunk = end >= fileSize;
        
        try {
            // Convert chunk to base64
            const base64Chunk = btoa(String.fromCharCode.apply(null, new Uint8Array(chunkData)));
            
            // Prepare chunk info
            const chunkInfo = {
                chunk_number: chunkNumber,
                offset: offset,
                data: base64Chunk,
                is_last: isLastChunk,
                file_size: fileSize
            };
            
            // Send chunk
            const xhr = new XMLHttpRequest();
            xhr.open('POST', 'http://localhost:5001/api/robust-transfer/chunk/' + encodeURIComponent(clientId), true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.timeout = 30000; // 30 second timeout
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            
                            if (response.success) {
                                // Update progress
                                const progress = response.progress || (offset + chunkData.length) / fileSize * 100;
                                updateProgressBar(progress);
                                
                                // Log progress
                                if (chunkNumber % 10 === 0 || isLastChunk) {
                                    console.log(`Chunk ${chunkNumber} sent successfully (${formatBytes(offset + chunkData.length)} / ${formatBytes(fileSize)})`);
                                }
                                
                                resolve({
                                    isLastChunk,
                                    nextOffset: end,
                                    progress,
                                    response
                                });
                            } else {
                                if (retryCount < maxRetries) {
                                    console.warn(`Chunk ${chunkNumber} failed, retrying (${retryCount + 1}/${maxRetries})...`);
                                    setTimeout(() => {
                                        sendChunk(clientId, transferInfo, chunkNumber, offset, fileData, retryCount + 1)
                                            .then(resolve)
                                            .catch(reject);
                                    }, 1000 * Math.pow(2, retryCount)); // Exponential backoff
                                } else {
                                    reject(new Error(`Failed after ${maxRetries} retries: ${response.message}`));
                                }
                            }
                        } catch (e) {
                            if (retryCount < maxRetries) {
                                console.warn(`Error parsing response for chunk ${chunkNumber}, retrying (${retryCount + 1}/${maxRetries})...`);
                                setTimeout(() => {
                                    sendChunk(clientId, transferInfo, chunkNumber, offset, fileData, retryCount + 1)
                                        .then(resolve)
                                        .catch(reject);
                                }, 1000 * Math.pow(2, retryCount));
                            } else {
                                reject(new Error(`Error parsing response: ${e.message}`));
                            }
                        }
                    } else {
                        if (retryCount < maxRetries) {
                            console.warn(`HTTP error ${xhr.status} for chunk ${chunkNumber}, retrying (${retryCount + 1}/${maxRetries})...`);
                            setTimeout(() => {
                                sendChunk(clientId, transferInfo, chunkNumber, offset, fileData, retryCount + 1)
                                    .then(resolve)
                                    .catch(reject);
                            }, 1000 * Math.pow(2, retryCount));
                        } else {
                            reject(new Error(`HTTP error: ${xhr.status}`));
                        }
                    }
                }
            };
            
            xhr.ontimeout = function() {
                if (retryCount < maxRetries) {
                    console.warn(`Timeout for chunk ${chunkNumber}, retrying (${retryCount + 1}/${maxRetries})...`);
                    setTimeout(() => {
                        sendChunk(clientId, transferInfo, chunkNumber, offset, fileData, retryCount + 1)
                            .then(resolve)
                            .catch(reject);
                    }, 1000 * Math.pow(2, retryCount));
                } else {
                    reject(new Error('Request timed out'));
                }
            };
            
            xhr.onerror = function() {
                if (retryCount < maxRetries) {
                    console.warn(`Network error for chunk ${chunkNumber}, retrying (${retryCount + 1}/${maxRetries})...`);
                    setTimeout(() => {
                        sendChunk(clientId, transferInfo, chunkNumber, offset, fileData, retryCount + 1)
                            .then(resolve)
                            .catch(reject);
                    }, 1000 * Math.pow(2, retryCount));
                } else {
                    reject(new Error('Network error'));
                }
            };
            
            xhr.send(JSON.stringify(chunkInfo));
        } catch (e) {
            // Handle errors in chunk preparation (e.g., too large for btoa)
            console.error(`Error preparing chunk ${chunkNumber}: ${e.message}`);
            
            if (e.message && e.message.includes('Maximum call stack size exceeded')) {
                // This is likely due to the chunk being too large for btoa
                // Split the chunk in half and try again
                const midPoint = Math.floor((offset + end) / 2);
                console.log(`Chunk too large, splitting at ${formatBytes(midPoint - offset)}`);
                
                // Reduce chunk size for future chunks
                transferInfo.chunk_size = Math.floor(transferInfo.chunk_size / 2);
                
                // Try again with smaller chunk size
                sendChunk(clientId, transferInfo, chunkNumber, offset, fileData, 0)
                    .then(resolve)
                    .catch(reject);
            } else if (retryCount < maxRetries) {
                // For other errors, retry with smaller chunk size
                transferInfo.chunk_size = Math.floor(transferInfo.chunk_size / 2);
                console.log(`Reducing chunk size to ${formatBytes(transferInfo.chunk_size)} and retrying`);
                
                setTimeout(() => {
                    sendChunk(clientId, transferInfo, chunkNumber, offset, fileData, retryCount + 1)
                        .then(resolve)
                        .catch(reject);
                }, 1000 * Math.pow(2, retryCount));
            } else {
                reject(new Error(`Error preparing chunk: ${e.message}`));
            }
        }
    });
}

/**
 * Start a robust transfer
 * @param {string} clientId - Client ID
 * @param {ArrayBuffer} fileData - File data
 * @param {object} options - Transfer options
 * @returns {Promise} - Promise that resolves when transfer is complete
 */
async function startRobustTransfer(clientId, fileData, options = {}) {
    if (isTransferInProgress) {
        throw new Error('Transfer already in progress');
    }
    
    isTransferInProgress = true;
    
    try {
        // Prepare file info
        const fileInfo = {
            client_id: clientId,
            client_name: options.clientName || clientId,
            file_name: options.fileName || 'database.fdb',
            file_size: fileData.byteLength,
            chunk_size: options.chunkSize || 131072 // 128KB chunks
        };
        
        // Initiate transfer
        const transferInfo = await initiateRobustTransfer(clientId, fileInfo);
        console.log('Transfer initiated:', transferInfo);
        
        // Start sending chunks
        let chunkNumber = 0;
        let offset = 0;
        
        while (offset < fileData.byteLength) {
            const result = await sendChunk(clientId, transferInfo, chunkNumber, offset, fileData);
            
            // Update offset and chunk number
            offset = result.nextOffset;
            chunkNumber++;
            
            // If this was the last chunk, we're done
            if (result.isLastChunk) {
                break;
            }
        }
        
        console.log('Transfer complete!');
        isTransferInProgress = false;
        return { success: true, message: 'Transfer complete' };
    } catch (error) {
        console.error('Transfer failed:', error);
        isTransferInProgress = false;
        throw error;
    }
}

/**
 * Get transfer status
 * @param {string} clientId - Client ID
 * @returns {Promise} - Promise that resolves with transfer status
 */
function getTransferStatus(clientId) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('GET', 'http://localhost:5001/api/robust-transfer/status/' + encodeURIComponent(clientId), true);
        xhr.timeout = 10000; // 10 second timeout
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error(`Error parsing response: ${e.message}`));
                    }
                } else {
                    reject(new Error(`HTTP error: ${xhr.status}`));
                }
            }
        };
        
        xhr.ontimeout = function() {
            reject(new Error('Request timed out'));
        };
        
        xhr.onerror = function() {
            reject(new Error('Network error'));
        };
        
        xhr.send();
    });
}

/**
 * Resume a transfer
 * @param {string} clientId - Client ID
 * @returns {Promise} - Promise that resolves when transfer is resumed
 */
function resumeTransfer(clientId) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'http://localhost:5001/api/robust-transfer/resume/' + encodeURIComponent(clientId), true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.timeout = 10000; // 10 second timeout
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error(`Error parsing response: ${e.message}`));
                    }
                } else {
                    reject(new Error(`HTTP error: ${xhr.status}`));
                }
            }
        };
        
        xhr.ontimeout = function() {
            reject(new Error('Request timed out'));
        };
        
        xhr.onerror = function() {
            reject(new Error('Network error'));
        };
        
        xhr.send(JSON.stringify({ action: 'resume' }));
    });
}

/**
 * Cancel a transfer
 * @param {string} clientId - Client ID
 * @returns {Promise} - Promise that resolves when transfer is cancelled
 */
function cancelTransfer(clientId) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'http://localhost:5001/api/robust-transfer/cancel/' + encodeURIComponent(clientId), true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.timeout = 10000; // 10 second timeout
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        
                        if (response.success) {
                            isTransferInProgress = false;
                            currentTransferId = null;
                        }
                        
                        resolve(response);
                    } catch (e) {
                        reject(new Error(`Error parsing response: ${e.message}`));
                    }
                } else {
                    reject(new Error(`HTTP error: ${xhr.status}`));
                }
            }
        };
        
        xhr.ontimeout = function() {
            reject(new Error('Request timed out'));
        };
        
        xhr.onerror = function() {
            reject(new Error('Network error'));
        };
        
        xhr.send(JSON.stringify({ action: 'cancel' }));
    });
}

// Export functions
window.RobustTransfer = {
    initiateTransfer: initiateRobustTransfer,
    sendChunk: sendChunk,
    startTransfer: startRobustTransfer,
    getStatus: getTransferStatus,
    resumeTransfer: resumeTransfer,
    cancelTransfer: cancelTransfer,
    formatBytes: formatBytes,
    updateProgressBar: updateProgressBar
};
