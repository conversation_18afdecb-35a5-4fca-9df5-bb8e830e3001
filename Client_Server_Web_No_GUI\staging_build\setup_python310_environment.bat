@echo off
title IFESS Python 3.10 Environment Setup

echo ===============================================
echo   IFESS Python 3.10 Environment Setup
echo ===============================================
echo.
echo This script will help you set up a Python 3.10 environment
echo for MEGA upload compatibility.
echo.
echo MEGA library requires Python 3.10 or 3.11 due to compatibility
echo issues with Python 3.13+ (asyncio.coroutine removal).
echo.

REM Check if Python 3.10 is available
echo [1/5] Checking for Python 3.10...
python3.10 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ Python 3.10 found
    set PYTHON_CMD=python3.10
    goto :create_venv
)

python310 --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✓ Python 3.10 found as python310
    set PYTHON_CMD=python310
    goto :create_venv
)

REM Check if current python is 3.10
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo Current Python version: %PYTHON_VERSION%

if "%PYTHON_VERSION:~0,4%" == "3.10" (
    echo ✓ Current Python is 3.10
    set PYTHON_CMD=python
    goto :create_venv
)

echo ✗ Python 3.10 not found
echo.
echo Please install Python 3.10 from:
echo https://www.python.org/downloads/release/python-31011/
echo.
echo After installation, you can:
echo 1. Use 'py -3.10' command (if Python Launcher is installed)
echo 2. Add Python 3.10 to PATH as 'python310'
echo 3. Or run this script from Python 3.10 environment
echo.
pause
exit /b 1

:create_venv
echo.
echo [2/5] Creating Python 3.10 virtual environment...
if exist "venv_py310" (
    echo Virtual environment already exists, removing old one...
    rmdir /s /q venv_py310
)

%PYTHON_CMD% -m venv venv_py310
if %errorlevel% neq 0 (
    echo ✗ Failed to create virtual environment
    pause
    exit /b 1
)
echo ✓ Virtual environment created

echo.
echo [3/5] Activating virtual environment...
call venv_py310\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ✗ Failed to activate virtual environment
    pause
    exit /b 1
)
echo ✓ Virtual environment activated

echo.
echo [4/5] Installing required packages...
echo Installing basic packages...
python -m pip install --upgrade pip
python -m pip install wheel setuptools

echo Installing IFESS dependencies...
python -m pip install schedule
python -m pip install google-auth google-auth-oauthlib google-api-python-client
python -m pip install requests python-dateutil

echo Installing MEGA library...
python -m pip install mega.py
if %errorlevel% neq 0 (
    echo ✗ Failed to install mega.py
    echo Trying alternative installation...
    python -m pip install --no-deps mega.py
)

echo.
echo [5/5] Testing MEGA library compatibility...
python -c "from mega import Mega; print('✓ MEGA library imported successfully')"
if %errorlevel% neq 0 (
    echo ✗ MEGA library test failed
    echo This might be due to Python version compatibility issues.
) else (
    echo ✓ MEGA library is working correctly
)

echo.
echo ===============================================
echo   Setup Complete!
echo ===============================================
echo.
echo Python 3.10 virtual environment has been created in: venv_py310
echo.
echo To use this environment:
echo 1. Run: venv_py310\Scripts\activate.bat
echo 2. Then run your IFESS applications
echo.
echo To build IFESS with Python 3.10:
echo 1. Activate the environment: venv_py310\Scripts\activate.bat
echo 2. Run: build_all_clients_comprehensive.bat
echo.
echo To deactivate the environment:
echo Run: deactivate
echo.
pause 