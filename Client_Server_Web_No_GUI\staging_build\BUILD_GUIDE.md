# IFESS Client Suite - Build System Guide

## Overview

This build system creates standalone executable files (.exe) for three IFESS client applications using PyInstaller. The executables are completely self-contained and require no Python installation or additional dependencies on the target system.

## Target Applications

1. **ifess_client_hidden.exe** - Main background service client
   - Runs silently in background
   - Handles database monitoring and cloud uploads
   - No console window (--noconsole)

2. **ifess_client_debug.exe** - Debug interface client
   - GUI application for monitoring and debugging
   - Real-time connection statistics
   - Console window for debug output

3. **ifess_config_gui.exe** - Configuration GUI client
   - Configuration management interface
   - Database and server settings
   - Google Drive authentication setup

## Build Scripts

### 1. Setup Script: `setup_build_environment.bat`
**Purpose**: Prepares the build environment
- Verifies Python installation
- Installs PyInstaller and dependencies
- Creates build directories
- Checks for optional UPX compression

**Usage**: Run once before building
```batch
setup_build_environment.bat
```

### 2. Comprehensive Build: `build_all_clients_comprehensive.bat`
**Purpose**: Full production build with extensive logging
- Complete pre-build validation
- Dependency verification
- Detailed build logging
- Post-build packaging
- Error tracking and reporting

**Features**:
- 7-stage build process
- Comprehensive error handling
- Build log generation
- File size reporting
- Distribution packaging

**Usage**:
```batch
build_all_clients_comprehensive.bat
```

**Output**: 
- Executables in `dist/` folder
- Build log file: `build_log_YYYY-MM-DD_HH-MM-SS.txt`
- Distribution README and launcher scripts

### 3. Quick Build: `quick_build.bat`
**Purpose**: Fast development builds
- Minimal logging for speed
- Basic error checking
- Development and testing use

**Usage**:
```batch
quick_build.bat
```

## PyInstaller Specifications

### Hidden Client Spec: `ifess_client_hidden_comprehensive.spec`
- **Target**: Background service
- **Console**: No (--noconsole)
- **Compression**: UPX enabled
- **Bundled Resources**:
  - Configuration files (client_config.json, tokens)
  - Google Drive client modules
  - Common utilities
  - Service account credentials

### Debug Client Spec: `ifess_client_debug_comprehensive.spec`
- **Target**: Debug interface
- **Console**: Yes (for debugging)
- **Compression**: UPX enabled
- **Bundled Resources**:
  - GUI dependencies (tkinter, ttk)
  - Token downloader utility
  - Configuration files
  - Cloud client modules

### Config GUI Spec: `ifess_config_gui_comprehensive.spec`
- **Target**: Configuration interface
- **Console**: No (GUI application)
- **Compression**: UPX enabled
- **Bundled Resources**:
  - GUI libraries
  - Configuration templates
  - Database connection libraries
  - Cloud authentication modules

## Build Requirements

### System Requirements
- Windows 10/11
- Python 3.8 or later
- PowerShell (for batch scripts)

### Python Dependencies
- **Core**: `pyinstaller`, `requests`
- **Google APIs**: `google-api-python-client`, `google-auth`, `google-auth-oauthlib`
- **Database**: `fdb`, `firebirdsql`
- **Scheduling**: `schedule`
- **GUI**: `tkinter` (usually included with Python)

### Optional Tools
- **UPX**: For executable compression (reduces file size by ~50%)
  - Download from: https://upx.github.io/
  - Add to system PATH

## Build Process Details

### Stage 1: Pre-build Checks
- Python installation verification
- PyInstaller availability
- Source file validation
- UPX compression check

### Stage 2: Environment Cleaning
- Remove old build artifacts
- Create fresh dist/ and build/ directories
- Clear previous build cache

### Stage 3: Dependency Installation
- Update core Python packages
- Install/update Google API libraries
- Verify database connectivity libraries
- Check GUI framework availability

### Stage 4-6: Application Building
Each application is built using its respective .spec file:
- Source code analysis
- Dependency resolution
- Resource bundling
- Executable generation
- File size reporting

### Stage 7: Post-build Packaging
- Copy configuration templates and credential files
- Bundle Google Drive authentication tokens (token.json)
- Copy service account credentials and OAuth secrets
- Set OAuth configuration as primary for Google Drive access
- Create launcher batch files
- Generate distribution README with credential information
- Package for deployment with portable credential setup

## Output Structure

After successful build, the `dist/` folder contains:

```
dist/
├── ifess_client_hidden.exe      # Main background service
├── ifess_config_gui.exe         # Configuration GUI
├── debug_hidden_client.bat      # Debug tool for troubleshooting
├── run_hidden_client.bat        # Hidden client launcher
├── run_config_gui.bat           # Config GUI launcher
├── client_config.json           # Primary configuration (OAuth-enabled)
├── client_config_oauth_tokens.json  # Full OAuth configuration
├── token.json                   # Google Drive authentication token
├── token_backup.json            # Backup authentication token
├── ptrj-backup-services-account.json  # Service account credentials
├── client_secrets.json          # OAuth client secrets
├── setup_portable_credentials.bat  # Credential setup utility
├── README.txt                   # Distribution guide with credential info
└── [Additional batch files]     # Development utilities
```

## Deployment

### Single Machine Deployment
1. Copy entire `dist/` folder to target machine
2. No Python installation required
3. Run executables directly or use launcher batch files

### Network Deployment
1. Package `dist/` folder as ZIP archive
2. Distribute to client machines
3. Extract and run immediately

### Portable Installation
- All executables are completely portable
- Google Drive credentials bundled and pre-configured
- No additional authentication setup required
- No registry modifications
- No installation process required
- Can run from USB drives or network shares
- Works immediately with existing Google Drive access

## Troubleshooting

### Build Failures

**Python not found**
- Install Python 3.8+ from python.org
- Ensure "Add Python to PATH" is checked during installation

**PyInstaller errors**
- Update PyInstaller: `pip install --upgrade pyinstaller`
- Clear build cache: Delete `build/` and `dist/` folders

**Missing dependencies**
- Run `setup_build_environment.bat` to install all requirements
- Manually install missing packages with `pip install <package>`

**UPX compression issues**
- Download UPX from https://upx.github.io/
- Add UPX to system PATH
- Or disable UPX in .spec files (change `upx=False`)

### Runtime Issues

**"Failed to execute script" error**
- Antivirus software may be blocking execution
- Add executables to antivirus whitelist
- Try running with administrator privileges

**Missing configuration files**
- Ensure configuration files are in same directory as executable
- Check that bundled resources were included during build

**Database connection errors**
- Verify Firebird client libraries are accessible
- Check database server connectivity
- Validate configuration file settings

## Advanced Configuration

### Custom .spec Files
- Modify existing .spec files for custom builds
- Add additional resources with `datas` parameter
- Include extra modules with `hiddenimports`

### Build Optimization
- Enable/disable UPX compression
- Adjust `excludes` to reduce file size
- Optimize `hiddenimports` for faster startup

### Icon Customization
- Place `MAINICON.ico` in staging_build directory
- .spec files will automatically detect and use the icon

## Build Performance

### Typical Build Times
- **Quick Build**: 3-5 minutes
- **Comprehensive Build**: 8-12 minutes

### File Sizes (typical)
- **Without UPX**: 80-120 MB per executable
- **With UPX**: 40-60 MB per executable

### Build Optimization Tips
1. Use Quick Build for development
2. Use Comprehensive Build for production
3. Clean build environment regularly
4. Keep dependencies up to date

## Support and Maintenance

### Updating Dependencies
1. Run `setup_build_environment.bat` periodically
2. Update PyInstaller: `pip install --upgrade pyinstaller`
3. Update Google APIs: `pip install --upgrade google-api-python-client`

### Build Log Analysis
- Check build logs for warnings and errors
- Monitor file sizes for unexpected growth
- Verify all required resources are bundled

### Version Management
- Tag successful builds with version numbers
- Keep build logs for troubleshooting
- Archive working .spec files for rollback

## New Features and Improvements

### 1. Database Connection Test Fix
- **Issue**: "Test Connection" button in configuration GUI was failing with unpacking errors
- **Solution**: Updated to handle both tuple and boolean return types from FirebirdConnector
- **Benefit**: Reliable database connection testing with clear error messages

### 2. Scheduled Independent Upload Feature
- **Feature**: New "Scheduled Upload" tab in configuration GUI
- **Configuration**: Set daily upload time (hour and minute) and choose service (Google Drive or MEGA)
- **Operation**: Runs independently of server-triggered uploads
- **Requirement**: Hidden client must be running for scheduled uploads to work
- **Logging**: All scheduled uploads are logged with `[SCHEDULED]` prefix for easy identification

### 3. Python 3.10 Environment Setup
- **Script**: `setup_python310_environment.bat`
- **Purpose**: Creates Python 3.10 virtual environment for MEGA compatibility
- **Reason**: MEGA library requires Python 3.10/3.11 due to Python 3.13+ compatibility issues (asyncio.coroutine removal)
- **Features**: 
  - Automatic Python 3.10 detection
  - Virtual environment creation
  - All dependencies installation
  - MEGA library compatibility testing

### 4. ZIP File Cleanup Enhancement
- **Improvement**: Automatic cleanup of temporary ZIP files after successful uploads
- **Scope**: Applies to both Google Drive and MEGA uploads
- **Safety**: Only removes temporary files, preserves original database files
- **Implementation**: Cleanup occurs in both success and error scenarios

### 5. Debug Batch File Replacement
- **Change**: Removed `ifess_client_debug.exe` from compilation
- **Replacement**: Created `debug_hidden_client.bat` for troubleshooting
- **Advantage**: Shows real-time console output and error messages
- **Usage**: Run batch file to launch hidden client with visible terminal

### Configuration File Updates

The configuration file now supports scheduled upload settings:

```json
{
  "scheduled_upload": {
    "enabled": true,
    "hour": 2,
    "minute": 0,
    "service": "gdrive"
  }
}
```

### Python 3.10 Environment Usage

To use Python 3.10 environment for MEGA compatibility:

1. Run `setup_python310_environment.bat`
2. Activate environment: `venv_py310\Scripts\activate.bat`
3. Build applications: `build_all_clients_comprehensive.bat`
4. Deactivate when done: `deactivate`

### Troubleshooting New Features

**Scheduled Upload Not Working**
- Verify hidden client is running
- Check configuration file has `scheduled_upload` section
- Review logs for `[SCHEDULED]` entries
- Ensure selected upload service (Google Drive/MEGA) is properly configured

**MEGA Compatibility Issues**
- Use Python 3.10 environment setup script
- Verify MEGA library installation in Python 3.10
- Check Python version: `python --version` should show 3.10.x

**Database Connection Test Failing**
- Verify database file path exists
- Check ISQL executable path is correct
- Ensure database credentials are valid
- Review connection test logs for specific error messages

---

**Last Updated**: 2025-01-25
**Build System Version**: 2.0
**Compatible with**: Python 3.8+ (Python 3.10+ recommended for MEGA functionality) 