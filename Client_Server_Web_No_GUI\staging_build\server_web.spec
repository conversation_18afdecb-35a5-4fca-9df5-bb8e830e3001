# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from pathlib import Path

block_cipher = None

# Get the base directory
base_dir = os.path.abspath(os.path.join(os.path.dirname(os.path.abspath(SPEC)), '..', 'server_web'))

# Define data files to include
data_files = [
    # Include templates
    (os.path.join(base_dir, 'templates'), 'templates'),
    
    # Include static files
    (os.path.join(base_dir, 'static'), 'static'),
    
    # Include queries
    (os.path.join(base_dir, 'queries'), 'queries'),
    
    # Include SQL queries
    (os.path.join(base_dir, 'static', 'sql_queries'), os.path.join('static', 'sql_queries')),
]

# Ensure directories exist
for src, _ in data_files:
    if not os.path.exists(src):
        os.makedirs(src, exist_ok=True)

a = Analysis(
    [os.path.join(base_dir, 'server_web.py')],
    pathex=[base_dir],
    binaries=[],
    datas=data_files,
    hiddenimports=[
        'flask',
        'flask_cors',
        'socket',
        'threading',
        'logging',
        'json',
        'time',
        'datetime',
        'os',
        're',
        'traceback',
        'uuid',
        'struct',
        'zlib',
        'hashlib',
        'shutil',
        'subprocess',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='server_web',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='server_web',
)
