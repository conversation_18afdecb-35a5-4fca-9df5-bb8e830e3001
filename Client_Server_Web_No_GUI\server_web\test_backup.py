import os
import sys
import time
import datetime
import base64
import json

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.network import NetworkMessage

# Create backup directories
backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'backups')
client_id = 'client_fdb-client-monitoring_127.0.0.1'
client_dir = os.path.join(backup_dir, client_id)

# Create directories if they don't exist
os.makedirs(client_dir, exist_ok=True)
print(f"Created backup directory: {client_dir}")

# Create a test database file
timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
test_db_filename = f"test_db_{timestamp}.fdb"
test_db_path = os.path.join(client_dir, test_db_filename)

# Create a test database file with some content
with open(test_db_path, 'wb') as f:
    # Create a 1MB test file
    f.write(b'TESTDATABASE' * 100000)

print(f"Created test database file: {test_db_path}")
print(f"File size: {os.path.getsize(test_db_path)} bytes")

# Print the download URL
download_url = f"/api/backup/download/{client_id}/{test_db_filename}"
print(f"Database backup available for download at: {download_url}")
print(f"Full URL: http://localhost:5000{download_url}")

# Print instructions
print("\nTo download the database file, go to:")
print(f"http://localhost:5000/backups")
print("or use the direct download URL above.")
