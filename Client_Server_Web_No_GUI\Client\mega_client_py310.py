"""
MEGA Cloud Storage Client Module for Python 3.10+

This module provides functionality for uploading database files to MEGA cloud storage.
It is designed to work with the ifess client-server architecture and is compatible with Python 3.10+.
"""

import os
import sys
import time
import json
import base64
import logging
import threading
import traceback

# Apply patch for asyncio.coroutine before importing mega
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
try:
    import mega_patch
except ImportError:
    print("Warning: mega_patch.py not found, MEGA functionality may not work in Python 3.10+")

# Now import Mega with the patch applied
try:
    from mega import Mega
    MEGA_AVAILABLE = True
except ImportError as e:
    print(f"Error importing Mega: {e}")
    print("MEGA functionality will be disabled")
    MEGA_AVAILABLE = False
    Mega = None

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.network import NetworkMessage, send_message

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mega_client.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# MEGA account credentials
EMAIL = "<EMAIL>"
PASSWORD = "ptrj@123"

class MegaClient:
    """Client for uploading files to MEGA cloud storage"""
    
    def __init__(self, client_id=None):
        """Initialize the MEGA client"""
        self.client_id = client_id
        self.mega_api = None
        self.logged_in = False
        
        # Check if MEGA is available
        if not MEGA_AVAILABLE:
            logger.warning("MEGA functionality is disabled due to import errors")
            return
        
        # Try to login to MEGA
        self.login_mega()
    
    def login_mega(self):
        """Login to MEGA account"""
        if not MEGA_AVAILABLE:
            logger.error("Cannot login to MEGA: MEGA functionality is disabled")
            return False
            
        try:
            logger.info("Connecting to MEGA...")
            
            # Initialize MEGA API
            mega = Mega()
            
            # Try to login
            try:
                self.mega_api = mega.login(EMAIL, PASSWORD)
                self.logged_in = True
                logger.info(f"Successfully logged in to MEGA as {EMAIL}")
                
                # Get account details
                user_details = self.mega_api.get_user()
                if user_details:
                    logger.info(f"Logged in as: {user_details}")
                
                # Get storage space info
                space_info = self.mega_api.get_storage_space(giga=True)
                if space_info:
                    used = space_info['used']
                    total = space_info['total']
                    logger.info(f"Storage: {used:.2f} GB used of {total:.2f} GB total")
                
                return True
                
            except Exception as e:
                logger.error(f"Login failed: {str(e)}")
                logger.error(traceback.format_exc())
                return False
        
        except Exception as e:
            logger.error(f"Error initializing MEGA API: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def upload_file_to_mega(self, file_path):
        """Upload a file to MEGA"""
        if not MEGA_AVAILABLE:
            logger.error("Cannot upload to MEGA: MEGA functionality is disabled")
            return {
                'success': False,
                'error': "MEGA functionality is disabled",
                'file_name': os.path.basename(file_path) if file_path else "unknown"
            }
            
        file_name = os.path.basename(file_path)
        
        try:
            # Check if file exists and is readable
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"File not found: {file_path}")
            
            file_size = os.path.getsize(file_path)
            logger.info(f"Uploading file: {file_name} ({file_size} bytes)")
            
            # Make sure we're logged in
            if not self.logged_in:
                logger.info("Not logged in, attempting to login...")
                if not self.login_mega():
                    raise Exception("Failed to login to MEGA")
            
            # Upload file to MEGA
            # The upload method returns the file node ID
            file_node = self.mega_api.upload(file_path)
            
            if file_node:
                logger.info(f"✓ Uploaded {file_name} successfully")
                logger.info(f"  Node ID: {file_node}")
                
                # Try to get a link to the file
                try:
                    file_link = self.mega_api.get_upload_link(file_node)
                    logger.info(f"  Link: {file_link}")
                    return {
                        'success': True,
                        'file_name': file_name,
                        'file_size': file_size,
                        'node_id': file_node,
                        'link': file_link
                    }
                except Exception as e:
                    logger.warning(f"Could not get link: {str(e)}")
                    return {
                        'success': True,
                        'file_name': file_name,
                        'file_size': file_size,
                        'node_id': file_node,
                        'link': None
                    }
            else:
                raise Exception("Upload failed, no file node returned")
            
        except Exception as e:
            logger.error(f"✗ Error uploading {file_name}: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'file_name': file_name,
                'error': str(e)
            }
    
    def handle_mega_upload_request(self, socket, message):
        """Handle a request to upload a file to MEGA"""
        try:
            # Check if MEGA is available
            if not MEGA_AVAILABLE:
                logger.error("Cannot handle MEGA upload request: MEGA functionality is disabled")
                response = NetworkMessage(
                    NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                    {
                        'success': False,
                        'error': 'MEGA functionality is disabled',
                        'request_id': message.data.get('request_id') if hasattr(message, 'data') and isinstance(message.data, dict) else None
                    },
                    self.client_id
                )
                send_message(socket, response)
                return
                
            # Extract data from message
            data = message.data
            file_path = data.get('file_path')
            request_id = data.get('request_id')
            
            if not file_path:
                logger.error("No file path provided in upload request")
                # Send error response
                response = NetworkMessage(
                    NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                    {
                        'success': False,
                        'error': 'No file path provided',
                        'request_id': request_id
                    },
                    self.client_id
                )
                send_message(socket, response)
                return
            
            logger.info(f"Received MEGA upload request for file: {file_path}")
            
            # Send acknowledgment
            ack_message = NetworkMessage(
                NetworkMessage.TYPE_MEGA_UPLOAD_ACK,
                {
                    'file_path': file_path,
                    'request_id': request_id,
                    'timestamp': time.time()
                },
                self.client_id
            )
            send_message(socket, ack_message)
            
            # Start upload in a separate thread
            def upload_thread():
                try:
                    # Send progress update
                    progress_message = NetworkMessage(
                        NetworkMessage.TYPE_MEGA_UPLOAD_PROGRESS,
                        {
                            'file_path': file_path,
                            'request_id': request_id,
                            'progress': 0,
                            'status': 'starting',
                            'timestamp': time.time()
                        },
                        self.client_id
                    )
                    send_message(socket, progress_message)
                    
                    # Upload file
                    result = self.upload_file_to_mega(file_path)
                    
                    # Add request_id to result
                    result['request_id'] = request_id
                    
                    # Send result
                    result_message = NetworkMessage(
                        NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                        result,
                        self.client_id
                    )
                    send_message(socket, result_message)
                    
                except Exception as e:
                    logger.error(f"Error in upload thread: {str(e)}")
                    logger.error(traceback.format_exc())
                    
                    # Send error result
                    error_message = NetworkMessage(
                        NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                        {
                            'success': False,
                            'error': str(e),
                            'request_id': request_id
                        },
                        self.client_id
                    )
                    send_message(socket, error_message)
            
            # Start upload thread
            threading.Thread(target=upload_thread, daemon=True).start()
            
        except Exception as e:
            logger.error(f"Error handling MEGA upload request: {str(e)}")
            logger.error(traceback.format_exc())
            
            # Try to send error response
            try:
                error_message = NetworkMessage(
                    NetworkMessage.TYPE_MEGA_UPLOAD_RESULT,
                    {
                        'success': False,
                        'error': str(e),
                        'request_id': data.get('request_id') if isinstance(data, dict) else None
                    },
                    self.client_id
                )
                send_message(socket, error_message)
            except:
                logger.error("Failed to send error response")

# For testing
if __name__ == "__main__":
    client = MegaClient()
    if client.logged_in:
        print("Successfully logged in to MEGA")
        
        # Test upload
        test_file = input("Enter path to file for testing upload: ")
        if os.path.exists(test_file):
            result = client.upload_file_to_mega(test_file)
            print(f"Upload result: {result}")
        else:
            print(f"File not found: {test_file}")
    else:
        print("Failed to login to MEGA")
