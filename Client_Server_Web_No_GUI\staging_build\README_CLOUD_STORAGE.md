# IFESS Cloud Storage Integration

Dokumen ini menjelaskan integrasi penyimpanan cloud (MEGA dan Google Drive) untuk aplikasi IFESS.

## Fitur

1. **MEGA Cloud Storage**
   - Upload database ke MEGA cloud storage
   - Kompatibel dengan Python 3.10+
   - Patch otomatis untuk masalah `asyncio.coroutine`
   - Bekerja dengan aplikasi yang dikompilasi

2. **Google Drive Storage**
   - Upload database ke Google Drive
   - Dukungan untuk akun pribadi (<EMAIL>)
   - Auto-refresh token OAuth yang kedaluwarsa
   - File disimpan di folder pribadi dan dimiliki oleh akun pribadi
   - Struktur folder: Backup_PTRJ/IFESS/{client_id}/{client_id}_date_time.zip

## Cara Menggunakan

### Menjalankan Aplikasi

Gunakan script `run_with_cloud_support.bat` untuk menjalankan aplikasi dengan dukungan penyimpanan cloud:

```
run_with_cloud_support.bat
```

Script ini akan:
- Menginstal dependensi yang diperlukan
- Menjalankan server web
- Menjalankan client hidden
- Menjalankan client debug

### Upload ke MEGA

1. Buka antarmuka web server di http://localhost:5000
2. Navigasi ke halaman "Backups"
3. Pilih client dari dropdown
4. Klik tombol "Upload to MEGA" untuk file yang ingin diupload

### Upload ke Google Drive

1. Buka antarmuka web server di http://localhost:5000
2. Navigasi ke halaman "Backups"
3. Pilih client dari dropdown
4. Klik tombol "Upload to GDrive" untuk file yang ingin diupload
5. Jika ini pertama kali menggunakan Google Drive, browser akan terbuka untuk autentikasi
6. Login dengan akun Google pribadi (<EMAIL>)
7. Berikan izin yang diminta

## Konfigurasi

### MEGA

Kredensial MEGA disimpan di file `client_config.json` atau `mega_credentials.json`. Default:

```json
{
  "email": "<EMAIL>",
  "password": "ptrj@123"
}
```

### Google Drive

Untuk menggunakan akun pribadi, file `client_secrets.json` dan `token.json` harus ada di direktori yang sama dengan aplikasi.

- `client_secrets.json`: Berisi kredensial OAuth untuk aplikasi
- `token.json`: Berisi token akses yang akan diperbarui secara otomatis saat kedaluwarsa

## Troubleshooting

### Masalah MEGA

Jika upload MEGA gagal dengan error `asyncio.coroutine`, pastikan file `mega_client_compiled.py` ada di direktori yang sama dengan aplikasi.

### Masalah Google Drive

Jika upload Google Drive gagal:

1. Hapus file `token.json` jika ada
2. Jalankan aplikasi lagi
3. Browser akan terbuka untuk autentikasi baru
4. Login dengan akun Google pribadi (<EMAIL>)

## Informasi Debug

Aplikasi debug (ifess_client_debug.py) menampilkan informasi tentang status upload MEGA dan Google Drive. Gunakan aplikasi ini untuk memantau proses upload dan melihat pesan error jika terjadi masalah.
