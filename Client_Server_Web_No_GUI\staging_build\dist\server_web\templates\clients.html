{% extends 'layout.html' %}

{% block title %}Clients - Firebird Query Server{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Client Management</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-laptop me-2"></i>Connected Clients
                </h5>
            </div>
            <div class="card-body">
                {% if clients %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="clients-table">
                        <thead>
                            <tr>
                                <th>Client Name</th>
                                <th>Client ID</th>
                                <th>IP Address</th>
                                <th>Connected Since</th>
                                <th>Database Path</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in clients %}
                            <tr>
                                <td>{{ client.name }}</td>
                                <td><code>{{ client.id }}</code></td>
                                <td>{{ client.address }}</td>
                                <td>{{ client.connected_since }}</td>
                                <td>
                                    {% if client.db_info and client.db_info.path %}
                                    <code>{{ client.db_info.path }}</code>
                                    {% else %}
                                    <span class="text-muted">No database info</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('query_page') }}?client={{ client.id }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-code"></i> Query
                                        </a>
                                        <button class="btn btn-sm btn-info view-client-details" data-client-id="{{ client.id }}">
                                            <i class="fas fa-info-circle"></i> Details
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No clients connected. Start the server and wait for clients to connect.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Client Details Modal -->
<div class="modal fade" id="clientDetailsModal" tabindex="-1" aria-labelledby="clientDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="clientDetailsModalLabel">Client Details</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="client-details-content">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading client details...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // View client details
        $('.view-client-details').click(function() {
            const clientId = $(this).data('client-id');
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('clientDetailsModal'));
            modal.show();
            
            // Load client details
            $.get('/api/clients/' + clientId, function(data) {
                if (data.success) {
                    const client = data.client;
                    let detailsHtml = `
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Basic Information</h6>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-md-4 fw-bold">Client Name:</div>
                                    <div class="col-md-8">${client.name}</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-md-4 fw-bold">Client ID:</div>
                                    <div class="col-md-8"><code>${client.id}</code></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-md-4 fw-bold">IP Address:</div>
                                    <div class="col-md-8">${client.address}</div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4 fw-bold">Connected Since:</div>
                                    <div class="col-md-8">${client.connected_since}</div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // Database information
                    if (client.db_info && Object.keys(client.db_info).length > 0) {
                        detailsHtml += `
                            <div class="card">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Database Information</h6>
                                </div>
                                <div class="card-body">
                        `;
                        
                        for (const [key, value] of Object.entries(client.db_info)) {
                            detailsHtml += `
                                <div class="row mb-2">
                                    <div class="col-md-4 fw-bold">${key.charAt(0).toUpperCase() + key.slice(1)}:</div>
                                    <div class="col-md-8">${value}</div>
                                </div>
                            `;
                        }
                        
                        detailsHtml += `
                                </div>
                            </div>
                        `;
                    } else {
                        detailsHtml += `
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>No database information available.
                            </div>
                        `;
                    }
                    
                    $('#client-details-content').html(detailsHtml);
                } else {
                    $('#client-details-content').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>Failed to load client details.
                        </div>
                    `);
                }
            }).fail(function() {
                $('#client-details-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>Failed to load client details.
                    </div>
                `);
            });
        });
    });
</script>
{% endblock %}
