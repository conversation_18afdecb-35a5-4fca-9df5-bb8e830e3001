// Main JavaScript for Firebird Query Server Web Interface

$(document).ready(function() {
    // Server control buttons
    $('#start-server-btn, #dashboard-start-server').click(function() {
        $.post('/api/server/start', function(data) {
            if (data.success) {
                // Update UI
                updateServerStatus('Running');
                // Show success message
                showAlert('success', 'Server started successfully.');
            } else {
                showAlert('danger', 'Failed to start server.');
            }
        }).fail(function() {
            showAlert('danger', 'Failed to start server. Please check the logs.');
        });
    });

    $('#stop-server-btn, #dashboard-stop-server').click(function() {
        if (confirm('Are you sure you want to stop the server? All client connections will be closed.')) {
            $.post('/api/server/stop', function(data) {
                if (data.success) {
                    // Update UI
                    updateServerStatus('Stopped');
                    // Show success message
                    showAlert('success', 'Server stopped successfully.');
                } else {
                    showAlert('danger', 'Failed to stop server.');
                }
            }).fail(function() {
                showAlert('danger', 'Failed to stop server. Please check the logs.');
            });
        }
    });

    // Function to update server status in UI
    function updateServerStatus(status) {
        // Update status indicator
        $('#server-status-text').text(status);
        
        if (status === 'Running') {
            $('#server-status-indicator')
                .removeClass('bg-danger')
                .addClass('bg-success');
            
            $('#server-status-indicator i')
                .removeClass('fa-stop')
                .addClass('fa-play');
            
            // Show stop button, hide start button
            $('#start-server-btn, #dashboard-start-server').addClass('d-none');
            $('#stop-server-btn, #dashboard-stop-server').removeClass('d-none');
        } else {
            $('#server-status-indicator')
                .removeClass('bg-success')
                .addClass('bg-danger');
            
            $('#server-status-indicator i')
                .removeClass('fa-play')
                .addClass('fa-stop');
            
            // Show start button, hide stop button
            $('#start-server-btn, #dashboard-start-server').removeClass('d-none');
            $('#stop-server-btn, #dashboard-stop-server').addClass('d-none');
        }
    }

    // Function to show alert message
    function showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;
        
        // Insert alert at the top of the content area
        $('.container-fluid').prepend(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }

    // Refresh client list periodically
    function refreshClientList() {
        // Only refresh if we're on the clients page or dashboard
        if (window.location.pathname === '/clients' || window.location.pathname === '/') {
            $.get('/api/clients', function(data) {
                if (data.success) {
                    // Update client count on dashboard
                    if ($('#client-count').length) {
                        $('#client-count').text(data.clients.length);
                    }
                    
                    // Update client table if it exists
                    if ($('#clients-table').length) {
                        if (data.clients.length > 0) {
                            let tableHtml = '';
                            
                            data.clients.forEach(client => {
                                tableHtml += `
                                    <tr>
                                        <td>${client.name}</td>
                                        <td><code>${client.id}</code></td>
                                        <td>${client.address}</td>
                                        <td>${client.connected_since}</td>
                                        <td>
                                            ${client.db_info && client.db_info.path 
                                                ? `<code>${client.db_info.path}</code>` 
                                                : '<span class="text-muted">No database info</span>'}
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/query?client=${client.id}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-code"></i> Query
                                                </a>
                                                <button class="btn btn-sm btn-info view-client-details" data-client-id="${client.id}">
                                                    <i class="fas fa-info-circle"></i> Details
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                `;
                            });
                            
                            $('#clients-table tbody').html(tableHtml);
                            
                            // Reattach event handlers
                            $('.view-client-details').click(function() {
                                const clientId = $(this).data('client-id');
                                viewClientDetails(clientId);
                            });
                        } else {
                            $('#clients-table').parent().html(`
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>No clients connected. Start the server and wait for clients to connect.
                                </div>
                            `);
                        }
                    }
                }
            });
        }
    }

    // Function to view client details
    function viewClientDetails(clientId) {
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('clientDetailsModal'));
        modal.show();
        
        // Load client details
        $.get('/api/clients/' + clientId, function(data) {
            if (data.success) {
                const client = data.client;
                let detailsHtml = `
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Basic Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row mb-2">
                                <div class="col-md-4 fw-bold">Client Name:</div>
                                <div class="col-md-8">${client.name}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-4 fw-bold">Client ID:</div>
                                <div class="col-md-8"><code>${client.id}</code></div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-4 fw-bold">IP Address:</div>
                                <div class="col-md-8">${client.address}</div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 fw-bold">Connected Since:</div>
                                <div class="col-md-8">${client.connected_since}</div>
                            </div>
                        </div>
                    </div>
                `;
                
                // Database information
                if (client.db_info && Object.keys(client.db_info).length > 0) {
                    detailsHtml += `
                        <div class="card">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Database Information</h6>
                            </div>
                            <div class="card-body">
                    `;
                    
                    for (const [key, value] of Object.entries(client.db_info)) {
                        detailsHtml += `
                            <div class="row mb-2">
                                <div class="col-md-4 fw-bold">${key.charAt(0).toUpperCase() + key.slice(1)}:</div>
                                <div class="col-md-8">${value}</div>
                            </div>
                        `;
                    }
                    
                    detailsHtml += `
                            </div>
                        </div>
                    `;
                } else {
                    detailsHtml += `
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No database information available.
                        </div>
                    `;
                }
                
                $('#client-details-content').html(detailsHtml);
            } else {
                $('#client-details-content').html(`
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>Failed to load client details.
                    </div>
                `);
            }
        }).fail(function() {
            $('#client-details-content').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>Failed to load client details.
                </div>
            `);
        });
    }

    // Refresh client list every 10 seconds
    setInterval(refreshClientList, 10000);

    // Check URL parameters for query page
    if (window.location.pathname === '/query') {
        const urlParams = new URLSearchParams(window.location.search);
        const queryParam = urlParams.get('query');
        const clientParam = urlParams.get('client');
        
        // If query parameter exists, set it in the editor
        if (queryParam && window.editor) {
            window.editor.setValue(decodeURIComponent(queryParam));
        }
        
        // If client parameter exists, select it in the dropdown
        if (clientParam) {
            $('#client-select option[value="all"]').prop('selected', false);
            $(`#client-select option[value="${clientParam}"]`).prop('selected', true);
        }
    }
});
