@echo off
echo Robust Firebird Database Transfer Tool
echo ===================================
echo.

if "%~1"=="" (
    echo Usage: import_database.bat [client_id] [host] [port]
    echo.
    echo Example: import_database.bat client1 ************* 8080
    echo.
    
    set /p CLIENT_ID=Enter the client ID: 
    set /p HOST=Enter the host (default: localhost): 
    set /p PORT=Enter the port (default: 8080): 
    
    if "%HOST%"=="" set HOST=localhost
    if "%PORT%"=="" set PORT=8080
) else (
    set CLIENT_ID=%~1
    set HOST=%~2
    if "%HOST%"=="" set HOST=localhost
    set PORT=%~3
    if "%PORT%"=="" set PORT=8080
)

echo.
echo Starting database import from %CLIENT_ID% at %HOST%:%PORT%
echo.

python robust_db_transfer.py "%CLIENT_ID%" "%HOST%" "%PORT%"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Database import completed successfully!
) else (
    echo.
    echo Database import failed. Check the log file for details.
)

echo.
pause
