{% extends "base.html" %}

{% block title %}Monitoring Dashboard{% endblock %}

{% block content %}
    <h1 class="mb-4">Monitoring Dashboard</h1>

    <div class="row">
        <div class="col-md-4 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Monitoring Queries
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group" id="monitoring-queries">
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">monitor_diff_job_field</h6>
                                <button type="button" class="btn btn-sm btn-primary monitoring-query" data-query-name="monitor_diff_job_field">
                                    <i class="fas fa-play me-1"></i>Run
                                </button>
                            </div>
                            <p class="mb-0 small text-muted">
                                Query untuk menemukan data yang salah di bulan berjalan (bulan saat ini). Monitoring query to find inconsistencies between job codes and field codes.
                            </p>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">simple_test</h6>
                                <button type="button" class="btn btn-sm btn-primary monitoring-query" data-query-name="simple_test">
                                    <i class="fas fa-play me-1"></i>Run
                                </button>
                            </div>
                            <p class="mb-0 small text-muted">
                                Simple test query to check if we can get results
                            </p>
                        </div>
                    </div>
                    </div>
                </div>
            </div>

        <div class="col-md-8 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>Monitoring Results
                        <div class="spinner-border spinner-border-sm text-light ms-2 d-none" id="monitoring-loading" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </h5>
                </div>
                <div class="card-body">
                    <div id="monitoring-results">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Select a monitoring query from the left panel to run it.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block scripts %}
<script>
    $(document).ready(function() {
        // Run monitoring query
        $('.monitoring-query').click(function() {
                const queryName = $(this).data('query-name');

                // Show loading indicator
                $('#monitoring-loading').removeClass('d-none');

                // Clear previous results
                $('#monitoring-results').html('');

                // Run query
                $.post('/api/monitoring/run', {
                    query_name: queryName
                }, function(data) {
                    if (data.success) {
                        // Poll for results
                        pollForResults(queryName);
                    } else {
                        // Hide loading indicator
                        $('#monitoring-loading').addClass('d-none');

                        // Show error message
                        $('#monitoring-results').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>${data.message}
                            </div>
                        `);
                    }
                }).fail(function() {
                    // Hide loading indicator
                    $('#monitoring-loading').addClass('d-none');

                    // Show error message
                    $('#monitoring-results').html(`
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-circle me-2"></i>Failed to run monitoring query. Please try again.
                        </div>
                    `);
                });
            });

            // Function to poll for monitoring results
            function pollForResults(queryName) {
                // Poll for results
                const pollInterval = setInterval(function() {
                    $.get('/api/query/results', function(data) {
                        if (data.success) {
                            // Get poll count
                            const pollCount = $('#monitoring-loading').data('poll-count') || 0;

                            // Log polling status
                            console.log(`Polling for results (attempt ${pollCount + 1}):`, data.results);

                            // Hide loading indicator if we have results or it's been more than 20 seconds
                            if (data.results.length > 0 || pollCount > 20) {
                                $('#monitoring-loading').addClass('d-none');
                                clearInterval(pollInterval);
                                console.log('Polling complete');
                            } else {
                                // Increment poll count
                                $('#monitoring-loading').data('poll-count', pollCount + 1);
                            }

                            if (data.results.length > 0) {
                                // Process results
                                console.log(`Found ${data.results.length} results, processing...`);
                                processResults(data.results, queryName);
                            } else if (pollCount > 20) {
                                // No results after 20 seconds
                                console.log('No results after 20 seconds');
                                $('#monitoring-results').html(`
                                    <div class="alert alert-warning">
                                        <i class="fas fa-exclamation-triangle me-2"></i>No results received from clients after 20 seconds.
                                        <hr>
                                        <p>This could be due to:</p>
                                        <ul>
                                            <li>No clients are connected</li>
                                            <li>Clients are not responding</li>
                                            <li>The query is taking too long to execute</li>
                                            <li>The query returned no results</li>
                                        </ul>
                                        <p>Check the server logs for more information.</p>
                                    </div>
                                `);
                            }
                        } else {
                            // Hide loading indicator
                            $('#monitoring-loading').addClass('d-none');
                            clearInterval(pollInterval);
                            console.log('Error retrieving results');

                            // Show error message
                            $('#monitoring-results').html(`
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>Failed to retrieve results.
                                </div>
                            `);
                        }
                    }).fail(function(xhr, status, error) {
                        // Hide loading indicator
                        $('#monitoring-loading').addClass('d-none');
                        clearInterval(pollInterval);
                        console.log('AJAX error:', status, error);

                        // Show error message
                        $('#monitoring-results').html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>Failed to retrieve results: ${error}
                            </div>
                        `);
                    });
                }, 1000);

                // Initialize poll count
                $('#monitoring-loading').data('poll-count', 0);
                console.log('Started polling for results');
            }

            // Function to process and display monitoring results
            function processResults(results, queryName) {
                // Group results by client
                const clientResults = {};
                let allResultsHtml = `<h4 class="mb-3">Results for: ${queryName}</h4>`;

                if (results.length === 0) {
                    allResultsHtml += `
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No results found for this query. This could mean:
                            <ul class="mb-0 mt-2">
                                <li>There are no records that match the query criteria</li>
                                <li>The database doesn't contain the tables or fields referenced in the query</li>
                                <li>The query syntax might need adjustment for your specific database</li>
                            </ul>
                        </div>
                    `;

                    // Update monitoring results
                    $('#monitoring-results').html(allResultsHtml);
                    return;
                }

                // Log the results for debugging
                console.log('Processing results:', results);

                results.forEach(result => {
                    const clientName = result.client_name || 'Unknown Client';
                    const clientId = result.client_id || 'unknown';

                    console.log(`Processing result from client: ${clientName}`);
                    console.log('Result data:', result);

                    if (!clientResults[clientId]) {
                        clientResults[clientId] = {
                            name: clientName,
                            results: []
                        };
                    }

                    clientResults[clientId].results.push(result);

                    // Check if result has rows
                    if (result.rows && result.rows.length > 0) {
                        console.log(`Client ${clientName} has ${result.rows.length} rows`);
                        // Add to all results
                        allResultsHtml += generateResultHtml(result, clientName);
                    } else if (result.error) {
                        console.log(`Client ${clientName} returned error: ${result.error}`);
                        allResultsHtml += `
                            <div class="alert alert-danger mb-4">
                                <h5 class="alert-heading">Error from ${clientName}</h5>
                                <p>${result.error}</p>
                            </div>
                        `;
                    } else {
                        console.log(`Client ${clientName} returned no rows`);
                        allResultsHtml += `
                            <div class="alert alert-info mb-4">
                                <h5 class="alert-heading">No results from ${clientName}</h5>
                                <p>The query executed successfully but returned no rows.</p>
                            </div>
                        `;
                    }
                });

                // Update monitoring results
                $('#monitoring-results').html(allResultsHtml);
            }

            // Function to generate HTML for a result
            function generateResultHtml(result, clientName = null) {
                let html = '';

                // Log the result structure for debugging
                console.log('Generating HTML for result:', result);

                // Add client name if provided
                if (clientName) {
                    html += `<h5 class="mb-3">${clientName}</h5>`;
                }

                // Check if result has error
                if (result.error) {
                    html += `
                        <div class="alert alert-danger mb-4">
                            <i class="fas fa-exclamation-circle me-2"></i>${result.error}
                        </div>
                    `;
                    return html;
                }

                // Check if result has data
                if (!result.headers || !result.rows || result.rows.length === 0) {
                    html += `
                        <div class="alert alert-info mb-4">
                            <i class="fas fa-info-circle me-2"></i>Query executed successfully, but no data returned.
                        </div>
                    `;
                    return html;
                }

                // Log the headers and first row for debugging
                console.log('Headers:', result.headers);
                console.log('First row:', result.rows[0]);

                // Add result table with improved styling and features
                html += `
                    <div class="card mb-4">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Query Results</h5>
                            <div>
                                <button class="btn btn-sm btn-light me-2" onclick="exportTableToCSV(this, 'query_results.csv')">
                                    <i class="fas fa-file-csv me-1"></i>Export CSV
                                </button>
                                <button class="btn btn-sm btn-light" onclick="printTable(this)">
                                    <i class="fas fa-print me-1"></i>Print
                                </button>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-striped table-bordered table-hover mb-0" id="result-table-${Date.now()}">
                                    <thead class="table-primary sticky-top">
                                        <tr>
                `;

                // Add headers - ensure we have headers
                const headers = result.headers || Object.keys(result.rows[0] || {});

                headers.forEach(header => {
                    html += `<th class="position-relative">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>${header}</span>
                                    <span class="ms-2 text-muted sort-icon" onclick="sortTable(this)">
                                        <i class="fas fa-sort"></i>
                                    </span>
                                </div>
                            </th>`;
                });

                html += `
                                        </tr>
                                    </thead>
                                    <tbody>
                `;

                // Add rows with improved formatting
                result.rows.forEach(row => {
                    html += '<tr>';
                    headers.forEach(header => {
                        // Handle different row formats
                        let value = '';
                        if (typeof row === 'object') {
                            if (Array.isArray(row)) {
                                // If row is an array, use the index of the header in the headers array
                                const index = headers.indexOf(header);
                                value = index >= 0 && index < row.length ? row[index] : '';
                            } else {
                                // If row is an object, use the header as a key
                                value = row[header] !== null && row[header] !== undefined ? row[header] : '';
                            }
                        }

                        // Format the value based on its type
                        let formattedValue = value;
                        let cellClass = '';

                        // Format dates
                        if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
                            try {
                                const date = new Date(value);
                                if (!isNaN(date.getTime())) {
                                    formattedValue = date.toLocaleDateString();
                                    cellClass = 'text-nowrap';
                                }
                            } catch (e) {
                                // Not a valid date, use original value
                            }
                        }
                        // Format numbers
                        else if (typeof value === 'number' || (typeof value === 'string' && !isNaN(value) && value.trim() !== '')) {
                            if (header.toLowerCase().includes('price') ||
                                header.toLowerCase().includes('amount') ||
                                header.toLowerCase().includes('total') ||
                                header.toLowerCase().includes('nilai')) {
                                formattedValue = parseFloat(value).toLocaleString(undefined, {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });
                                cellClass = 'text-end';
                            }
                        }

                        html += `<td class="${cellClass}">${formattedValue}</td>`;
                    });
                    html += '</tr>';
                });

                html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="card-footer bg-light">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-muted">
                                    <i class="fas fa-info-circle me-1"></i>Showing ${result.rows.length} rows.
                                    ${result.truncated ? ' <span class="text-warning">(Results truncated)</span>' : ''}
                                </div>
                                <div>
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">Filter:</span>
                                        <input type="text" class="form-control" placeholder="Type to filter..."
                                               onkeyup="filterTable(this)" style="max-width: 200px;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                return html;
            }

            // Table sorting function
            function sortTable(element) {
                const table = $(element).closest('table');
                const headerIndex = $(element).closest('th').index();
                const rows = table.find('tbody tr').toArray();
                const isAscending = $(element).find('i').hasClass('fa-sort') || $(element).find('i').hasClass('fa-sort-down');

                // Update sort icon
                table.find('th .sort-icon i').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
                $(element).find('i').removeClass('fa-sort').addClass(isAscending ? 'fa-sort-up' : 'fa-sort-down');

                // Sort rows
                rows.sort((a, b) => {
                    const aValue = $(a).find('td').eq(headerIndex).text().trim();
                    const bValue = $(b).find('td').eq(headerIndex).text().trim();

                    // Check if values are numbers
                    const aNum = parseFloat(aValue.replace(/,/g, ''));
                    const bNum = parseFloat(bValue.replace(/,/g, ''));

                    if (!isNaN(aNum) && !isNaN(bNum)) {
                        return isAscending ? aNum - bNum : bNum - aNum;
                    }

                    // Check if values are dates
                    const aDate = new Date(aValue);
                    const bDate = new Date(bValue);

                    if (!isNaN(aDate) && !isNaN(bDate)) {
                        return isAscending ? aDate - bDate : bDate - aDate;
                    }

                    // Default to string comparison
                    return isAscending ?
                        aValue.localeCompare(bValue) :
                        bValue.localeCompare(aValue);
                });

                // Reattach sorted rows
                table.find('tbody').empty().append(rows);
            }

            // Table filtering function
            function filterTable(input) {
                const filterValue = $(input).val().toLowerCase();
                const table = $(input).closest('.card').find('table');

                table.find('tbody tr').each(function() {
                    const rowText = $(this).text().toLowerCase();
                    $(this).toggle(rowText.indexOf(filterValue) > -1);
                });

                // Update row count
                const visibleRows = table.find('tbody tr:visible').length;
                const totalRows = table.find('tbody tr').length;

                $(input).closest('.card-footer').find('.text-muted').html(
                    `<i class="fas fa-info-circle me-1"></i>Showing ${visibleRows} of ${totalRows} rows.`
                );
            }

            // Export table to CSV
            function exportTableToCSV(button, filename) {
                const table = $(button).closest('.card').find('table');
                let csv = [];

                // Get headers
                const headers = [];
                table.find('thead th').each(function() {
                    headers.push($(this).text().trim());
                });
                csv.push(headers.join(','));

                // Get rows
                table.find('tbody tr:visible').each(function() {
                    const row = [];
                    $(this).find('td').each(function() {
                        // Escape quotes and wrap in quotes if contains comma
                        let cell = $(this).text().trim();
                        if (cell.includes(',') || cell.includes('"') || cell.includes('\'')) {
                            cell = '"' + cell.replace(/"/g, '""') + '"';
                        }
                        row.push(cell);
                    });
                    csv.push(row.join(','));
                });

                // Download CSV
                const csvContent = csv.join('\n');
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);

                const link = document.createElement('a');
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            // Print table
            function printTable(button) {
                const table = $(button).closest('.card').find('table');
                const tableHtml = table.prop('outerHTML');

                const printWindow = window.open('', '_blank');
                printWindow.document.write(`
                    <html>
                        <head>
                            <title>Query Results</title>
                            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                            <style>
                                body { padding: 20px; }
                                table { width: 100%; border-collapse: collapse; }
                                th, td { padding: 8px; border: 1px solid #ddd; }
                                th { background-color: #f8f9fa; }
                                .table-striped tbody tr:nth-of-type(odd) { background-color: rgba(0,0,0,.05); }
                            </style>
                        </head>
                        <body>
                            <h3 class="mb-3">Query Results</h3>
                            ${tableHtml}
                        </body>
                    </html>
                `);
                printWindow.document.close();
                printWindow.focus();
                printWindow.print();
            }
        });
    </script>
{% endblock %}
