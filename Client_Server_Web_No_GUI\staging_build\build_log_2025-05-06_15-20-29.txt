IFESS Client Suite - Comprehensive Build Log 
Build started: 05/06/2025 15:20:29,10 
================================================ 
 
Current directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build 
[1/7] Performing pre-build checks... 
Python 3.13.3
WARNING: UPX not found - compression will be disabled 
Checking source files... 
Found: ifess_client_hidden.py 
Found: ifess_client_debug.py 
Found: ifess_config_gui.py 
Pre-build checks completed successfully 
[2/7] Cleaning build environment... 
Removing old dist directory... 
Removing old build directory... 
Build environment cleaned 
[3/7] Checking and installing dependencies... 
Installing/updating Python dependencies... 
Installing: google-api-python-client 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-api-python-client in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.171.0)
Requirement already satisfied: httplib2<1.0.0,>=0.19.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (0.22.0)
Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (2.40.3)
Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (0.2.0)
Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (2.24.2)
Requirement already satisfied: uritemplate<5,>=3.0.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (4.1.1)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.70.0)
Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.19.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (5.29.5)
Requirement already satisfied: proto-plus<2.0.0,>=1.22.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.26.1)
Requirement already satisfied: requests<3.0.0,>=2.18.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (2.32.3)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (4.9)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from httplib2<1.0.0,>=0.19.0->google-api-python-client) (3.2.1)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (2025.1.31)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (0.6.1)
Installing: google-auth 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.40.3)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth) (0.6.1)
Installing: google-auth-oauthlib 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth-oauthlib in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (1.2.2)
Requirement already satisfied: google-auth>=2.15.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-oauthlib) (2.40.3)
Requirement already satisfied: requests-oauthlib>=0.7.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-oauthlib) (2.0.0)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth>=2.15.0->google-auth-oauthlib) (0.6.1)
Requirement already satisfied: oauthlib>=3.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.2.2)
Requirement already satisfied: requests>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib) (2.32.3)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (2025.1.31)
Installing: google-auth-httplib2 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth-httplib2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (0.2.0)
Requirement already satisfied: google-auth in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-httplib2) (2.40.3)
Requirement already satisfied: httplib2>=0.19.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-httplib2) (0.22.0)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from httplib2>=0.19.0->google-auth-httplib2) (3.2.1)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth->google-auth-httplib2) (0.6.1)
Installing: requests 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: requests in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.32.3)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (2025.1.31)
Installing: fdb 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: fdb in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.0.3)
Requirement already satisfied: firebird-base~=2.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from fdb) (2.0.2)
Requirement already satisfied: python-dateutil~=2.8 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from fdb) (2.9.0.post0)
Requirement already satisfied: protobuf~=5.29 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from firebird-base~=2.0->fdb) (5.29.5)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from python-dateutil~=2.8->fdb) (1.17.0)
Installing: schedule 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: schedule in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (1.2.2)
Dependencies check completed 
[4/7] Building IFESS Hidden Client... 
Building ifess_client_hidden.exe... 
'pyinstaller' is not recognized as an internal or external command,
operable program or batch file.
ERROR: Failed to build ifess_client_hidden.exe 
[5/7] Building IFESS Debug Client... 
Building ifess_client_debug.exe... 
'pyinstaller' is not recognized as an internal or external command,
operable program or batch file.
ERROR: Failed to build ifess_client_debug.exe 
[6/7] Building IFESS Config GUI... 
Building ifess_config_gui.exe... 
'pyinstaller' is not recognized as an internal or external command,
operable program or batch file.
ERROR: Failed to build ifess_config_gui.exe 
[7/7] Post-build tasks and packaging... 
Copying additional files to dist directory... 
Copied: client_config.json 
Copied: client_config_oauth_tokens.json 
Copied: run_client_debug.bat 
Copied: run_client_with_debug_gui.bat 
Creating launcher batch files... 
Created launcher batch files 
Creating distribution README... 
Created distribution README 
Build completed: 05/06/2025 15:20:41,18 
Results: 
  Successful builds: 0/3 
  Errors: 3 
  Warnings: 1 
Distribution ready in: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ 
Build log saved as: build_log_2025-05-06_15-20-29.txt 
   BUILD FAILED 
