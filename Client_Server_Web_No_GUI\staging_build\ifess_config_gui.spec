# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\ifess_config_gui.py'],
    pathex=[],
    binaries=[],
    datas=[('D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\client_config.json', '.'), ('D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\ptrj-backup-services-account.json', '.')],
    hiddenimports=['tkinter', 'fdb', 'socket', 'json', 'requests', 'googleapiclient', 'googleapiclient.discovery', 'google.oauth2', 'google.oauth2.service_account'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ifess_config_gui',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
