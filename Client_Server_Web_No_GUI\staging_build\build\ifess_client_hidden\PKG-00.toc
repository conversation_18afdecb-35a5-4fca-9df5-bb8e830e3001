('D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\ifess_client_hidden\\ifess_client_hidden.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\ifess_client_hidden\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\ifess_client_hidden\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\ifess_client_hidden\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\ifess_client_hidden\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\ifess_client_hidden\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\ifess_client_hidden\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('ifess_client_hidden',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\ifess_client_hidden.py',
   'PYSOURCE'),
  ('python313.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.dll',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\PublicKey\\_ed25519.pyd',
   'BINARY'),
  ('Crypto\\Util\\_cpuid_c.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Util\\_cpuid_c.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD5.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_MD5.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_ARC4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_ARC4.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ec_ws.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\PublicKey\\_ec_ws.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ctr.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_ctr.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_clmul.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_ghash_clmul.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_eksblowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cast.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_cast.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA384.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_SHA384.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2b.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_BLAKE2b.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_blowfish.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_blowfish.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des3.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_des3.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA1.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_SHA1.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_keccak.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_keccak.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_MD2.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aes.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_aes.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_RIPEMD160.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_RIPEMD160.pyd',
   'BINARY'),
  ('Crypto\\Util\\_strxor.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Util\\_strxor.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_aesni.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_aesni.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_pkcs1_decode.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_pkcs1_decode.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cfb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_cfb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_MD4.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_MD4.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_chacha20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_chacha20.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_ghash_portable.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_ghash_portable.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ocb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_ocb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_poly1305.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_poly1305.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA224.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_SHA224.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve25519.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\PublicKey\\_curve25519.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_des.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_des.pyd',
   'BINARY'),
  ('Crypto\\Protocol\\_scrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Protocol\\_scrypt.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_curve448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\PublicKey\\_curve448.pyd',
   'BINARY'),
  ('Crypto\\PublicKey\\_ed448.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\PublicKey\\_ed448.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_Salsa20.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_Salsa20.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ecb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_ecb.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_arc2.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_arc2.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_ofb.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_ofb.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA512.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_SHA512.pyd',
   'BINARY'),
  ('Crypto\\Math\\_modexp.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Math\\_modexp.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_BLAKE2s.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_BLAKE2s.pyd',
   'BINARY'),
  ('Crypto\\Hash\\_SHA256.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Hash\\_SHA256.pyd',
   'BINARY'),
  ('Crypto\\Cipher\\_raw_cbc.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Crypto\\Cipher\\_raw_cbc.pyd',
   'BINARY'),
  ('_lzma.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_cffi_backend.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\charset_normalizer\\md__mypyc.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\charset_normalizer\\md.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_wmi.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_wmi.pyd',
   'EXTENSION'),
  ('_uuid.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_uuid.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python3.dll',
   'BINARY'),
  ('pywin32_system32\\pywintypes313.dll',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\pywin32_system32\\pywintypes313.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('client_config.json',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\client_config.json',
   'DATA'),
  ('gdrive_client_module.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\gdrive_client_module.py',
   'DATA'),
  ('mega_client_py310.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\mega_client_py310.py',
   'DATA'),
  ('ptrj-backup-services-account.json',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\ptrj-backup-services-account.json',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\licenses\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\cryptography-44.0.1.dist-info\\licenses\\LICENSE',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\cryptography-44.0.1.dist-info\\INSTALLER',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\cryptography-44.0.1.dist-info\\WHEEL',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\licenses\\LICENSE.APACHE',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\cryptography-44.0.1.dist-info\\licenses\\LICENSE.APACHE',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\licenses\\LICENSE.BSD',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\cryptography-44.0.1.dist-info\\licenses\\LICENSE.BSD',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\cryptography-44.0.1.dist-info\\METADATA',
   'DATA'),
  ('cryptography-44.0.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\cryptography-44.0.1.dist-info\\RECORD',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\realtimebidding.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\realtimebidding.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessnotifications.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessnotifications.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicecontrol.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicecontrol.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigee.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigee.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admob.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\admob.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\texttospeech.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\texttospeech.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sts.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\sts.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\privateca.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\privateca.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.reports_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.reports_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\index.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\index.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\transcoder.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\transcoder.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\safebrowsing.v5.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\safebrowsing.v5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\retail.v2alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\retail.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclicksearch.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclicksearch.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\redis.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\redis.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\script.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\script.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iap.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\iap.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexperiencereport.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexperiencereport.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsub.v1beta1a.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsub.v1beta1a.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workstations.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\workstations.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\monitoring.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\monitoring.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sourcerepo.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\sourcerepo.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\publicca.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\publicca.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsdata.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsdata.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androidmanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\androidmanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workstations.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\workstations.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playgrouping.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\playgrouping.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\container.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\container.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\fcmdata.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\fcmdata.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedatabase.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedatabase.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.reports_v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.reports_v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastore.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datacatalog.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datacatalog.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\css.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\css.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudprofiler.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudprofiler.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.datasources_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.datasources_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\fcm.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\fcm.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudsearch.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudsearch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\translate.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\translate.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firestore.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firestore.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\books.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\books.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\publicca.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\publicca.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\prod_tt_sasportal.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\prod_tt_sasportal.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vmwareengine.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vmwareengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\realtimebidding.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\realtimebidding.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\baremetalsolution.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\baremetalsolution.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\kmsinventory.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\kmsinventory.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androiddeviceprovisioning.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\androiddeviceprovisioning.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tagmanager.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\tagmanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androidenterprise.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\androidenterprise.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\parallelstore.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\parallelstore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oracledatabase.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\oracledatabase.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudidentity.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudidentity.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oslogin.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\oslogin.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkconnectivity.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedidentities.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedidentities.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\billingbudgets.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\billingbudgets.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v3.5.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v3.5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataform.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataform.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\websecurityscanner.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sheets.v4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\sheets.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\policytroubleshooter.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\speech.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\speech.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\genomics.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\genomics.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.quota_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.quota_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.directoryv1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.directoryv1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recommender.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\recommender.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\connectors.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\connectors.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sasportal.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\sasportal.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alloydb.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\alloydb.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\siteVerification.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\siteVerification.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudiot.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudiot.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datalabeling.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datalabeling.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recommendationengine.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\recommendationengine.v1beta1.json',
   'DATA'),
  ('google_api_python_client-2.168.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_python_client-2.168.0.dist-info\\RECORD',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\backupdr.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\backupdr.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admob.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\admob.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebase.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebase.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\content.v2.1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\content.v2.1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vmmigration.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vmmigration.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1beta4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1beta4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tagmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\tagmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\integrations.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\integrations.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\healthcare.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\healthcare.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\trafficdirector.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\trafficdirector.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigateway.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigateway.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datamigration.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datamigration.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ideahub.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\ideahub.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\webrisk.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\webrisk.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apphub.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\apphub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workloadmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\workloadmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\blogger.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\blogger.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recommender.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\recommender.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ideahub.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\ideahub.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\civicinfo.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\civicinfo.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\compute.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\compute.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\factchecktools.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\factchecktools.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\aiplatform.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\aiplatform.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\airquality.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\airquality.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\clouddebugger.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\clouddebugger.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\acmedns.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\acmedns.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflows.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflows.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iam.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\iam.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.lfp_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.lfp_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\transcoder.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\transcoder.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\fitness.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\fitness.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\contentwarehouse.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\contentwarehouse.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ml.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\ml.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\identitytoolkit.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\identitytoolkit.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firestore.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firestore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\documentai.v1beta3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\documentai.v1beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\aiplatform.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\aiplatform.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vision.v1p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vision.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.products_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.products_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analytics.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\analytics.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\manufacturers.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\manufacturers.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\checks.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\checks.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\artifactregistry.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\artifactregistry.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networksecurity.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\networksecurity.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkmanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkmanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaserules.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaserules.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gamesConfiguration.v1configuration.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gamesConfiguration.v1configuration.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\orgpolicy.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\orgpolicy.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\integrations.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\integrations.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\privateca.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\privateca.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gameservices.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gameservices.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\deploymentmanager.alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\deploymentmanager.alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\androidpublisher.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\androidpublisher.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\accessapproval.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\accessapproval.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firestore.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firestore.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\identitytoolkit.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\identitytoolkit.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtasks.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtasks.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\monitoring.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\monitoring.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicenetworking.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicenetworking.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vault.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vault.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workspaceevents.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\workspaceevents.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtube.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtube.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\places.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\places.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\clouderrorreporting.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\clouderrorreporting.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\migrationcenter.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\migrationcenter.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chromemanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\chromemanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.directory_v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.directory_v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedynamiclinks.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedynamiclinks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\people.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\people.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\publicca.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\publicca.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\documentai.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\documentai.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gmail.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gmail.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\assuredworkloads.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chromepolicy.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\chromepolicy.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\toolresults.v1beta3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\toolresults.v1beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\authorizedbuyersmarketplace.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\securitycenter.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dns.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dns.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1beta5.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1beta5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\trafficdirector.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\trafficdirector.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\solar.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\solar.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessaccountmanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessaccountmanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\compute.alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\compute.alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudsupport.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudsupport.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\storage.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\storage.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigquerydatapolicy.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigquerydatapolicy.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsreporting.v4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsreporting.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\file.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\file.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vision.v1p2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vision.v1p2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtrace.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtrace.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\walletobjects.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\walletobjects.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseml.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseml.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\blockchainnodeengine.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\blockchainnodeengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceusage.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceusage.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigquery.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigquery.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudchannel.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudchannel.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\billingbudgets.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\billingbudgets.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p4beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p4beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessbusinessinformation.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessbusinessinformation.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playintegrity.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\playintegrity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\texttospeech.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\texttospeech.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\container.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\container.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.conversions_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.conversions_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtubereporting.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtubereporting.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securitycenter.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\securitycenter.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\runtimeconfig.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\eventarc.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\eventarc.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iam.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\iam.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discoveryengine.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\discoveryengine.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domains.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\domains.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\genomics.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\genomics.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vectortile.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vectortile.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsadmin.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\storagetransfer.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\storagetransfer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v3.4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v3.4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\language.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\language.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.reportsv1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.reportsv1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alertcenter.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\alertcenter.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\containeranalysis.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\containeranalysis.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discovery.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\discovery.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\smartdevicemanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\smartdevicemanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\translate.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\translate.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\meet.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\meet.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtrace.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtrace.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessverifications.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessverifications.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datalineage.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datalineage.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\beyondcorp.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\beyondcorp.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\storagebatchoperations.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\storagebatchoperations.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastore.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastore.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceusage.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceusage.v1.json',
   'DATA'),
  ('google_api_python_client-2.168.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_python_client-2.168.0.dist-info\\METADATA',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alloydb.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\alloydb.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\binaryauthorization.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryconnection.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\alloydb.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\alloydb.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\streetviewpublish.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\streetviewpublish.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudcontrolspartner.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\addressvalidation.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\addressvalidation.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\netapp.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\netapp.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedkafka.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedkafka.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.reviews_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.reviews_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\recaptchaenterprise.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\recaptchaenterprise.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policysimulator.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\policysimulator.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\secretmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\secretmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigateway.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigateway.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsub.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\ondemandscanning.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\memcache.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\memcache.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playcustomapp.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\playcustomapp.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\migrationcenter.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\migrationcenter.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domains.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\domains.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\testing.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\testing.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\slides.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\slides.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gameservices.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gameservices.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\batch.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\batch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chat.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\chat.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\contactcenterinsights.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\contactcenterinsights.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\groupssettings.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\groupssettings.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\notebooks.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\notebooks.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\poly.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\poly.v1.json',
   'DATA'),
  ('google_api_python_client-2.168.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_python_client-2.168.0.dist-info\\WHEEL',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsensehost.v4.1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsensehost.v4.1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedidentities.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedidentities.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessplaceactions.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessplaceactions.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicedirectory.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicedirectory.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\groupsmigration.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\groupsmigration.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\keep.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\keep.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\forms.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\forms.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataplex.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataplex.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pollen.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\pollen.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vpcaccess.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vpcaccess.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastream.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastream.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\travelimpactmodel.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\travelimpactmodel.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\blogger.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\blogger.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticshub.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticshub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drivelabels.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\drivelabels.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\digitalassetlinks.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\digitalassetlinks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p5beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p5beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v2alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v2alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticshub.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticshub.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudshell.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudshell.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vpcaccess.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vpcaccess.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drive.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\drive.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasestorage.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasestorage.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dns.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dns.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discoveryengine.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\discoveryengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.notifications_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.notifications_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\translate.v3beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\translate.v3beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\baremetalsolution.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\safebrowsing.v4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\safebrowsing.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\beyondcorp.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\beyondcorp.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\netapp.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\netapp.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\clouddeploy.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\clouddeploy.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sqladmin.v1beta4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\sqladmin.v1beta4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\businessprofileperformance.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\businessprofileperformance.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudshell.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudshell.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drive.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\drive.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\searchconsole.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\searchconsole.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datafusion.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datafusion.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicedirectory.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicedirectory.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicemanagement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicemanagement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseml.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseml.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oslogin.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\oslogin.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\composer.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\composer.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkservices.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkservices.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dns.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dns.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkebackup.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkebackup.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domains.v1alpha2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\domains.v1alpha2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsublite.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsublite.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\youtubeAnalytics.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessqanda.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessqanda.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflowexecutions.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappdistribution.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\healthcare.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\healthcare.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v2alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sqladmin.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\sqladmin.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudfunctions.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudfunctions.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\parallelstore.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\parallelstore.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\versionhistory.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\versionhistory.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\secretmanager.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataproc.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataproc.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iam.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\iam.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\webmasters.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\webmasters.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\certificatemanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\certificatemanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\webfonts.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\webfonts.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbilling.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbilling.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastream.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastream.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playablelocations.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\playablelocations.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\domainsrdap.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\domainsrdap.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vision.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vision.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\chromeuxreport.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\chromeuxreport.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networksecurity.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\networksecurity.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\redis.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\redis.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseappcheck.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\docs.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\docs.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\homegraph.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\homegraph.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtrace.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtrace.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\speech.v1p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\speech.v1p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\eventarc.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\eventarc.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datamigration.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datamigration.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\games.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\games.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\calendar.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\calendar.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oslogin.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\oslogin.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\licensing.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\licensing.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\retail.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\retail.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1p3beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1p3beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\identitytoolkit.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\identitytoolkit.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.promotions_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.promotions_v1beta.json',
   'DATA'),
  ('google_api_python_client-2.168.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_python_client-2.168.0.dist-info\\INSTALLER',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tasks.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\tasks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\marketingplatformadmin.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\marketingplatformadmin.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigquerydatatransfer.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigquerydatatransfer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\content.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\content.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinessbusinesscalls.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinessbusinesscalls.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datapipelines.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datapipelines.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datacatalog.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datacatalog.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer.v1.4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\retail.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\retail.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\advisorynotifications.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\advisorynotifications.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\libraryagent.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\libraryagent.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\rapidmigrationassessment.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\rapidmigrationassessment.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\containeranalysis.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\containeranalysis.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\documentai.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\documentai.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\speech.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\speech.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudidentity.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudidentity.v1.json',
   'DATA'),
  ('google_api_python_client-2.168.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_python_client-2.168.0.dist-info\\LICENSE',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\file.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\file.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkeonprem.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkeonprem.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasehosting.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasehosting.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\drivelabels.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\drivelabels.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\config.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\config.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\remotebuildexecution.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\discoveryengine.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\discoveryengine.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\localservices.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\localservices.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v3p1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v3p1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicenetworking.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicenetworking.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v2alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v2alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebaseml.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebaseml.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\workflows.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\workflows.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\composer.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\composer.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datastore.v1beta3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datastore.v1beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iamcredentials.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\iamcredentials.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\genomics.v2alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\genomics.v2alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\vmmigration.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\vmmigration.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\developerconnect.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\developerconnect.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\reseller.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\reseller.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\compute.beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\compute.beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudtasks.v2beta3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\lifesciences.v2beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\lifesciences.v2beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1p7beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1p7beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\iap.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\iap.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.datatransferv1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.datatransferv1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkmanagement.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkmanagement.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\areainsights.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\areainsights.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adexchangebuyer2.v2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\adexchangebuyer2.v2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\analyticsdata.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\analyticsdata.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pubsub.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\pubsub.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\datafusion.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\datafusion.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\managedidentities.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\managedidentities.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\verifiedaccess.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\verifiedaccess.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\area120tables.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\area120tables.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataportability.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataportability.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\driveactivity.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\driveactivity.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\acceleratedmobilepageurl.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\acceleratedmobilepageurl.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\abusiveexperiencereport.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\abusiveexperiencereport.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbuild.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbuild.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\videointelligence.v1p2beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\videointelligence.v1p2beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\classroom.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\classroom.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apphub.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\apphub.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\resourcesettings.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\resourcesettings.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\memcache.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\memcache.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsenseplatform.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\securityposture.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\securityposture.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.inventories_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.inventories_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\paymentsresellersubscription.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\paymentsresellersubscription.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasehosting.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasehosting.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\readerrevenuesubscriptionlinking.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\readerrevenuesubscriptionlinking.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudresourcemanager.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\firebasedataconnect.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\sts.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\sts.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\observability.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\observability.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\adsense.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\adsense.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\verifiedaccess.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\verifiedaccess.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gamesManagement.v1management.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gamesManagement.v1management.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\searchads360.v0.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\searchads360.v0.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudsupport.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudsupport.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudscheduler.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\admin.datatransfer_v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\admin.datatransfer_v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudcommerceprocurement.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudcommerceprocurement.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\jobs.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\jobs.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\ids.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\ids.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\pagespeedonline.v5.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\pagespeedonline.v5.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\indexing.v3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\indexing.v3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\biglake.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\biglake.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\contactcenteraiplatform.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\contactcenteraiplatform.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apigeeregistry.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\apigeeregistry.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dlp.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dlp.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\tpu.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\tpu.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\osconfig.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\osconfig.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gmailpostmastertools.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dialogflow.v3beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dialogflow.v3beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\run.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\run.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\appengine.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\appengine.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\accesscontextmanager.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataproc.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataproc.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataportability.v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataportability.v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\policyanalyzer.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\connectors.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\connectors.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\essentialcontacts.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\essentialcontacts.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\serviceconsumermanagement.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\networkservices.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\networkservices.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\playdeveloperreporting.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apikeys.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\apikeys.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\artifactregistry.v1beta2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\servicecontrol.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\servicecontrol.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v4.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v4.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigtableadmin.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigtableadmin.v2.json',
   'DATA'),
  ('google_api_python_client-2.168.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_python_client-2.168.0.dist-info\\REQUESTED',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\notebooks.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\notebooks.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\deploymentmanager.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dataflow.v1b3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dataflow.v1b3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1beta1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigqueryreservation.v1beta1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\spanner.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\spanner.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudkms.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudkms.v1.json',
   'DATA'),
  ('google_api_python_client-2.168.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_python_client-2.168.0.dist-info\\top_level.txt',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\apim.v1alpha.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\apim.v1alpha.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\merchantapi.accounts_v1beta.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\merchantapi.accounts_v1beta.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\gkehub.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\gkehub.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\mybusinesslodging.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\mybusinesslodging.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudasset.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudasset.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\bigtableadmin.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\bigtableadmin.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\containeranalysis.v1alpha1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\containeranalysis.v1alpha1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\dfareporting.v3.3.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\dfareporting.v3.3.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\displayvideo.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\displayvideo.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\doubleclickbidmanager.v1.1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\oauth2.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\oauth2.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\metastore.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\metastore.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\logging.v2.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\logging.v2.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\looker.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\looker.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\kgsearch.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\kgsearch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\customsearch.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\customsearch.v1.json',
   'DATA'),
  ('googleapiclient\\discovery_cache\\documents\\cloudbilling.v1.json',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\googleapiclient\\discovery_cache\\documents\\cloudbilling.v1.json',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_core-2.24.2.dist-info\\METADATA',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_core-2.24.2.dist-info\\RECORD',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_core-2.24.2.dist-info\\top_level.txt',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_core-2.24.2.dist-info\\INSTALLER',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_core-2.24.2.dist-info\\WHEEL',
   'DATA'),
  ('google_api_core-2.24.2.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\google_api_core-2.24.2.dist-info\\LICENSE',
   'DATA'),
  ('httplib2\\cacerts.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\httplib2\\cacerts.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\wheel-0.45.1.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\wheel-0.45.1.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\wheel-0.45.1.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\wheel-0.45.1.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.45.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\wheel-0.45.1.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.45.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\wheel-0.45.1.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.45.1.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\wheel-0.45.1.dist-info\\REQUESTED',
   'DATA'),
  ('base_library.zip',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\ifess_client_hidden\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
