# IFESS Google Drive OAuth

Dokumen ini menjelaskan penggunaan OAuth untuk Google Drive di aplikasi IFESS.

## Fitur

1. **OAuth Google Drive**
   - Autentikasi dengan akun pribadi (<EMAIL>)
   - Auto-refresh token OAuth yang kedaluwarsa
   - File disimpan di folder pribadi dan dimiliki oleh akun pribadi
   - Struktur folder: Backup_PTRJ/IFESS/{client_id}/{client_id}_date_time.zip

## Cara Menggunakan

### Menjalankan Aplikasi dengan OAuth

Gunakan script `run_with_oauth.bat` untuk menjalankan aplikasi dengan dukungan OAuth:

```
run_with_oauth.bat
```

Script ini akan:
- Menginstal dependensi yang diperlukan
- Menjalankan server web, client hidden, dan client debug

### Upload ke Google Drive

1. Buka antarmuka web server di http://localhost:5000
2. Navigasi ke halaman "Backups"
3. Pilih client dari dropdown
4. <PERSON>lik tombol "Upload to GDrive" untuk file yang ingin diupload
5. Browser akan terbuka untuk autentikasi
6. <PERSON>gin dengan akun Google pribadi (<EMAIL>)
7. Berikan izin yang diminta

## Konfigurasi

### File Kredensial

Aplikasi akan mencari file kredensial dalam urutan berikut:

1. `client_secrets.json` di direktori yang sama dengan aplikasi
2. File yang ditentukan dalam `gdrive_client_config.json`
3. File di direktori `Client_Transfer_Gdrive`

### Token OAuth

Setelah autentikasi pertama, token OAuth akan disimpan di file `token.json`. Token ini akan digunakan untuk autentikasi berikutnya dan akan diperbarui secara otomatis saat kedaluwarsa.

## Troubleshooting

### Masalah "invalid_client"

Jika Anda melihat error "The OAuth client was not found" atau "Error 401: invalid_client":

1. Pastikan file `client_secrets.json` ada dan valid
2. Hapus file `token.json` jika ada
3. Jalankan aplikasi lagi dan lakukan autentikasi baru

### Masalah Token Kedaluwarsa

Jika token kedaluwarsa dan tidak dapat diperbarui secara otomatis:

1. Hapus file `token.json`
2. Jalankan aplikasi lagi
3. Browser akan terbuka untuk autentikasi baru
4. Login dengan akun Google pribadi (<EMAIL>)

## Informasi Debug

Aplikasi debug (ifess_client_debug.py) menampilkan informasi tentang status upload Google Drive. Gunakan aplikasi ini untuk memantau proses upload dan melihat pesan error jika terjadi masalah.
