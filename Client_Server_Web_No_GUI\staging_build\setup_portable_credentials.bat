@echo off
REM ===============================================
REM IFESS Portable Credentials Setup
REM Ensures all credential files are properly 
REM configured for portable operation
REM ===============================================

echo Setting up portable credentials...

REM Create backup of existing config if it exists
if exist "client_config.json" (
    if not exist "client_config_basic_backup.json" (
        copy "client_config.json" "client_config_basic_backup.json" >nul 2>&1
        echo Backed up existing client_config.json
    )
)

REM Use OAuth config as primary if it exists
if exist "client_config_oauth_tokens.json" (
    echo Using OAuth configuration as primary config...
    copy "client_config_oauth_tokens.json" "client_config.json" >nul 2>&1
    echo OAuth configuration set as primary
) else (
    echo Warning: OAuth configuration not found
)

REM Verify credential files exist
set MISSING_FILES=0

if not exist "token.json" (
    echo Warning: token.json not found
    set /a MISSING_FILES+=1
)

if not exist "ptrj-backup-services-account.json" (
    echo Warning: ptrj-backup-services-account.json not found
    set /a MISSING_FILES+=1
)

if %MISSING_FILES% equ 0 (
    echo ✓ All credential files found
    echo Portable credentials setup completed successfully
) else (
    echo ⚠ Some credential files are missing
    echo Please ensure all credential files are in the dist directory
)

echo.
echo Portable setup complete. Applications will now use local credentials.
pause 