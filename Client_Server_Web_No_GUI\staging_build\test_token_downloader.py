"""
Test Token Downloader
-------------------
This script tests the token downloader module that gets the latest token from Google Drive.
"""

import os
import sys
import json
import logging
import traceback

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("test_token_downloader")

def test_get_token_list():
    """Test getting the list of token files from Google Drive folder"""
    try:
        logger.info("Testing get_token_list_from_folder()...")
        
        # Import token_downloader
        import token_downloader
        
        # Get token list
        files = token_downloader.get_token_list_from_folder()
        
        if files:
            logger.info(f"Found {len(files)} token files in the folder")
            
            # Show the first 5 files
            for i, file in enumerate(files[:5]):
                file_name = file.get('name', '')
                file_id = file.get('id', '')
                modified_time = file.get('modifiedTime', '')
                
                # Check if file has parsed_datetime
                if 'parsed_datetime' in file:
                    parsed_datetime = file['parsed_datetime'].strftime("%Y-%m-%d %H:%M:%S")
                    logger.info(f"  {i+1}. {file_name} (ID: {file_id}, Modified: {modified_time}, Parsed: {parsed_datetime})")
                else:
                    logger.info(f"  {i+1}. {file_name} (ID: {file_id}, Modified: {modified_time})")
            
            # Show total count if more than 5 files
            if len(files) > 5:
                logger.info(f"  ... and {len(files) - 5} more files")
            
            return True
        else:
            logger.error("No token files found in the folder")
            return False
    
    except Exception as e:
        logger.error(f"Error testing get_token_list_from_folder: {e}")
        logger.error(traceback.format_exc())
        return False

def test_download_token():
    """Test downloading the latest token from Google Drive folder"""
    try:
        logger.info("Testing download_token()...")
        
        # Import token_downloader
        import token_downloader
        
        # Download token
        success = token_downloader.download_token()
        
        if success:
            logger.info("Token downloaded successfully")
            
            # Check if token file exists
            token_file = token_downloader.TOKEN_FILE
            if os.path.exists(token_file):
                logger.info(f"Token file exists: {token_file}")
                
                # Load token data
                try:
                    with open(token_file, 'r') as f:
                        token_data = json.load(f)
                    
                    # Show token info
                    logger.info(f"Token client ID: {token_data.get('client_id')}")
                    logger.info(f"Token expiry: {token_data.get('expiry')}")
                    logger.info(f"Token scopes: {', '.join(token_data.get('scopes', []))}")
                    
                    # Validate token
                    is_valid = token_downloader.validate_token(token_data)
                    logger.info(f"Token is valid: {is_valid}")
                    
                    # Check if token is expiring soon
                    is_expiring = token_downloader.is_token_expiring_soon(token_data)
                    logger.info(f"Token is expiring soon: {is_expiring}")
                    
                    return True
                except Exception as load_error:
                    logger.error(f"Error loading token data: {load_error}")
                    logger.error(traceback.format_exc())
                    return False
            else:
                logger.error(f"Token file not found: {token_file}")
                return False
        else:
            logger.error("Failed to download token")
            return False
    
    except Exception as e:
        logger.error(f"Error testing download_token: {e}")
        logger.error(traceback.format_exc())
        return False

def test_get_token():
    """Test getting token from local file or downloading it"""
    try:
        logger.info("Testing get_token()...")
        
        # Import token_downloader
        import token_downloader
        
        # Get token
        token_data = token_downloader.get_token()
        
        if token_data:
            logger.info("Token obtained successfully")
            
            # Show token info
            logger.info(f"Token client ID: {token_data.get('client_id')}")
            logger.info(f"Token expiry: {token_data.get('expiry')}")
            logger.info(f"Token scopes: {', '.join(token_data.get('scopes', []))}")
            
            return True
        else:
            logger.error("Failed to get token")
            return False
    
    except Exception as e:
        logger.error(f"Error testing get_token: {e}")
        logger.error(traceback.format_exc())
        return False

def main():
    """Main function"""
    logger.info("=== TESTING TOKEN DOWNLOADER ===")
    
    # Test getting token list
    list_success = test_get_token_list()
    logger.info(f"Test get_token_list_from_folder: {'SUCCESS' if list_success else 'FAILURE'}")
    
    # Test downloading token
    download_success = test_download_token()
    logger.info(f"Test download_token: {'SUCCESS' if download_success else 'FAILURE'}")
    
    # Test getting token
    get_success = test_get_token()
    logger.info(f"Test get_token: {'SUCCESS' if get_success else 'FAILURE'}")
    
    # Overall result
    overall_success = list_success and download_success and get_success
    logger.info(f"=== OVERALL TEST RESULT: {'SUCCESS' if overall_success else 'FAILURE'} ===")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
