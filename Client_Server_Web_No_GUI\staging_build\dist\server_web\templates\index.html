{% extends 'layout.html' %}

{% block title %}Dashboard - Firebird Query Server{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">Dashboard</h1>
    </div>
</div>

<div class="row">
    <!-- Server Status Card -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>Server Status
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="fw-bold">Status:</span>
                    <span class="badge {% if server_status == 'Running' %}bg-success{% else %}bg-danger{% endif %} fs-6">
                        {{ server_status }}
                    </span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="fw-bold">Connected Clients:</span>
                    <span class="badge bg-info fs-6">{{ client_count }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-bold">Queries Executed:</span>
                    <span class="badge bg-secondary fs-6">{{ query_count }}</span>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-grid gap-2">
                    <button id="dashboard-start-server" class="btn btn-success {% if server_status == 'Running' %}d-none{% endif %}">
                        <i class="fas fa-play me-1"></i>Start Server
                    </button>
                    <button id="dashboard-stop-server" class="btn btn-danger {% if server_status != 'Running' %}d-none{% endif %}">
                        <i class="fas fa-stop me-1"></i>Stop Server
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Card -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="{{ url_for('query_page') }}" class="btn btn-outline-primary">
                        <i class="fas fa-code me-2"></i>Execute Query
                    </a>
                    <a href="{{ url_for('monitoring') }}" class="btn btn-outline-success">
                        <i class="fas fa-chart-line me-2"></i>Run Monitoring
                    </a>
                    <a href="{{ url_for('client_list') }}" class="btn btn-outline-info">
                        <i class="fas fa-laptop me-2"></i>Manage Clients
                    </a>
                    <a href="{{ url_for('query_history_page') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-history me-2"></i>View Query History
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Info Card -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>System Information
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="fw-bold">Server Port:</span>
                    <span>5555</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="fw-bold">Web Interface Port:</span>
                    <span>5000</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span class="fw-bold">Max Result Rows:</span>
                    <span>10,000</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span class="fw-bold">Server Uptime:</span>
                    <span id="server-uptime">-</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Connected Clients Table -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-laptop me-2"></i>Connected Clients
                </h5>
            </div>
            <div class="card-body">
                {% if clients %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Client Name</th>
                                <th>IP Address</th>
                                <th>Connected Since</th>
                                <th>Database</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in clients %}
                            <tr>
                                <td>{{ client.name }}</td>
                                <td>{{ client.address }}</td>
                                <td>{{ client.connected_since }}</td>
                                <td>
                                    {% if client.db_info and client.db_info.path %}
                                    {{ client.db_info.path }}
                                    {% else %}
                                    <span class="text-muted">No database info</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('query_page') }}?client={{ client.id }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-code"></i> Query
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No clients connected. Start the server and wait for clients to connect.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Dashboard start server button
        $('#dashboard-start-server').click(function() {
            $.post('/api/server/start', function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to start server');
                }
            });
        });

        // Dashboard stop server button
        $('#dashboard-stop-server').click(function() {
            $.post('/api/server/stop', function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to stop server');
                }
            });
        });

        // Update server uptime
        function updateUptime() {
            if ('{{ server_status }}' === 'Running') {
                $.get('/api/server/uptime', function(data) {
                    $('#server-uptime').text(data.uptime);
                });
            }
        }

        // Update uptime every 10 seconds
        updateUptime();
        setInterval(updateUptime, 10000);
    });
</script>
{% endblock %}
