#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Google Drive Subfolder Handler for IFESS
----------------------------------------
This module handles Google Drive uploads with subfolder support.
It creates a folder structure in the format:
root/{main_folder}/{subfolder}/{file}
"""

import os
import sys
import time
import json
import logging
import traceback
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('GDriveSubfolderHandler')

# Try to import Google Drive API
try:
    from google.oauth2 import service_account
    from googleapiclient.discovery import build
    from googleapiclient.http import MediaFileUpload
    GDRIVE_API_AVAILABLE = True
except ImportError:
    logger.warning("Google Drive API not available. Install required packages.")
    GDRIVE_API_AVAILABLE = False

# Shared folder ID - this is the folder that was shared with the service account
# This is the ID from the URL: https://drive.google.com/drive/folders/1HuSxKhmXRLuZxiYpgcSfVIb2i4YTUhpA
SHARED_FOLDER_ID = "1HuSxKhmXRLuZxiYpgcSfVIb2i4YTUhpA"

class GDriveSubfolderHandler:
    """Google Drive handler with subfolder support"""
    def __init__(self, credentials_file):
        """Initialize Google Drive handler

        Args:
            credentials_file: Path to service account JSON file or OAuth credentials
        """
        self.credentials_file = credentials_file
        self.credentials = None
        self.service = None

        # Check if Google Drive API is available
        if not GDRIVE_API_AVAILABLE:
            logger.error("Google Drive API not available")
            return

        # Initialize Google Drive service
        if os.path.exists(self.credentials_file):
            try:
                self._load_credentials()
                logger.info(f"Google Drive handler initialized with credentials: {self.credentials_file}")
            except Exception as e:
                logger.error(f"Error initializing Google Drive handler: {e}")
                logger.error(traceback.format_exc())
        else:
            logger.warning(f"Credentials file not found: {self.credentials_file}")

    def _load_credentials(self):
        """Load credentials from file"""
        try:
            # Check if this is a service account file
            if self._is_service_account_file(self.credentials_file):
                logger.info(f"Loading service account credentials from: {self.credentials_file}")
                self.credentials = service_account.Credentials.from_service_account_file(
                    self.credentials_file,
                    scopes=['https://www.googleapis.com/auth/drive']
                )
                logger.info("Service account credentials loaded successfully")
            else:
                logger.error("Only service account credentials are supported")
                return

            # Build the service
            logger.info("Building Google Drive API service...")
            self.service = build('drive', 'v3', credentials=self.credentials)
            logger.info("Google Drive API service created successfully")

        except Exception as e:
            logger.error(f"Error loading credentials: {e}")
            logger.error(traceback.format_exc())
            raise

    def _is_service_account_file(self, file_path):
        """Check if the file is a service account JSON file"""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                return 'type' in data and data['type'] == 'service_account'
        except Exception:
            return False

    def _find_folder(self, folder_name, parent_id=None):
        """Find a folder by name and parent ID

        Args:
            folder_name: Name of the folder to find
            parent_id: ID of the parent folder (None for root)

        Returns:
            str: Folder ID or None if not found
        """
        if not self.service:
            logger.error("Google Drive service not initialized")
            return None

        try:
            # Build the query
            query = f"name='{folder_name}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
            if parent_id:
                query += f" and '{parent_id}' in parents"

            logger.info(f"Searching for folder '{folder_name}'" + (f" in parent: {parent_id}" if parent_id else ""))
            logger.info(f"Query: {query}")

            # Execute the query
            results = self.service.files().list(
                q=query,
                spaces='drive',
                fields='files(id, name, parents)'
            ).execute()

            items = results.get('files', [])
            logger.info(f"Found {len(items)} matching folders")

            # Return the first matching folder
            if items:
                folder_id = items[0]['id']
                parents = items[0].get('parents', [])
                parent_str = f" in {parents}" if parents else ""
                logger.info(f"Found folder: {folder_name} (ID: {folder_id}){parent_str}")
                return folder_id
            else:
                logger.info(f"Folder '{folder_name}' not found")
                return None

        except Exception as e:
            logger.error(f"Error finding folder {folder_name}: {e}")
            logger.error(traceback.format_exc())
            return None

    def _create_folder(self, folder_name, parent_id=None):
        """Create a folder

        Args:
            folder_name: Name of the folder to create
            parent_id: ID of the parent folder (None for root)

        Returns:
            str: Folder ID or None if creation failed
        """
        if not self.service:
            logger.error("Google Drive service not initialized")
            return None

        try:
            logger.info(f"Creating folder '{folder_name}'" + (f" in parent: {parent_id}" if parent_id else ""))

            # Create folder metadata
            folder_metadata = {
                'name': folder_name,
                'mimeType': 'application/vnd.google-apps.folder'
            }

            if parent_id:
                folder_metadata['parents'] = [parent_id]

            # Create the folder
            folder = self.service.files().create(
                body=folder_metadata,
                fields='id,name,parents'
            ).execute()

            folder_id = folder.get('id')
            parents = folder.get('parents', [])
            parent_str = f" in {parents}" if parents else ""
            logger.info(f"Created folder: {folder_name} (ID: {folder_id}){parent_str}")
            return folder_id

        except Exception as e:
            logger.error(f"Error creating folder {folder_name}: {e}")
            logger.error(traceback.format_exc())
            return None

    def _find_or_create_folder(self, folder_name, parent_id=None):
        """Find a folder or create it if it doesn't exist

        Args:
            folder_name: Name of the folder to find or create
            parent_id: ID of the parent folder (None for root)

        Returns:
            str: Folder ID or None if not found and creation failed
        """
        # First try to find the folder
        folder_id = self._find_folder(folder_name, parent_id)

        # If not found, create it
        if not folder_id:
            folder_id = self._create_folder(folder_name, parent_id)

        return folder_id

    def _create_folder_structure(self, main_folder, subfolder=None):
        """Create folder structure: {main_folder}/{subfolder}/

        Args:
            main_folder: Name of the main folder (ignored if shared folder is accessible)
            subfolder: Name of the subfolder (optional, can be nested path like "IFESS/client_id")

        Returns:
            str: ID of the subfolder or main folder if subfolder is None
        """
        if not self.service:
            logger.error("Google Drive service not initialized")
            return None

        try:
            # First, try to access the shared folder directly
            logger.info(f"Trying to access shared folder with ID: {SHARED_FOLDER_ID}")
            try:
                # Try to get the folder metadata
                shared_folder = self.service.files().get(
                    fileId=SHARED_FOLDER_ID,
                    fields='id,name'
                ).execute()

                logger.info(f"Successfully accessed shared folder: {shared_folder.get('name')} (ID: {shared_folder.get('id')})")

                # Use the shared folder directly as the base folder
                # This avoids creating a redundant "Backup_PTRJ" folder inside the "Backup_PTRJ" shared folder
                base_folder_id = SHARED_FOLDER_ID
                logger.info(f"Using shared folder directly as the base folder")
            except Exception as e:
                logger.warning(f"Could not access shared folder directly: {e}")
                logger.warning("Will try to find or create main folder in root instead")

                # If we can't access the shared folder, create/find the main folder
                logger.info(f"Finding or creating main folder: {main_folder}")
                base_folder_id = self._find_or_create_folder(main_folder, None)

                if not base_folder_id:
                    logger.error(f"Failed to find or create main folder: {main_folder}")
                    return None

            # If no subfolder, return base folder ID
            if not subfolder:
                return base_folder_id

            # Handle nested subfolders (e.g., "IFESS/client_id")
            current_parent_id = base_folder_id
            subfolder_parts = subfolder.split('/')

            for i, folder_name in enumerate(subfolder_parts):
                logger.info(f"Finding or creating subfolder [{i+1}/{len(subfolder_parts)}]: {folder_name}")
                folder_id = self._find_or_create_folder(folder_name, current_parent_id)

                if not folder_id:
                    logger.error(f"Failed to find or create subfolder: {folder_name}")
                    return None

                current_parent_id = folder_id
                logger.info(f"Created/found subfolder: {folder_name} (ID: {folder_id})")

            # Return the ID of the last subfolder in the chain
            return current_parent_id

        except Exception as e:
            logger.error(f"Error creating folder structure: {e}")
            logger.error(traceback.format_exc())
            return None

    def _get_folder_owner(self, folder_id):
        """Get the owner of a folder

        Args:
            folder_id: ID of the folder

        Returns:
            str: Email of the owner or None if not found
        """
        if not self.service:
            logger.error("Google Drive service not initialized")
            return None

        try:
            # Get folder metadata including owners
            folder = self.service.files().get(
                fileId=folder_id,
                fields='owners'
            ).execute()

            owners = folder.get('owners', [])
            if owners:
                owner_email = owners[0].get('emailAddress')
                logger.info(f"Folder owner: {owner_email}")
                return owner_email
            else:
                logger.warning(f"No owner found for folder {folder_id}")
                return None

        except Exception as e:
            logger.error(f"Error getting folder owner: {e}")
            logger.error(traceback.format_exc())
            return None

    def upload_file(self, file_path, main_folder, subfolder=None, progress_callback=None):
        """Upload file to Google Drive

        Args:
            file_path: Path to the file to upload
            main_folder: Name of the main folder
            subfolder: Name of the subfolder (optional)
            progress_callback: Callback function for progress updates (optional)

        Returns:
            dict: Upload result
        """
        if not self.service:
            logger.error("Google Drive service not initialized")
            return {'success': False, 'error': "Google Drive service not initialized"}

        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return {'success': False, 'error': f"File not found: {file_path}"}

        try:
            logger.info("===== STARTING UPLOAD TO GOOGLE DRIVE =====")
            logger.info(f"File path: {file_path}")

            # Get file size
            file_size = os.path.getsize(file_path)
            file_size_mb = file_size / (1024 * 1024)
            logger.info(f"File size: {file_size} bytes ({file_size_mb:.2f} MB)")

            # Create folder structure
            parent_folder_id = self._create_folder_structure(main_folder, subfolder)

            if not parent_folder_id:
                return {'success': False, 'error': "Failed to create folder structure"}

            # Try to get the owner of the shared folder
            logger.info("Trying to get the owner of the shared folder...")
            shared_folder_owner = self._get_folder_owner(SHARED_FOLDER_ID)

            # Prepare file metadata
            file_name = os.path.basename(file_path)
            file_metadata = {
                'name': file_name,
                'parents': [parent_folder_id]
            }

            logger.info(f"File metadata: {file_metadata}")

            # Prepare media upload
            logger.info("Preparing media upload...")
            media = MediaFileUpload(
                file_path,
                resumable=True,
                chunksize=1024*1024  # 1MB chunks
            )

            # Create upload request
            logger.info("Creating upload request...")
            request = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,name,webViewLink'
            )

            # Upload file with progress reporting
            logger.info("Starting upload...")
            start_time = time.time()
            response = None
            last_progress = 0

            while response is None:
                status, response = request.next_chunk()
                if status:
                    progress = int(status.progress() * 100)
                    bytes_uploaded = int(file_size * status.progress())

                    # Only report progress if it has changed significantly
                    if progress - last_progress >= 1:
                        logger.info(f"Uploaded {bytes_uploaded} bytes ({progress}%)")
                        last_progress = progress

                        if progress_callback:
                            progress_callback(bytes_uploaded, progress)

            # Calculate upload duration
            end_time = time.time()
            duration = end_time - start_time
            logger.info(f"Upload complete, response: {response}")
            logger.info(f"Upload duration: {duration:.2f} seconds")

            file_id = response.get('id')

            # If we found the shared folder owner, make them an editor first, then try to transfer ownership
            if shared_folder_owner:
                try:
                    # First, add the user as an editor (this doesn't require consent)
                    logger.info(f"Adding {shared_folder_owner} as an editor...")
                    self.service.permissions().create(
                        fileId=file_id,
                        body={
                            'type': 'user',
                            'role': 'writer',
                            'emailAddress': shared_folder_owner
                        },
                        sendNotificationEmail=False
                    ).execute()
                    logger.info(f"Added {shared_folder_owner} as an editor successfully")

                    # Then try to transfer ownership
                    try:
                        logger.info(f"Attempting to transfer ownership to {shared_folder_owner}...")
                        self.service.permissions().create(
                            fileId=file_id,
                            body={
                                'type': 'user',
                                'role': 'owner',
                                'emailAddress': shared_folder_owner
                            },
                            transferOwnership=True,
                            sendNotificationEmail=True,
                            emailMessage="IFESS Backup System is transferring ownership of this file to you to save space in the service account."
                        ).execute()
                        logger.info(f"Ownership transferred successfully to {shared_folder_owner}")
                    except Exception as e:
                        if "consentRequiredForOwnershipTransfer" in str(e):
                            logger.warning(f"Ownership transfer requires consent. A notification has been sent to {shared_folder_owner}.")
                            logger.warning("Please check your email and accept the ownership transfer request.")
                            logger.warning("After accepting once, future transfers should work automatically.")
                        else:
                            logger.warning(f"Could not transfer ownership: {e}")
                        # Continue even if ownership transfer fails
                except Exception as e:
                    logger.warning(f"Could not add user as editor: {e}")
                    # Continue even if adding as editor fails
            else:
                logger.warning("Could not determine shared folder owner, file will remain owned by service account")

            # Set file permissions (make it accessible to anyone with the link)
            logger.info("Setting file permissions...")
            self.service.permissions().create(
                fileId=file_id,
                body={
                    'type': 'anyone',
                    'role': 'reader'
                }
            ).execute()

            # Get sharing link
            logger.info("Getting sharing link...")
            web_view_link = response.get('webViewLink')
            web_content_link = f"https://drive.google.com/uc?id={file_id}&export=download"

            logger.info("===== UPLOAD SUCCESSFUL =====")
            logger.info(f"File: {file_name}")
            logger.info(f"File ID: {file_id}")
            logger.info(f"View Link: {web_view_link}")
            logger.info(f"Download Link: {web_content_link}")
            logger.info(f"Duration: {duration:.2f} seconds")

            return {
                'success': True,
                'file_id': file_id,
                'name': file_name,
                'web_view_link': web_view_link,
                'web_content_link': web_content_link,
                'upload_duration': duration
            }

        except Exception as e:
            logger.error(f"Error uploading file: {e}")
            logger.error(traceback.format_exc())
            return {'success': False, 'error': str(e)}

# For testing
if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Upload a file to Google Drive with subfolder support")
    parser.add_argument("file_path", help="Path to the file to upload")
    parser.add_argument("--main-folder", default="Backup_PTRJ", help="Name of the main folder")
    parser.add_argument("--subfolder", default="IFESS", help="Name of the subfolder")
    parser.add_argument("--credentials", default="ptrj-backup-services-account.json", help="Path to credentials file")

    args = parser.parse_args()

    # Create handler
    handler = GDriveSubfolderHandler(args.credentials)

    # Define progress callback
    def progress_callback(bytes_uploaded, progress):
        print(f"Upload progress: {progress}%")

    # Upload file
    result = handler.upload_file(args.file_path, args.main_folder, args.subfolder, progress_callback)

    # Print result
    print(f"Upload result: {result}")
