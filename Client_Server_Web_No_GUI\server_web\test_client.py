import os
import sys
import time
import datetime
import base64
import json
import socket
import struct

# Add parent directory to path to import common modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from common.network import NetworkMessage, send_message, receive_message

# Create a socket connection to the server
def connect_to_server(host='localhost', port=5555):
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.connect((host, port))
    print(f"Connected to server at {host}:{port}")
    return sock

# Register with the server
def register_to_server(sock, client_id='test_client', display_name='Test Client'):
    registration = {
        'client_id': client_id,
        'display_name': display_name,
        'platform': 'Windows',
        'python_version': '3.13.3',
        'database': {
            'path': 'D:/test_db.fdb'
        }
    }
    message = NetworkMessage(
        msg_type='register',
        data=registration
    )
    send_message(sock, message)
    print(f"Registered to server as {display_name} ({client_id})")

# Create a test database backup
def create_test_backup(sock, client_id='test_client'):
    # Create backup directories
    backup_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'backups')
    client_dir = os.path.join(backup_dir, client_id)

    # Create directories if they don't exist
    os.makedirs(client_dir, exist_ok=True)
    print(f"Created backup directory: {client_dir}")

    # Create a test database file
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    test_db_filename = f"test_db_{timestamp}.fdb"
    test_db_path = os.path.join(client_dir, test_db_filename)

    # Create a test database file with some content
    with open(test_db_path, 'wb') as f:
        # Create a 1MB test file
        f.write(b'TESTDATABASE' * 100000)

    print(f"Created test database file: {test_db_path}")
    print(f"File size: {os.path.getsize(test_db_path)} bytes")

    # Send database info to server
    info_data = {
        'filename': test_db_filename,
        'size': os.path.getsize(test_db_path),
        'db_info': {
            'path': test_db_path,
            'timestamp': datetime.datetime.now().isoformat()
        },
        'client_id': client_id,
        'timestamp': time.time()
    }
    info_message = NetworkMessage(
        msg_type='db_info',
        data=info_data,
        client_id=client_id
    )

    # Try sending the info message multiple times
    max_retries = 3
    for retry in range(max_retries):
        try:
            print(f"Sending database info to server (attempt {retry+1}/{max_retries})")
            if send_message(sock, info_message):
                print(f"Sent database info to server: {test_db_filename}")
                break
            else:
                print(f"Failed to send database info to server (attempt {retry+1}/{max_retries})")
                if retry < max_retries - 1:
                    time.sleep(0.5)  # Short delay before retry
        except Exception as e:
            print(f"Error sending database info: {e}")
            if retry < max_retries - 1:
                time.sleep(0.5)  # Short delay before retry
            else:
                print("Failed to send database info after multiple attempts")
                return

    # Wait for acknowledgment with timeout
    print("Waiting for server acknowledgment...")
    ack_received = False
    max_wait_time = 10  # seconds
    start_time = time.time()

    while time.time() - start_time < max_wait_time:
        try:
            # Set a short timeout for the socket to allow checking the elapsed time
            sock.settimeout(1.0)
            message = receive_message(sock)
            if message and message.type == 'db_ack':
                print(f"Received acknowledgment from server: {message.data}")
                ack_received = True
                break
            elif message:
                print(f"Received unexpected message type: {message.type}")
        except socket.timeout:
            print(f"Waiting for acknowledgment... ({int(time.time() - start_time)}/{max_wait_time} seconds)")
        except Exception as e:
            print(f"Error receiving acknowledgment: {e}")
            break

    # Reset socket timeout
    sock.settimeout(None)

    if not ack_received:
        print("Did not receive acknowledgment from server within timeout period")
        return

    # Send database chunks (simulated)
    print("Sending database chunks (simulated)...")
    time.sleep(1)  # Simulate sending chunks

    # Send completion message
    complete_data = {
        'file_path': test_db_path,
        'filename': test_db_filename,
        'file_size': os.path.getsize(test_db_path),
        'chunks_sent': 1,
        'total_chunks': 1,
        'bytes_sent': os.path.getsize(test_db_path),
        'elapsed_time': 1.0,
        'average_speed': os.path.getsize(test_db_path) / 1.0,
        'average_speed_kb': (os.path.getsize(test_db_path) / 1.0) / 1024,
        'timestamp': time.time(),
        'client_id': client_id,
        'status': 'complete',
        'failed_chunks': 0
    }
    complete_message = NetworkMessage(
        msg_type='db_complete',
        data=complete_data,
        client_id=client_id
    )

    # Try sending the completion message multiple times
    for retry in range(max_retries):
        try:
            print(f"Sending completion message to server (attempt {retry+1}/{max_retries})")
            if send_message(sock, complete_message):
                print(f"Sent completion message to server")
                break
            else:
                print(f"Failed to send completion message to server (attempt {retry+1}/{max_retries})")
                if retry < max_retries - 1:
                    time.sleep(0.5)  # Short delay before retry
        except Exception as e:
            print(f"Error sending completion message: {e}")
            if retry < max_retries - 1:
                time.sleep(0.5)  # Short delay before retry
            else:
                print("Failed to send completion message after multiple attempts")
                return

    # Print the download URL
    download_url = f"/api/backup/download/{client_id}/{test_db_filename}"
    print(f"Database backup available for download at: {download_url}")
    print(f"Full URL: http://localhost:5000{download_url}")

# Main function
def main():
    try:
        # Connect to server
        sock = connect_to_server()

        # Register with server
        client_id = 'test_client'
        display_name = 'Test Client'
        register_to_server(sock, client_id, display_name)

        # Wait for server to process registration
        time.sleep(1)

        # Create a test backup
        create_test_backup(sock, client_id)

        # Keep the connection alive
        print("Keeping connection alive for 30 seconds...")
        for i in range(30):
            time.sleep(1)
            print(f"Waiting... {i+1}/30")

        # Close the connection
        sock.close()
        print("Connection closed")

    except Exception as e:
        print(f"Error: {e}")
        import traceback
        print(traceback.format_exc())

if __name__ == "__main__":
    main()
