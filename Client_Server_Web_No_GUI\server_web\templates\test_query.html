<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Query</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Test Query Results</h1>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">Query Results</h5>
                    </div>
                    <div class="card-body">
                        {% if results %}
                            {% for result in results %}
                                <h4>Client: {{ result.client_name }}</h4>
                                
                                {% if result.error %}
                                    <div class="alert alert-danger">
                                        Error: {{ result.error }}
                                    </div>
                                {% elif result.rows and result.rows|length > 0 %}
                                    <div class="table-responsive">
                                        <table class="table table-striped table-bordered">
                                            <thead>
                                                <tr>
                                                    {% for header in result.headers %}
                                                        <th>{{ header }}</th>
                                                    {% endfor %}
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for row in result.rows %}
                                                    <tr>
                                                        {% for header in result.headers %}
                                                            <td>{{ row[header] }}</td>
                                                        {% endfor %}
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    <p>Showing {{ result.rows|length }} rows</p>
                                {% else %}
                                    <div class="alert alert-info">
                                        No rows returned for this query.
                                    </div>
                                {% endif %}
                                
                                <hr>
                            {% endfor %}
                        {% else %}
                            <div class="alert alert-info">
                                <p>No results available. This could mean:</p>
                                <ul>
                                    <li>No clients are connected</li>
                                    <li>The query hasn't been executed yet</li>
                                    <li>The query didn't return any results</li>
                                </ul>
                            </div>
                        {% endif %}
                        
                        <div class="mt-4">
                            <a href="/test_query?query=simple_test" class="btn btn-primary me-2">Run Simple Test Query</a>
                            <a href="/test_query?query=monitor_diff_job_field" class="btn btn-success">Run Monitoring Query</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
