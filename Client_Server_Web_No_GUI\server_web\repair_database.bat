@echo off
echo Firebird Database Repair Tool
echo ===========================
echo.

if "%~1"=="" (
    echo Usage: repair_database.bat [database_file_path]
    echo.
    echo Example: repair_database.bat "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups\import_76b3e573\database_20250423_105816.fdb"
    echo.
    
    set /p DB_PATH=Enter the path to the database file to repair: 
) else (
    set DB_PATH=%~1
)

echo.
echo Starting repair of database: %DB_PATH%
echo.

python repair_firebird_db.py "%DB_PATH%"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Repair completed successfully!
) else (
    echo.
    echo Repair failed. Check the log file for details.
)

echo.
pause
