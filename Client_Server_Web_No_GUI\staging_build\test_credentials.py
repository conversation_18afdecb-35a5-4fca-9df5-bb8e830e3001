#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to verify credential loading in compiled applications
"""

import os
import sys
import json

# Determine base directory - whether running as script or frozen executable
if getattr(sys, 'frozen', False):
    # Running in a bundled executable
    BASE_DIR = os.path.dirname(sys.executable)
else:
    # Running as a script
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))

print(f"Base directory: {BASE_DIR}")
print(f"Current working directory: {os.getcwd()}")

# Define config files
CONFIG_FILE = os.path.join(BASE_DIR, "client_config.json")
OAUTH_CONFIG_FILE = os.path.join(BASE_DIR, "client_config_oauth_tokens.json")

print("\n=== Configuration Files ===")
print(f"CONFIG_FILE: {CONFIG_FILE}")
print(f"CONFIG_FILE exists: {os.path.exists(CONFIG_FILE)}")
print(f"OAUTH_CONFIG_FILE: {OAUTH_CONFIG_FILE}")
print(f"OAUTH_CONFIG_FILE exists: {os.path.exists(OAUTH_CONFIG_FILE)}")

# Test configuration loading (same logic as in the apps)
def get_config():
    """Get configuration from file, prioritizing OAuth config"""
    # First try to load OAuth config (contains Google Drive credentials)
    if os.path.exists(OAUTH_CONFIG_FILE):
        try:
            with open(OAUTH_CONFIG_FILE, 'r') as f:
                config = json.load(f)
                print(f"✓ Configuration loaded from OAuth config: {OAUTH_CONFIG_FILE}")
                return config, OAUTH_CONFIG_FILE
        except json.JSONDecodeError as e:
            print(f"✗ Error parsing OAuth config file: {e}")
            
    # Fall back to regular config file
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r') as f:
                config = json.load(f)
                print(f"✓ Configuration loaded from basic config: {CONFIG_FILE}")
                return config, CONFIG_FILE
        except json.JSONDecodeError as e:
            print(f"✗ Error parsing config file: {e}")
    
    print("✗ No configuration file found")
    return None, None

# Load configuration
print("\n=== Configuration Loading Test ===")
config, config_source = get_config()

if config:
    print(f"✓ Configuration loaded successfully from: {config_source}")
    
    # Check Google Drive configuration
    gdrive_config = config.get('gdrive', {})
    if gdrive_config:
        print("✓ Google Drive configuration found")
        
        # Check token file
        token_file = gdrive_config.get('token_file', 'token.json')
        if not os.path.isabs(token_file):
            token_file = os.path.join(BASE_DIR, token_file)
        
        print(f"Token file path: {token_file}")
        print(f"Token file exists: {os.path.exists(token_file)}")
        
        if os.path.exists(token_file):
            try:
                with open(token_file, 'r') as f:
                    token_data = json.load(f)
                print("✓ Token file loaded successfully")
                print(f"Token type: {token_data.get('type', 'unknown')}")
                if 'client_id' in token_data:
                    print(f"Client ID: {token_data['client_id'][:20]}...")
            except Exception as e:
                print(f"✗ Error loading token file: {e}")
        else:
            print("✗ Token file not found")
    else:
        print("✗ No Google Drive configuration found")
else:
    print("✗ Failed to load configuration")

# Check other credential files
print("\n=== Credential Files Check ===")
credential_files = [
    "token.json",
    "token_backup.json", 
    "ptrj-backup-services-account.json",
    "client_secret.json",
    "client_secrets.json"
]

for filename in credential_files:
    filepath = os.path.join(BASE_DIR, filename)
    exists = os.path.exists(filepath)
    status = "✓" if exists else "✗"
    print(f"{status} {filename}: {exists}")
    if exists:
        try:
            size = os.path.getsize(filepath)
            print(f"    Size: {size} bytes")
        except:
            pass

print("\n=== Summary ===")
if config and config.get('gdrive'):
    token_file = config['gdrive'].get('token_file', 'token.json')
    if not os.path.isabs(token_file):
        token_file = os.path.join(BASE_DIR, token_file)
    
    if os.path.exists(token_file):
        print("✓ CREDENTIAL TEST PASSED: Applications should be able to authenticate with Google Drive")
    else:
        print("✗ CREDENTIAL TEST FAILED: Token file not found")
else:
    print("✗ CREDENTIAL TEST FAILED: No Google Drive configuration found")

print("\nTest completed.") 