"""
Patch for MEGA.py library to fix asyncio.coroutine deprecation in Python 3.10+
"""

import sys
import asyncio
import functools
import types
import warnings

# Check if we need to apply the patch
if not hasattr(asyncio, 'coroutine'):
    print("Applying asyncio.coroutine patch for Python 3.10+")
    
    # Create a replacement for asyncio.coroutine
    def coroutine(func):
        """
        Simple replacement for asyncio.coroutine decorator.
        This is a simplified version that just marks the function as a coroutine.
        """
        if isinstance(func, types.GeneratorType) or asyncio.iscoroutinefunction(func):
            return func
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            res = func(*args, **kwargs)
            if isinstance(res, types.GeneratorType):
                return res
            else:
                # If it's not a generator, just return the result
                return res
        
        wrapper._is_coroutine = asyncio.coroutines._is_coroutine
        return wrapper
    
    # Add the coroutine function to asyncio module
    asyncio.coroutine = coroutine
    
    print("Patch applied successfully")
else:
    print("asyncio.coroutine exists, no patch needed")
