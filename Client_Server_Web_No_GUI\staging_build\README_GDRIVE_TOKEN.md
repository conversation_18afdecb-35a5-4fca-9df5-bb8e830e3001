# IFESS Google Drive Token

Dokumen ini menjelaskan penggunaan token Google Drive yang disimpan di Google Drive untuk aplikasi IFESS.

## Fitur

1. **Token dari Google Drive**
   - Mengunduh token OAuth dari Google Drive
   - Tidak perlu melakukan autentikasi manual di setiap komputer
   - Auto-refresh token yang kedaluwarsa
   - Bekerja di komputer jarak jauh tanpa interaksi pengguna

## Cara Menggunakan

### Menjalankan Aplikasi dengan Token dari Google Drive

Gunakan script `run_with_gdrive_token.bat` untuk menjalankan aplikasi dengan token dari Google Drive:

```
run_with_gdrive_token.bat
```

Script ini akan:
- Menginstal dependensi yang diperlukan
- Mengunduh token dari Google Drive
- Menjalankan server web, client hidden, dan client debug

### Upload ke Google Drive

1. Buka antarmuka web server di http://localhost:5000
2. Navigasi ke halaman "Backups"
3. Pilih client dari dropdown
4. Klik tombol "Upload to GDrive" untuk file yang ingin diupload
5. Upload akan langsung dimulai tanpa perlu autentikasi manual

## Konfigurasi

### Token Google Drive

Token OAuth disimpan di Google Drive dan dapat diakses melalui link:
https://drive.google.com/file/d/1q2fJ-ORqJj2TQwdTKQgXG6RqJ7WqENAh/view?usp=sharing

Jika token perlu diperbarui:
1. Jalankan aplikasi di komputer dengan akses internet
2. Biarkan aplikasi melakukan refresh token
3. Salin file `token.json` yang diperbarui
4. Upload ke Google Drive dengan ID yang sama

### File Kredensial

Aplikasi akan mencari file kredensial dalam urutan berikut:
1. `client_secrets.json` di direktori yang sama dengan aplikasi
2. `client_secret.json` di direktori yang sama dengan aplikasi
3. File yang ditentukan dalam `gdrive_client_config.json`

## Troubleshooting

### Masalah Mengunduh Token

Jika token tidak dapat diunduh dari Google Drive:
1. Pastikan komputer memiliki akses internet
2. Periksa apakah link Google Drive masih valid
3. Aplikasi akan mencoba menggunakan token lokal jika ada
4. Jika tidak ada token lokal, aplikasi akan mencoba OAuth flow (memerlukan interaksi pengguna)

### Masalah Token Kedaluwarsa

Jika token kedaluwarsa dan tidak dapat diperbarui secara otomatis:
1. Jalankan aplikasi di komputer dengan akses internet dan browser
2. Biarkan aplikasi melakukan OAuth flow untuk mendapatkan token baru
3. Salin file `token.json` yang baru
4. Upload ke Google Drive dengan ID yang sama

## Informasi Debug

Aplikasi debug (ifess_client_debug.py) menampilkan informasi tentang status token dan upload Google Drive. Gunakan aplikasi ini untuk memantau proses dan melihat pesan error jika terjadi masalah.
