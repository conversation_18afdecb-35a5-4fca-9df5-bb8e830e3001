#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IFESS Client Debug Application

This application provides a GUI for viewing logs and status of the hidden client.
It does not affect the hidden client if closed.
"""

import os
import sys
import json
import socket
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import logging
import traceback
import subprocess
import ctypes
import re

# Constants
CONFIG_FILE = "client_config.json"
LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.log")
MUTEX_NAME = "Global\\IFESS_Hidden_Client_Running"
REFRESH_INTERVAL = 1000  # milliseconds

# Try to find log file in multiple locations
def find_log_file():
    """Find log file in multiple locations"""
    possible_paths = [
        # Same directory as debug app
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.log"),
        # Current directory
        "ifess_client_hidden.log",
        # Parent directory
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.log"),
        # dist directory
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_client_hidden.log"),
    ]

    # Find the first path that exists
    for path in possible_paths:
        if os.path.exists(path):
            return path

    # If not found, return the default path
    return LOG_FILE

# Parse command line arguments
verbose_mode = False
for arg in sys.argv[1:]:
    if arg == '--verbose':
        verbose_mode = True

# Setup logging
log_level = logging.DEBUG if verbose_mode else logging.INFO
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ifess_debug.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("IFESS-Debug")

# Log startup information
logger.info("===== IFESS DEBUG CLIENT STARTING =====")
logger.info(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
logger.info(f"Python version: {sys.version}")
logger.info(f"Running from: {os.path.abspath(__file__)}")
logger.info(f"Current directory: {os.getcwd()}")
logger.info(f"Log file: {os.path.abspath('ifess_debug.log')}")
logger.info(f"Verbose mode: {verbose_mode}")

class DebugApp:
    """Debug application for IFESS hidden client"""
    def __init__(self, root):
        self.root = root
        self.root.title("IFESS Client Debug" + (" (Verbose Mode)" if verbose_mode else ""))
        self.root.geometry("900x700")
        self.root.minsize(700, 500)

        # Set icon if available
        try:
            self.root.iconbitmap("MAINICON.ico")
        except Exception as e:
            if verbose_mode:
                logger.debug(f"Could not load icon: {e}")

        # Variables
        self.client_running = False
        self.log_position = 0
        self.auto_scroll = tk.BooleanVar(value=True)
        self.auto_refresh = tk.BooleanVar(value=True)
        self.backup_status = "Unknown"
        self.backup_progress = 0
        self.backup_details = {}
        self.last_backup_time = None
        self.connection_status = "Unknown"
        self.last_connection_drop = None
        self.reconnection_attempts = 0
        self.verbose_mode = verbose_mode

        # Log initialization
        logger.info("Initializing debug application")
        if verbose_mode:
            logger.debug("Verbose mode enabled, additional debug information will be shown")
            logger.debug(f"Auto-scroll: {self.auto_scroll.get()}")
            logger.debug(f"Auto-refresh: {self.auto_refresh.get()}")
            logger.debug(f"Refresh interval: {REFRESH_INTERVAL} ms")

        # Create UI
        self.create_widgets()

        # Initial check and refresh
        logger.info("Performing initial status check and log refresh")
        self.check_client_status()
        self.refresh_log()

        # Start auto-refresh if enabled
        if self.auto_refresh.get():
            logger.info("Starting auto-refresh")
            self.schedule_refresh()

    def create_widgets(self):
        """Create UI widgets"""
        # Main frame with padding
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Client Status", padding="5")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        # Status indicators
        self.status_label = ttk.Label(status_frame, text="Checking...", foreground="blue")
        self.status_label.pack(side=tk.LEFT, padx=5)

        # Connection status label (new)
        self.connection_label = ttk.Label(status_frame, text="Connection: Unknown", foreground="gray")
        self.connection_label.pack(side=tk.LEFT, padx=20)

        # Control buttons
        control_frame = ttk.Frame(status_frame)
        control_frame.pack(side=tk.RIGHT, padx=5)

        ttk.Button(control_frame, text="Start Client", command=self.start_client).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="Stop Client", command=self.stop_client).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="Refresh", command=self.manual_refresh).pack(side=tk.LEFT, padx=2)

        # Backup status frame (new)
        backup_frame = ttk.LabelFrame(main_frame, text="Backup & MEGA Status", padding="5")
        backup_frame.pack(fill=tk.X, pady=(0, 10))

        # Backup status indicators
        self.backup_status_label = ttk.Label(backup_frame, text="Not monitoring backup activity", foreground="gray")
        self.backup_status_label.pack(side=tk.TOP, padx=5, anchor='w')

        # MEGA status indicators
        self.mega_status_label = ttk.Label(backup_frame, text="MEGA: Not monitoring upload activity", foreground="gray")
        self.mega_status_label.pack(side=tk.TOP, padx=5, anchor='w')

        # Progress bar
        self.progress_frame = ttk.Frame(backup_frame)
        self.progress_frame.pack(fill=tk.X, padx=5, pady=5)

        self.progress_bar = ttk.Progressbar(self.progress_frame, orient="horizontal", length=100, mode="determinate")
        self.progress_bar.pack(fill=tk.X, side=tk.TOP)

        self.progress_label = ttk.Label(self.progress_frame, text="0%")
        self.progress_label.pack(side=tk.TOP, pady=2)

        # Backup details
        self.backup_details_text = scrolledtext.ScrolledText(backup_frame, wrap=tk.WORD, width=80, height=5)
        self.backup_details_text.pack(fill=tk.X, expand=False, padx=5, pady=5)
        self.backup_details_text.insert(tk.END, "No backup details available")
        self.backup_details_text.config(state=tk.DISABLED)

        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Client Log", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # Filters frame
        filters_frame = ttk.Frame(log_frame)
        filters_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(filters_frame, text="Filter:").pack(side=tk.LEFT, padx=(0, 5))
        self.filter_var = tk.StringVar(value="All")
        ttk.Radiobutton(filters_frame, text="All", variable=self.filter_var, value="All", command=self.apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filters_frame, text="Backup Only", variable=self.filter_var, value="Backup", command=self.apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filters_frame, text="Errors Only", variable=self.filter_var, value="Error", command=self.apply_filter).pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(filters_frame, text="Connection Issues", variable=self.filter_var, value="Connection", command=self.apply_filter).pack(side=tk.LEFT, padx=5)

        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, width=80, height=20)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Options frame
        options_frame = ttk.Frame(main_frame)
        options_frame.pack(fill=tk.X, pady=(10, 0))

        # Auto-scroll checkbox
        ttk.Checkbutton(options_frame, text="Auto-scroll", variable=self.auto_scroll).pack(side=tk.LEFT, padx=5)

        # Auto-refresh checkbox
        ttk.Checkbutton(options_frame, text="Auto-refresh", variable=self.auto_refresh,
                        command=self.toggle_auto_refresh).pack(side=tk.LEFT, padx=5)

        # Open config button
        ttk.Button(options_frame, text="Open Config Tool", command=self.open_config_tool).pack(side=tk.RIGHT, padx=5)

        # Clear log button
        ttk.Button(options_frame, text="Clear Log Display", command=self.clear_log_display).pack(side=tk.RIGHT, padx=5)

    def check_client_status(self):
        """Check if the hidden client is running"""
        try:
            # Try to create the mutex without taking ownership
            mutex = ctypes.windll.kernel32.OpenMutexW(0x00100000, 0, MUTEX_NAME)

            if mutex:
                # Mutex exists, client is running
                ctypes.windll.kernel32.CloseHandle(mutex)
                self.client_running = True
                self.status_label.config(text="Client is running", foreground="green")
            else:
                # Mutex doesn't exist, client is not running
                self.client_running = False
                self.status_label.config(text="Client is not running", foreground="red")
        except Exception as e:
            logger.error(f"Error checking client status: {e}")
            self.status_label.config(text=f"Error checking status: {str(e)}", foreground="red")

    def refresh_log(self):
        """Refresh log display"""
        try:
            # Find log file
            log_file_path = find_log_file()

            if not os.path.exists(log_file_path):
                self.log_text.delete(1.0, tk.END)
                self.log_text.insert(tk.END, "Log file not found. Searched in:\n")
                for path in [
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.log"),
                    "ifess_client_hidden.log",
                    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.log"),
                    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_client_hidden.log"),
                ]:
                    self.log_text.insert(tk.END, f"- {path}\n")
                return

            # Get file size
            file_size = os.path.getsize(log_file_path)

            # If file size is the same as last time, no need to refresh
            if file_size == self.log_position and self.log_position > 0:
                return

            # Open log file
            with open(log_file_path, 'r', encoding='utf-8', errors='replace') as f:
                # If auto-scroll is enabled, read the whole file
                if self.auto_scroll.get() or self.log_position == 0:
                    log_content = f.read()
                    self.log_text.delete(1.0, tk.END)
                    self.log_text.insert(tk.END, log_content)
                    self.log_text.see(tk.END)
                else:
                    # Otherwise, just read new content
                    f.seek(self.log_position)
                    new_content = f.read()
                    if new_content:
                        self.log_text.insert(tk.END, new_content)

                # Update position
                self.log_position = file_size

                # Check for various activities in the log content
                self.check_for_backup_activities()
                self.check_for_connection_issues()

                # Apply filter if not set to All
                if self.filter_var.get() != "All":
                    self.apply_filter()

        except Exception as e:
            logger.error(f"Error refreshing log: {e}")
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, f"Error refreshing log: {str(e)}\n\n{traceback.format_exc()}")

    def check_for_connection_issues(self):
        """Check for connection issues in logs"""
        try:
            # Get current log content
            log_content = self.log_text.get(1.0, tk.END)

            # Check for connection lost or disconnect messages
            if "Connection may be lost" in log_content or "Connection lost with server" in log_content:
                self.connection_status = "Disconnected"
                self.connection_label.config(text="Connection: Disconnected", foreground="red")

                # Try to find the time of the last connection drop
                match = re.search(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+Connection may be lost", log_content)
                if match:
                    self.last_connection_drop = match.group(1)
                    logger.info(f"Detected connection drop at {self.last_connection_drop}")

            # Check for reconnection attempts
            reconnect_count = log_content.count("Attempting to reconnect")
            if reconnect_count > 0:
                self.reconnection_attempts = reconnect_count
                logger.info(f"Detected {reconnect_count} reconnection attempts")

            # Check for successful connection
            if "Connected to server" in log_content and "Registered to server" in log_content:
                last_connect_match = re.search(r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}).+Connected to server", log_content)
                if last_connect_match:
                    self.connection_status = "Connected"
                    self.connection_label.config(text="Connection: Connected", foreground="green")
                    logger.info(f"Detected successful connection at {last_connect_match.group(1)}")

            # Update connection details text
            details = ""
            if self.last_connection_drop:
                details += f"Last connection drop: {self.last_connection_drop}\n"
            if self.reconnection_attempts > 0:
                details += f"Reconnection attempts: {self.reconnection_attempts}\n"

            # Add to backup details text
            self.update_backup_details(additional_info=details if details else None)

        except Exception as e:
            logger.error(f"Error checking for connection issues: {e}")

    def check_for_backup_activities(self):
        """Check log for backup activities and update status"""
        try:
            # Get current log content
            log_content = self.log_text.get(1.0, tk.END)

            # Check for MEGA activities first
            self.check_for_mega_activities(log_content)

            # Check for backup request received
            if "[BACKUP] DATABASE BACKUP REQUEST RECEIVED" in log_content or "===== DATABASE BACKUP REQUEST RECEIVED =====" in log_content:
                self.backup_status = "Backup request received"
                self.backup_status_label.config(text=self.backup_status, foreground="blue")
                self.last_backup_time = time.time()

                # Extract detailed information if available
                match = re.search(r"\[BACKUP\] Database path: (.+)", log_content)
                if match:
                    self.backup_details["Database Path"] = match.group(1)

                # Update progress
                self.backup_progress = 5
                self.progress_bar["value"] = 5
                self.progress_label.config(text="5% (Request received)")

                # Update details text
                self.update_backup_details()

            # Check for backup started
            if "Starting backup of database:" in log_content:
                self.backup_status = "Backup started"
                self.backup_status_label.config(text=self.backup_status, foreground="blue")

                # Extract file information
                match = re.search(r"Starting backup of database: (.+), size: (\d+) bytes", log_content)
                if match:
                    self.backup_details["Filename"] = match.group(1)
                    self.backup_details["Size"] = match.group(2) + " bytes"

                # Update progress
                self.backup_progress = 10
                self.progress_bar["value"] = 10
                self.progress_label.config(text="10% (Backup started)")

                # Update details text
                self.update_backup_details()
        except Exception as e:
            logger.error(f"Error checking for backup activities: {e}")
            if self.verbose_mode:
                logger.debug(traceback.format_exc())

    def check_for_mega_activities(self, log_content):
        """Check log for MEGA-related activities and update status"""
        try:
            # Check for MEGA client initialization
            if "Using MEGA client import helper" in log_content:
                self.mega_status_label.config(text="MEGA: Using import helper", foreground="blue")
            elif "Using patched MEGA client" in log_content:
                self.mega_status_label.config(text="MEGA: Using patched client", foreground="blue")
            elif "Using Python 3.10 specific MEGA client" in log_content:
                self.mega_status_label.config(text="MEGA: Using Python 3.10 client", foreground="blue")
            elif "Using standard MEGA client" in log_content:
                self.mega_status_label.config(text="MEGA: Using standard client", foreground="blue")

            # Check for asyncio.coroutine patch
            if "Applying asyncio.coroutine patch" in log_content:
                self.mega_status_label.config(text="MEGA: Applied asyncio.coroutine patch", foreground="green")
            elif "asyncio.coroutine exists, no patch needed" in log_content:
                self.mega_status_label.config(text="MEGA: No patch needed", foreground="green")

            # Check for MEGA login status
            if "Successfully logged in to MEGA" in log_content:
                self.mega_status_label.config(text="MEGA: Logged in successfully", foreground="green")
            elif "Login failed" in log_content and "MEGA" in log_content:
                self.mega_status_label.config(text="MEGA: Login failed", foreground="red")

            # Check for MEGA upload request
            if "[MEGA] ===== RECEIVED MEGA UPLOAD REQUEST =====" in log_content:
                self.mega_status_label.config(text="MEGA: Upload request received", foreground="blue")

            # Check for upload progress
            if "Starting upload of" in log_content and "to MEGA" in log_content:
                self.mega_status_label.config(text="MEGA: Upload in progress", foreground="blue")

            # Check for upload completion
            if "✓ Uploaded" in log_content and "successfully" in log_content:
                self.mega_status_label.config(text="MEGA: Upload completed successfully", foreground="green")

            # Check for upload errors
            if "✗ Error uploading" in log_content:
                self.mega_status_label.config(text="MEGA: Upload failed", foreground="red")

            # Check for asyncio.coroutine errors
            if "asyncio.coroutine" in log_content and "AttributeError" in log_content:
                self.mega_status_label.config(text="MEGA: asyncio.coroutine error", foreground="red")

        except Exception as e:
            logger.error(f"Error checking for MEGA activities: {e}")
            if self.verbose_mode:
                logger.debug(traceback.format_exc())

            # Check for backup started
            if "Starting backup of database:" in log_content:
                self.backup_status = "Backup started"
                self.backup_status_label.config(text=self.backup_status, foreground="blue")

                # Extract file information
                match = re.search(r"Starting backup of database: (.+), size: (\d+) bytes", log_content)
                if match:
                    self.backup_details["Filename"] = match.group(1)
                    self.backup_details["Size"] = match.group(2) + " bytes"

                # Update progress
                self.backup_progress = 10
                self.progress_bar["value"] = 10
                self.progress_label.config(text="10% (Backup started)")

                # Update details text
                self.update_backup_details()

            # Check for chunk progress
            chunk_matches = re.findall(r"\[BACKUP\] Sent chunk (\d+)/(\d+) \((\d+)%\)", log_content)
            if chunk_matches:
                last_match = chunk_matches[-1]
                current_chunk = int(last_match[0])
                total_chunks = int(last_match[1])
                progress = int(last_match[2])

                self.backup_status = f"Sending chunk {current_chunk}/{total_chunks}"
                self.backup_status_label.config(text=self.backup_status, foreground="blue")

                self.backup_details["Current Chunk"] = str(current_chunk)
                self.backup_details["Total Chunks"] = str(total_chunks)
                self.backup_details["Progress"] = f"{progress}%"

                # Update progress bar
                self.backup_progress = progress
                self.progress_bar["value"] = progress
                self.progress_label.config(text=f"{progress}% (Chunk {current_chunk}/{total_chunks})")

                # Update details text
                self.update_backup_details()

            # Check for completion
            if "===== DATABASE BACKUP COMPLETED SUCCESSFULLY =====" in log_content:
                self.backup_status = "Backup completed successfully"
                self.backup_status_label.config(text=self.backup_status, foreground="green")

                # Find completion details
                file_match = re.search(r"\[BACKUP\] File: (.+), Size: (.+)", log_content)
                time_match = re.search(r"\[BACKUP\] Time: (.+) seconds, Speed: (.+) KB/s", log_content)

                if file_match:
                    self.backup_details["Filename"] = file_match.group(1)
                    self.backup_details["Size"] = file_match.group(2)

                if time_match:
                    self.backup_details["Duration"] = time_match.group(1) + " seconds"
                    self.backup_details["Speed"] = time_match.group(2) + " KB/s"

                # Update progress to 100%
                self.backup_progress = 100
                self.progress_bar["value"] = 100
                self.progress_label.config(text="100% (Completed)")

                # Update details text
                self.update_backup_details()

            # Check for failure
            if "===== DATABASE BACKUP FAILED =====" in log_content:
                self.backup_status = "Backup failed"
                self.backup_status_label.config(text=self.backup_status, foreground="red")

                # Find error details
                error_match = re.search(r"\[BACKUP\] Error sending database file: (.+)", log_content)
                if error_match:
                    self.backup_details["Error"] = error_match.group(1)

                # Update progress (keep previous progress)
                self.progress_label.config(text=f"{self.backup_progress}% (Failed)")

                # Update details text
                self.update_backup_details()

        except Exception as e:
            logger.error(f"Error checking for backup activities: {e}")

    def update_backup_details(self, additional_info=None):
        """Update backup details text"""
        try:
            self.backup_details_text.config(state=tk.NORMAL)
            self.backup_details_text.delete(1.0, tk.END)

            if self.backup_details:
                details_text = "Backup Details:\n"
                for key, value in self.backup_details.items():
                    details_text += f"{key}: {value}\n"

                if self.last_backup_time:
                    elapsed = time.time() - self.last_backup_time
                    details_text += f"Elapsed Time: {elapsed:.1f} seconds\n"

                if additional_info:
                    details_text += "\nConnection Info:\n" + additional_info

                self.backup_details_text.insert(tk.END, details_text)
            else:
                self.backup_details_text.insert(tk.END, "No backup details available")

            self.backup_details_text.config(state=tk.DISABLED)
        except Exception as e:
            logger.error(f"Error updating backup details: {e}")

    def apply_filter(self):
        """Apply filter to log display"""
        filter_value = self.filter_var.get()

        if filter_value == "All":
            # Refresh without filtering
            self.log_position = 0
            self.refresh_log()
            return

        try:
            # Store current content
            current_content = self.log_text.get(1.0, tk.END)
            lines = current_content.split('\n')
            filtered_lines = []

            for line in lines:
                if filter_value == "Backup" and ("[BACKUP]" in line or "DATABASE BACKUP" in line):
                    filtered_lines.append(line)
                elif filter_value == "Error" and ("ERROR" in line or "Error" in line or "error" in line or "Failed" in line):
                    filtered_lines.append(line)
                elif filter_value == "Connection" and ("Connection" in line or "connected" in line or "disconnected" in line or "socket" in line.lower()):
                    filtered_lines.append(line)

            # Update display
            self.log_text.delete(1.0, tk.END)
            if filtered_lines:
                self.log_text.insert(tk.END, '\n'.join(filtered_lines))
            else:
                self.log_text.insert(tk.END, f"No {filter_value.lower()} entries found in log")
        except Exception as e:
            logger.error(f"Error applying filter: {e}")
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, f"Error applying filter: {str(e)}")

    def schedule_refresh(self):
        """Schedule automatic refresh"""
        if self.auto_refresh.get():
            self.check_client_status()
            self.refresh_log()
            self.root.after(REFRESH_INTERVAL, self.schedule_refresh)

    def toggle_auto_refresh(self):
        """Toggle auto-refresh"""
        if self.auto_refresh.get():
            self.schedule_refresh()

    def manual_refresh(self):
        """Manually refresh status and log"""
        self.check_client_status()
        self.refresh_log()

    def clear_log_display(self):
        """Clear the log display, but not the file"""
        self.log_text.delete(1.0, tk.END)

    def start_client(self):
        """Start hidden client application"""
        if self.client_running:
            messagebox.showinfo("Info", "Client is already running")
            return

        try:
            # Try to find the client application in multiple locations
            possible_paths = [
                # Same directory as debug app
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.py"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.exe"),
                # Current directory
                "ifess_client_hidden.py",
                "ifess_client_hidden.exe",
                # Parent directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.py"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.exe"),
                # dist directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_client_hidden.exe"),
            ]

            # Find the first path that exists
            client_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    client_path = path
                    logger.info(f"Found client application at: {client_path}")
                    break

            if client_path is None:
                messagebox.showerror("Error", "Client application not found. Please make sure ifess_client_hidden.exe is in the same directory.")
                logger.error("Client application not found in any of the possible locations.")
                return

            # Check file extension to determine how to run it
            if client_path.endswith('.py'):
                # Run as Python script
                python_path = sys.executable
                cmd = [python_path, client_path]
                subprocess.Popen(cmd, creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                # Run as executable
                subprocess.Popen([client_path], creationflags=subprocess.CREATE_NO_WINDOW)

            # Wait a moment for client to start
            time.sleep(1)

            # Check status
            self.check_client_status()

            if self.client_running:
                messagebox.showinfo("Success", "Client started successfully")
            else:
                messagebox.showwarning("Warning", "Client may not have started properly. Check logs for details.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start client: {str(e)}")
            logger.error(f"Error starting client: {e}")
            logger.debug(traceback.format_exc())

    def stop_client(self):
        """Stop hidden client application"""
        if not self.client_running:
            messagebox.showinfo("Info", "Client is not running")
            return

        try:
            # Find the process and terminate it
            if sys.platform == 'win32':
                # On Windows, use taskkill to find and kill by image name
                logger.info("Attempting to stop client using taskkill /im ifess_client_hidden.exe")
                subprocess.run(['taskkill', '/f', '/im', 'ifess_client_hidden.exe'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Also try to kill the Python process if running as script
                logger.info("Attempting to stop client using taskkill for Python process")
                subprocess.run(['taskkill', '/f', '/fi', 'WINDOWTITLE eq ifess_client_hidden.py'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Also try by Python command line
                logger.info("Attempting to stop client using taskkill for Python with command line")
                subprocess.run(['taskkill', '/f', '/fi', 'IMAGENAME eq python.exe', '/fi', 'COMMANDLINE eq *ifess_client_hidden.py*'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Also try by pythonw.exe (for hidden Python processes)
                logger.info("Attempting to stop client using taskkill for pythonw.exe")
                subprocess.run(['taskkill', '/f', '/fi', 'IMAGENAME eq pythonw.exe', '/fi', 'COMMANDLINE eq *ifess_client_hidden.py*'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # Wait a moment for client to stop
            time.sleep(1)

            # Check status
            self.check_client_status()

            if not self.client_running:
                messagebox.showinfo("Success", "Client stopped successfully")
            else:
                messagebox.showwarning("Warning", "Failed to stop client. Try closing it manually.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop client: {str(e)}")
            logger.error(f"Error stopping client: {e}")
            logger.debug(traceback.format_exc())

    def open_config_tool(self):
        """Open the configuration tool"""
        try:
            # Try to find the config tool in multiple locations
            possible_paths = [
                # Same directory as debug app
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_config_gui.py"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_config_gui.exe"),
                # Current directory
                "ifess_config_gui.py",
                "ifess_config_gui.exe",
                # Parent directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_config_gui.py"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_config_gui.exe"),
                # dist directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_config_gui.exe"),
            ]

            # Find the first path that exists
            config_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    logger.info(f"Found config tool at: {config_path}")
                    break

            if config_path is None:
                # If config tool not found, open config file directly
                if os.path.exists(CONFIG_FILE):
                    # Open with default application
                    logger.info(f"Opening config file directly: {CONFIG_FILE}")
                    os.startfile(CONFIG_FILE)
                else:
                    messagebox.showerror("Error", "Configuration tool and file not found")
                    logger.error("Configuration tool and file not found")
                return

            # Check file extension to determine how to run it
            if config_path.endswith('.py'):
                # Run as Python script
                python_path = sys.executable
                subprocess.Popen([python_path, config_path])
            else:
                # Run as executable
                subprocess.Popen([config_path])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open configuration tool: {str(e)}")
            logger.error(f"Error opening config tool: {e}")
            logger.debug(traceback.format_exc())

def main():
    """Main entry point"""
    try:
        # Create main window
        root = tk.Tk()
        app = DebugApp(root)

        # Start Tkinter event loop
        root.mainloop()
    except Exception as e:
        logger.critical(f"Error in main: {e}")
        logger.debug(traceback.format_exc())
        messagebox.showerror("Critical Error", f"Application error: {str(e)}")

if __name__ == "__main__":
    main()
