#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IFESS Client Debug Application (Simplified)

This application provides a simple GUI for viewing console output from the hidden client.
It displays all console output from the hidden client in real-time.
"""

import os
import sys
import json
import socket
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import logging
import traceback
import subprocess
import ctypes
import re

# Constants
CONFIG_FILE = "client_config.json"
LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.log")
MUTEX_NAME = "Global\\IFESS_Hidden_Client_Running"
REFRESH_INTERVAL = 500  # milliseconds (faster refresh for more responsive console display)

# Try to find log file in multiple locations
def find_log_file():
    """Find log file in multiple locations"""
    possible_paths = [
        # Same directory as debug app
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.log"),
        # Current directory
        "ifess_client_hidden.log",
        # Parent directory
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.log"),
        # dist directory
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_client_hidden.log"),
    ]

    # Find the first path that exists
    for path in possible_paths:
        if os.path.exists(path):
            return path

    # If not found, return the default path
    return LOG_FILE

# Parse command line arguments
verbose_mode = False
for arg in sys.argv[1:]:
    if arg == '--verbose':
        verbose_mode = True

# Setup logging
log_level = logging.DEBUG if verbose_mode else logging.INFO
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("ifess_debug.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("IFESS-Debug-Simple")

# Log startup information
logger.info("===== IFESS DEBUG CLIENT (SIMPLIFIED) STARTING =====")
logger.info(f"Start time: {time.strftime('%Y-%m-%d %H:%M:%S')}")
logger.info(f"Python version: {sys.version}")
logger.info(f"Running from: {os.path.abspath(__file__)}")
logger.info(f"Current directory: {os.getcwd()}")
logger.info(f"Log file: {os.path.abspath('ifess_debug.log')}")
logger.info(f"Verbose mode: {verbose_mode}")

class SimpleDebugApp:
    """Simple debug application for IFESS hidden client"""
    def __init__(self, root):
        self.root = root
        self.root.title("IFESS Client Debug Console" + (" (Verbose Mode)" if verbose_mode else ""))
        self.root.geometry("900x700")
        self.root.minsize(700, 500)

        # Set icon if available
        try:
            # Try to find icon in multiple locations
            icon_paths = [
                "MAINICON.ico",
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "MAINICON.ico"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "MAINICON.ico"),
            ]

            icon_found = False
            for icon_path in icon_paths:
                if os.path.exists(icon_path):
                    self.root.iconbitmap(icon_path)
                    icon_found = True
                    break

            if not icon_found and verbose_mode:
                logger.debug("Icon not found in any of the expected locations")
        except Exception as e:
            if verbose_mode:
                logger.debug(f"Could not load icon: {e}")

        # Variables
        self.client_running = False
        self.log_position = 0
        self.auto_scroll = tk.BooleanVar(value=True)
        self.auto_refresh = tk.BooleanVar(value=True)
        self.verbose_mode = verbose_mode

        # Log initialization
        logger.info("Initializing debug application")
        if verbose_mode:
            logger.debug("Verbose mode enabled, additional debug information will be shown")
            logger.debug(f"Auto-scroll: {self.auto_scroll.get()}")
            logger.debug(f"Auto-refresh: {self.auto_refresh.get()}")
            logger.debug(f"Refresh interval: {REFRESH_INTERVAL} ms")

        # Create UI
        self.create_widgets()

        # Initial check and refresh
        logger.info("Performing initial status check and log refresh")
        self.check_client_status()
        self.refresh_log()

        # Start auto-refresh if enabled
        if self.auto_refresh.get():
            logger.info("Starting auto-refresh")
            self.schedule_refresh()

    def create_widgets(self):
        """Create UI widgets"""
        # Main frame with padding
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Client Status", padding="5")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        # Status indicators
        self.status_label = ttk.Label(status_frame, text="Checking...", foreground="blue")
        self.status_label.pack(side=tk.LEFT, padx=5)

        # Control buttons
        control_frame = ttk.Frame(status_frame)
        control_frame.pack(side=tk.RIGHT, padx=5)

        ttk.Button(control_frame, text="Start Client", command=self.start_client).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="Stop Client", command=self.stop_client).pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="Refresh", command=self.manual_refresh).pack(side=tk.LEFT, padx=2)

        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Client Console Output", padding="5")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # Log text area
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, width=80, height=30)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # Configure text tags for coloring
        self.log_text.tag_configure("error", foreground="red")
        self.log_text.tag_configure("warning", foreground="orange")
        self.log_text.tag_configure("info", foreground="blue")
        self.log_text.tag_configure("success", foreground="green")
        self.log_text.tag_configure("mega", foreground="purple")
        self.log_text.tag_configure("gdrive", foreground="teal")
        self.log_text.tag_configure("backup", foreground="brown")

        # Options frame
        options_frame = ttk.Frame(main_frame)
        options_frame.pack(fill=tk.X, pady=(10, 0))

        # Auto-scroll checkbox
        ttk.Checkbutton(options_frame, text="Auto-scroll", variable=self.auto_scroll).pack(side=tk.LEFT, padx=5)

        # Auto-refresh checkbox
        ttk.Checkbutton(options_frame, text="Auto-refresh", variable=self.auto_refresh,
                        command=self.toggle_auto_refresh).pack(side=tk.LEFT, padx=5)

        # Open config button
        ttk.Button(options_frame, text="Open Config Tool", command=self.open_config_tool).pack(side=tk.RIGHT, padx=5)

        # Clear log button
        ttk.Button(options_frame, text="Clear Log Display", command=self.clear_log_display).pack(side=tk.RIGHT, padx=5)

    def check_client_status(self):
        """Check if the hidden client is running"""
        try:
            # Try to create the mutex without taking ownership
            mutex = ctypes.windll.kernel32.OpenMutexW(0x00100000, 0, MUTEX_NAME)

            if mutex:
                # Mutex exists, client is running
                ctypes.windll.kernel32.CloseHandle(mutex)
                self.client_running = True
                self.status_label.config(text="Client is running", foreground="green")
            else:
                # Mutex doesn't exist, client is not running
                self.client_running = False
                self.status_label.config(text="Client is not running", foreground="red")
        except Exception as e:
            logger.error(f"Error checking client status: {e}")
            self.status_label.config(text=f"Error checking status: {str(e)}", foreground="red")

    def refresh_log(self):
        """Refresh log display"""
        try:
            # Find log file
            log_file_path = find_log_file()

            if not os.path.exists(log_file_path):
                self.log_text.delete(1.0, tk.END)
                self.log_text.insert(tk.END, "Log file not found. Searched in:\n")
                for path in [
                    os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.log"),
                    "ifess_client_hidden.log",
                    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.log"),
                    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_client_hidden.log"),
                ]:
                    self.log_text.insert(tk.END, f"- {path}\n")
                return

            # Get file size
            file_size = os.path.getsize(log_file_path)

            # If file size is the same as last time, no need to refresh
            if file_size == self.log_position and self.log_position > 0:
                return

            # Open log file
            with open(log_file_path, 'r', encoding='utf-8', errors='replace') as f:
                # If auto-scroll is enabled, read the whole file
                if self.auto_scroll.get() or self.log_position == 0:
                    log_content = f.read()
                    self.log_text.delete(1.0, tk.END)
                    self.insert_colored_text(log_content)
                    self.log_text.see(tk.END)
                else:
                    # Otherwise, just read new content
                    f.seek(self.log_position)
                    new_content = f.read()
                    if new_content:
                        self.insert_colored_text(new_content)

                # Update position
                self.log_position = file_size

        except Exception as e:
            logger.error(f"Error refreshing log: {e}")
            self.log_text.delete(1.0, tk.END)
            self.log_text.insert(tk.END, f"Error refreshing log: {str(e)}\n\n{traceback.format_exc()}")

    def insert_colored_text(self, text):
        """Insert text with color highlighting based on content"""
        lines = text.split('\n')
        for line in lines:
            # Determine the tag to use based on the content
            tag = None
            if "ERROR" in line or "Error" in line or "error" in line or "FAILED" in line or "Failed" in line:
                tag = "error"
            elif "WARNING" in line or "Warning" in line:
                tag = "warning"
            elif "INFO" in line or "Info" in line:
                tag = "info"
            elif "SUCCESS" in line or "Completed successfully" in line or "COMPLETED" in line:
                tag = "success"
            elif "[MEGA]" in line or "mega.mega" in line:
                tag = "mega"
            elif "[GDRIVE]" in line or "google drive" in line.lower():
                tag = "gdrive"
            elif "[BACKUP]" in line or "DATABASE BACKUP" in line:
                tag = "backup"

            # Insert the line with the appropriate tag
            if tag:
                self.log_text.insert(tk.END, line + '\n', tag)
            else:
                self.log_text.insert(tk.END, line + '\n')

    def schedule_refresh(self):
        """Schedule automatic refresh"""
        if self.auto_refresh.get():
            self.check_client_status()
            self.refresh_log()
            self.root.after(REFRESH_INTERVAL, self.schedule_refresh)

    def toggle_auto_refresh(self):
        """Toggle auto-refresh"""
        if self.auto_refresh.get():
            self.schedule_refresh()

    def manual_refresh(self):
        """Manually refresh status and log"""
        self.check_client_status()
        self.refresh_log()

    def clear_log_display(self):
        """Clear the log display, but not the file"""
        self.log_text.delete(1.0, tk.END)

    def start_client(self):
        """Start hidden client application"""
        if self.client_running:
            messagebox.showinfo("Info", "Client is already running")
            return

        try:
            # Try to find the client application in multiple locations
            possible_paths = [
                # Same directory as debug app
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.py"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_client_hidden.exe"),
                # Current directory
                "ifess_client_hidden.py",
                "ifess_client_hidden.exe",
                # Parent directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.py"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_client_hidden.exe"),
                # dist directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_client_hidden.exe"),
            ]

            # Find the first path that exists
            client_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    client_path = path
                    logger.info(f"Found client application at: {client_path}")
                    break

            if client_path is None:
                messagebox.showerror("Error", "Client application not found. Please make sure ifess_client_hidden.exe is in the same directory.")
                logger.error("Client application not found in any of the possible locations.")
                return

            # Check file extension to determine how to run it
            if client_path.endswith('.py'):
                # Run as Python script
                python_path = sys.executable
                cmd = [python_path, client_path]
                subprocess.Popen(cmd, creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                # Run as executable
                subprocess.Popen([client_path], creationflags=subprocess.CREATE_NO_WINDOW)

            # Wait a moment for client to start
            time.sleep(1)

            # Check status
            self.check_client_status()

            if self.client_running:
                messagebox.showinfo("Success", "Client started successfully")
            else:
                messagebox.showwarning("Warning", "Client may not have started properly. Check logs for details.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start client: {str(e)}")
            logger.error(f"Error starting client: {e}")
            logger.debug(traceback.format_exc())

    def stop_client(self):
        """Stop hidden client application"""
        if not self.client_running:
            messagebox.showinfo("Info", "Client is not running")
            return

        try:
            # Find the process and terminate it
            if sys.platform == 'win32':
                # On Windows, use taskkill to find and kill by image name
                logger.info("Attempting to stop client using taskkill /im ifess_client_hidden.exe")
                subprocess.run(['taskkill', '/f', '/im', 'ifess_client_hidden.exe'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Also try to kill the Python process if running as script
                logger.info("Attempting to stop client using taskkill for Python process")
                subprocess.run(['taskkill', '/f', '/fi', 'WINDOWTITLE eq ifess_client_hidden.py'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Also try by Python command line
                logger.info("Attempting to stop client using taskkill for Python with command line")
                subprocess.run(['taskkill', '/f', '/fi', 'IMAGENAME eq python.exe', '/fi', 'COMMANDLINE eq *ifess_client_hidden.py*'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

                # Also try by pythonw.exe (for hidden Python processes)
                logger.info("Attempting to stop client using taskkill for pythonw.exe")
                subprocess.run(['taskkill', '/f', '/fi', 'IMAGENAME eq pythonw.exe', '/fi', 'COMMANDLINE eq *ifess_client_hidden.py*'], shell=True,
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE)

            # Wait a moment for client to stop
            time.sleep(1)

            # Check status
            self.check_client_status()

            if not self.client_running:
                messagebox.showinfo("Success", "Client stopped successfully")
            else:
                messagebox.showwarning("Warning", "Failed to stop client. Try closing it manually.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop client: {str(e)}")
            logger.error(f"Error stopping client: {e}")
            logger.debug(traceback.format_exc())

    def open_config_tool(self):
        """Open the configuration tool"""
        try:
            # Try to find the config tool in multiple locations
            possible_paths = [
                # Same directory as debug app
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_config_gui.py"),
                os.path.join(os.path.dirname(os.path.abspath(__file__)), "ifess_config_gui.exe"),
                # Current directory
                "ifess_config_gui.py",
                "ifess_config_gui.exe",
                # Parent directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_config_gui.py"),
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "ifess_config_gui.exe"),
                # dist directory
                os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "dist", "ifess_config_gui.exe"),
            ]

            # Find the first path that exists
            config_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    logger.info(f"Found config tool at: {config_path}")
                    break

            if config_path is None:
                # If config tool not found, open config file directly
                if os.path.exists(CONFIG_FILE):
                    # Open with default application
                    logger.info(f"Opening config file directly: {CONFIG_FILE}")
                    os.startfile(CONFIG_FILE)
                else:
                    messagebox.showerror("Error", "Configuration tool and file not found")
                    logger.error("Configuration tool and file not found")
                return

            # Check file extension to determine how to run it
            if config_path.endswith('.py'):
                # Run as Python script
                python_path = sys.executable
                subprocess.Popen([python_path, config_path])
            else:
                # Run as executable
                subprocess.Popen([config_path])
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open configuration tool: {str(e)}")
            logger.error(f"Error opening config tool: {e}")
            logger.debug(traceback.format_exc())

def main():
    """Main entry point"""
    try:
        # Create main window
        root = tk.Tk()
        app = SimpleDebugApp(root)

        # Start Tkinter event loop
        root.mainloop()
    except Exception as e:
        logger.critical(f"Error in main: {e}")
        logger.debug(traceback.format_exc())
        messagebox.showerror("Critical Error", f"Application error: {str(e)}")

if __name__ == "__main__":
    main()
