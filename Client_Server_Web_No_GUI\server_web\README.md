# Firebird Query Server Web Interface

This is a web-based version of the Firebird Query Server, allowing you to manage and monitor Firebird database clients through a modern web interface.

## Features

- Connect to multiple Firebird database clients
- Execute SQL queries across all connected clients
- Monitor database health with predefined queries
- View query history and results
- Save and reuse queries
- Responsive web interface

## Installation

1. Install the required dependencies:

```bash
pip install -r requirements.txt
```

2. Run the server:

```bash
python server_web.py
```

3. Open your web browser and navigate to:

```
http://localhost:5000
```

## Directory Structure

```
server_web/
├── server_web.py       # Main Flask application
├── requirements.txt    # Dependencies
├── templates/          # HTML templates
│   ├── layout.html     # Base template
│   ├── index.html      # Dashboard
│   ├── clients.html    # Client management
│   ├── query.html      # Query execution
│   ├── monitoring.html # Monitoring dashboard
│   └── query_history.html # Query history
├── static/             # Static files
│   ├── css/            # Stylesheets
│   │   └── style.css   # Main stylesheet
│   └── js/             # JavaScript
│       └── main.js     # Main JavaScript file
└── queries/            # Predefined monitoring queries
    └── monitor_diff_job_field.sql # Example monitoring query
```

## Usage

### Dashboard

The dashboard provides an overview of the server status, connected clients, and quick access to all features.

### Clients

View and manage all connected clients. You can see client details and execute queries on specific clients.

### Query

Execute SQL queries on one or more connected clients. You can also use predefined queries or save your own queries for later use.

### Monitoring

Run predefined monitoring queries to check database health and identify issues. Results are displayed in a user-friendly format.

### Query History

View and reuse previously executed queries. You can also save frequently used queries for easy access.

## Predefined Queries

Predefined queries are stored in the `queries` directory as SQL files. The filename (without extension) is used as the query name.

To add a new predefined query:

1. Create a new SQL file in the `queries` directory
2. Name the file descriptively (e.g., `monitor_active_users.sql`)
3. Add your SQL query to the file

The query will automatically appear in the Monitoring and Query pages.

## API Endpoints

The server provides several API endpoints for interacting with the server programmatically:

- `/api/server/start` - Start the server
- `/api/server/stop` - Stop the server
- `/api/query/send` - Send a query to clients
- `/api/query/results` - Get query results
- `/api/clients` - Get client list
- `/api/monitoring/run` - Run a monitoring query

## Troubleshooting

If you encounter any issues:

1. Check the server logs in the console
2. Verify that all clients are properly connected
3. Ensure that the Firebird database files are accessible
4. Check network connectivity between the server and clients

## License

This software is provided as-is with no warranty. Use at your own risk.
