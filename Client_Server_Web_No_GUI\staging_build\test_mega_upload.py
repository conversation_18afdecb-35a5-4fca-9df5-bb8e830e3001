import sys
import os
import time
import logging
import traceback
from mega_client_py310 import MegaClient

# Konfigurasi logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_mega_upload')

def test_upload():
    # Buat file test
    test_file_path = os.path.abspath("test_upload.txt")
    with open(test_file_path, "w") as f:
        f.write(f"Test upload file created at {time.ctime()}\n")
        f.write("This is a test file for MEGA upload functionality.\n")
        f.write("=" * 50 + "\n")
        for i in range(100):
            f.write(f"Line {i+1}: Adding some content to make the file bigger\n")
    
    file_size = os.path.getsize(test_file_path)
    logger.info(f"Created test file: {test_file_path} ({file_size} bytes)")
    
    try:
        # Inisialisasi MEGA client dengan client_id
        logger.info("Initializing MEGA client")
        client_id = "test_client_001"  # Menggunakan ID client untuk testing
        mega_client = MegaClient(client_id)
        
        # Coba login
        logger.info("Attempting to log in to MEGA")
        if not mega_client.logged_in:
            logger.error("Failed to log in to MEGA, cannot continue testing")
            return
        
        # Upload file
        logger.info("Uploading test file to MEGA")
        
        # Gunakan custom name untuk testing
        custom_name = f"test_upload_{time.strftime('%Y%m%d_%H%M%S')}"
        
        result = mega_client.upload_file_to_mega(test_file_path, custom_name=custom_name)
        
        # Tampilkan hasil
        if result['success']:
            logger.info("=" * 50)
            logger.info("UPLOAD SUCCESS!")
            logger.info(f"File name: {result['file_name']}")
            logger.info(f"File size: {result['size']} bytes")
            logger.info(f"Upload duration: {result['duration']:.2f} seconds")
            logger.info(f"Upload speed: {result['speed']/1024/1024:.2f} MB/s")
            logger.info(f"Uploaded to folder: {result['folder']}")
            if result.get('link'):
                logger.info(f"Download link: {result['link']}")
            logger.info("=" * 50)
        else:
            logger.error("=" * 50)
            logger.error("UPLOAD FAILED!")
            logger.error(f"Error: {result.get('error', 'Unknown error')}")
            logger.error("=" * 50)
            
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        logger.error(traceback.format_exc())
    finally:
        # Cleanup
        logger.info("Cleaning up test file")
        try:
            if os.path.exists(test_file_path):
                os.remove(test_file_path)
                logger.info(f"Removed test file: {test_file_path}")
        except Exception as e:
            logger.warning(f"Failed to remove test file: {e}")

if __name__ == "__main__":
    logger.info("Starting MEGA upload test")
    test_upload()
    logger.info("MEGA upload test completed") 