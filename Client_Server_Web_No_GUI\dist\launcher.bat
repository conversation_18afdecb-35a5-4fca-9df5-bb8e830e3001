@echo off
echo IFESS Client Launcher
echo =====================
echo.
echo 1. Start Hidden Client
echo 2. Open Configuration GUI
echo 3. Open Debug Monitor
echo 4. Exit
echo.
choice /C 1234 /N /M "Select an option: "

if errorlevel 4 goto :exit
if errorlevel 3 goto :debug
if errorlevel 2 goto :config
if errorlevel 1 goto :hidden

:hidden
echo Starting Hidden Client...
start "" /b ifess_client_hidden.exe
goto :exit

:config
echo Opening Configuration GUI...
start "" ifess_config_gui.exe
goto :exit

:debug
echo Opening Debug Monitor...
start "" ifess_client_debug.exe
goto :exit

:exit
echo Exiting...
