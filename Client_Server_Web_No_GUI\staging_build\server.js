/**
 * IFESS Server Web Node.js Wrapper
 * 
 * This script serves as a wrapper for the Python-based server_web application,
 * allowing it to be run with Node.js on any computer.
 */

const express = require('express');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

// Configuration
const PORT = process.env.PORT || 5000;
const PYTHON_EXECUTABLE = path.join(__dirname, 'dist', 'server_web.exe');
const PYTHON_PORT = 5001; // The port the Python app will run on

// Create Express app
const app = express();
const server = http.createServer(app);

// Store the Python process
let pythonProcess = null;
let pythonRunning = false;

// Middleware to check if Python is running
const checkPythonRunning = (req, res, next) => {
  if (!pythonRunning) {
    return res.status(503).json({ 
      error: 'Python server is not running',
      message: 'The backend server is starting up or has encountered an error. Please try again in a moment.'
    });
  }
  next();
};

// Start the Python process
function startPythonProcess() {
  console.log('Starting Python process...');
  
  // Check if the executable exists
  if (!fs.existsSync(PYTHON_EXECUTABLE)) {
    console.error(`Python executable not found at: ${PYTHON_EXECUTABLE}`);
    console.log('Using fallback to python command...');
    
    // Try to use python command as fallback
    pythonProcess = spawn('python', [path.join(__dirname, 'server_web', 'server_web.py'), '--port', PYTHON_PORT.toString()], {
      cwd: __dirname
    });
  } else {
    // Use the compiled executable
    pythonProcess = spawn(PYTHON_EXECUTABLE, ['--port', PYTHON_PORT.toString()], {
      cwd: __dirname
    });
  }

  // Handle Python process output
  pythonProcess.stdout.on('data', (data) => {
    console.log(`Python stdout: ${data}`);
    
    // Check if the server is running
    if (data.toString().includes('Running on http')) {
      pythonRunning = true;
      console.log('Python server is now running');
    }
  });

  pythonProcess.stderr.on('data', (data) => {
    console.error(`Python stderr: ${data}`);
  });

  pythonProcess.on('close', (code) => {
    console.log(`Python process exited with code ${code}`);
    pythonRunning = false;
    
    // Restart the Python process if it crashes
    if (code !== 0) {
      console.log('Python process crashed, restarting in 5 seconds...');
      setTimeout(startPythonProcess, 5000);
    }
  });
}

// Proxy middleware to forward requests to the Python server
app.use((req, res, next) => {
  // Skip static files
  if (req.path.startsWith('/static')) {
    return next();
  }
  
  // Check if Python is running
  if (!pythonRunning) {
    return res.status(503).send('Backend server is starting up. Please try again in a moment.');
  }
  
  // Forward the request to the Python server
  const options = {
    hostname: 'localhost',
    port: PYTHON_PORT,
    path: req.url,
    method: req.method,
    headers: req.headers
  };
  
  const proxyReq = http.request(options, (proxyRes) => {
    res.writeHead(proxyRes.statusCode, proxyRes.headers);
    proxyRes.pipe(res);
  });
  
  proxyReq.on('error', (error) => {
    console.error('Proxy request error:', error);
    res.status(500).send('Error communicating with backend server');
  });
  
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    req.pipe(proxyReq);
  } else {
    proxyReq.end();
  }
});

// Serve static files
app.use('/static', express.static(path.join(__dirname, 'server_web', 'static')));

// Start the server
server.listen(PORT, () => {
  console.log(`Node.js server running on port ${PORT}`);
  console.log(`Starting Python backend on port ${PYTHON_PORT}...`);
  startPythonProcess();
});

// Handle server shutdown
process.on('SIGINT', () => {
  console.log('Shutting down...');
  
  if (pythonProcess) {
    console.log('Terminating Python process...');
    pythonProcess.kill();
  }
  
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});
