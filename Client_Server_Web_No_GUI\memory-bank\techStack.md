# Technology Stack

**Last Updated**: 2025-01-27
**Stack Version**: 2.1 - Enhanced with Google Drive Integration

## Core Technologies

### Programming Environment
- **Python 3.10+**: Main programming language with asyncio compatibility considerations
- **Platform**: Windows 10+ (win32 10.0.26100)
- **Shell**: PowerShell (C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe)
- **Architecture**: x64 compatible with legacy x86 components

### User Interface Technologies
- **Tkinter**: GUI framework for debug and configuration interfaces
- **Threading**: Concurrent operations and background task management
- **Windows API**: Native Windows integration via ctypes
- **Console Management**: Command-line interface and terminal handling

### Network and Communication
- **Socket Programming**: TCP/IP communication protocol for client-server
- **JSON Serialization**: Message format and configuration management
- **HTTP/HTTPS**: Google Drive API communication
- **OAuth 2.0**: Authentication protocol for cloud services

## Database Integration

### Firebird Database System
- **Firebird Engine**: Primary database system for monitoring
- **ISQL Interface**: Command-line tool at `C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe`
- **FDB Driver**: Python Firebird database connector
- **SQL Parameterization**: Secure query execution with parameter binding
- **Connection Management**: Database connection pooling and reuse

### Database Operations
```python
# Database connection pattern
database_config = {
    "path": "D:/path/to/database.FDB",
    "username": "sysdba", 
    "password": "masterkey",
    "use_localhost": true
}
```

## Cloud Storage Integration

### Google Drive API ✅ ENHANCED
- **Google API Client**: `google-api-python-client` for Drive operations
- **OAuth 2.0 Flow**: `google-auth-oauthlib` for authentication
- **Token Management**: Automatic refresh with `google-auth`
- **Folder Structure**: `Backup_PTRJ/Auto_Backup_App/Ifess_Database/{client_id}`

```python
# Enhanced authentication flow
def _load_credentials_from_token(self):
    """Load credentials from token.json file"""
    if os.path.exists(self.token_file):
        creds = Credentials.from_authorized_user_file(self.token_file, self.SCOPES)
        if creds and creds.valid:
            return creds
        elif creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
            return creds
```

### MEGA Cloud Storage ✅ COMPLETED
- **MEGA API**: Multiple client variants for compatibility
- **Fallback Chain**: 6 different implementations for maximum reliability
- **Progress Tracking**: Real-time upload progress with callbacks
- **Authentication**: Email/password based authentication

```python
# MEGA client fallback order
fallback_clients = [
    'mega_client_compiled',    # Built-in patch
    'mega_client_patched',     # Python 3.10+ compatibility  
    'mega_client_fixed',       # Progress tracking
    'mega_client_py310',       # Python 3.10 specific
    'mega_client',             # Standard client
    'mega_client_dummy'        # Disabled functionality
]
```

## Development and Build Tools

### Package Management
- **pip**: Python package installer with requirements.txt
- **Virtual Environment**: `venv_py310` for isolated dependencies
- **PyInstaller**: Executable generation with .spec files
- **Batch Scripts**: Automated build and deployment processes

### Build Configuration
```
staging_build/
├── *.spec files          # PyInstaller specifications
├── build_*.bat          # Automated build scripts  
├── requirements.txt     # Python dependencies
└── dist/               # Generated executables
```

### Dependencies ✅ UPDATED
```python
# Core Python packages (built-in)
import logging, threading, socket, json, tkinter
import time, uuid, platform, traceback, ctypes
import tempfile, subprocess, datetime, base64, stat

# External libraries
google-api-python-client>=2.0.0
google-auth>=2.0.0
google-auth-oauthlib>=0.5.0
google-auth-httplib2>=0.1.0
fdb>=2.0.0                    # Firebird database driver
mega.py                       # MEGA cloud storage (multiple variants)
```

## Configuration Management

### Configuration Architecture
```json
{
  "server_address": "localhost",
  "server_port": 5555,
  "reconnect_interval": 5,
  "client_id": "client_coba",
  "display_name": "FDB-Client-Monitoring",
  "database": {
    "path": "D:/path/to/database.FDB",
    "username": "sysdba",
    "password": "masterkey",
    "isql_path": "C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe",
    "use_localhost": true
  },
  "mega": {
    "email": "<EMAIL>",
    "password": "password"
  }
}
```

### OAuth Configuration ✅ NEW
```json
{
  "gdrive": {
    "token_file": "token.json",
    "folder_structure": "Backup_PTRJ/Auto_Backup_App/Ifess_Database",
    "scopes": ["https://www.googleapis.com/auth/drive.file"]
  }
}
```

## Technical Constraints and Limitations

### Platform-Specific Constraints
1. **Windows Dependencies**: ctypes for Windows API, mutex handling
2. **File Path Limitations**: Windows-style paths, case-insensitive filesystem
3. **Process Management**: Windows service architecture requirements
4. **Shell Integration**: PowerShell-specific batch file execution

### Python Version Constraints
1. **asyncio.coroutine Deprecation**: Affects MEGA client compatibility in Python 3.10+
2. **Threading Model**: GIL limitations for CPU-intensive operations
3. **Memory Management**: Garbage collection considerations for long-running services
4. **Import System**: Dynamic import fallbacks for cloud client variants

### Network and Security Constraints
1. **Plain Text Configuration**: Sensitive data stored without encryption
2. **TCP Communication**: No built-in SSL/TLS encryption
3. **Authentication**: Basic client registration without validation
4. **File Permissions**: Relies on filesystem access control

## Performance Characteristics

### Current Performance Profile
- **Memory Usage**: ~50-100MB per client instance
- **CPU Usage**: Low during idle, spikes during uploads/database operations
- **Network Bandwidth**: Dependent on file transfer operations
- **Disk I/O**: Log rotation (5MB files, 3 backups), database file transfers

### Optimization Strategies ✅ IMPLEMENTED
1. **Chunked Transfers**: Large file uploads split into manageable chunks
2. **Progress Callbacks**: Real-time progress tracking without blocking
3. **Thread Management**: Daemon threads for background operations
4. **Connection Reuse**: Database connection pooling and reuse

## Logging and Monitoring

### Logging Architecture
```python
# Rotating file handler configuration
log_handler = RotatingFileHandler(
    LOG_FILE,
    maxBytes=5*1024*1024,  # 5 MB
    backupCount=3
)

# Log format
formatter = logging.Formatter(
    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

### Debug and Monitoring Tools
- **Real-time Log Monitoring**: File-based log streaming in debug client
- **Connection Statistics**: Uptime, reconnection counts, error tracking
- **Progress Tracking**: Upload progress with percentage and status updates
- **Error Reporting**: Detailed exception logging with stack traces

## Security Architecture

### Current Security Model
- **Credential Storage**: Plain text in JSON configuration files
- **Network Security**: Unencrypted TCP connections
- **Access Control**: File system permissions only
- **Authentication**: Basic client registration

### OAuth Security ✅ IMPLEMENTED
- **Token-Based Auth**: Secure OAuth 2.0 flow for Google Drive
- **Automatic Refresh**: Token refresh without user intervention
- **Scope Limitation**: Minimal required permissions for Drive operations
- **Secure Storage**: Token.json with appropriate file permissions

## Integration Challenges and Solutions

### MEGA Client Compatibility ✅ RESOLVED
**Challenge**: Python 3.10+ asyncio.coroutine deprecation
**Solution**: Multiple fallback clients with compatibility patches
**Implementation**: Robust client loading with graceful fallbacks

### Google Drive Authentication ✅ RESOLVED  
**Challenge**: Complex OAuth flow and token management
**Solution**: Simplified token-based authentication with automatic refresh
**Implementation**: Enhanced GDrive client with token.json support

### Component Synchronization 🔄 IN PROGRESS
**Challenge**: Feature inconsistency across client components
**Solution**: Shared module architecture for common functionality
**Implementation**: Extract common code to reusable modules

## Development Environment Setup

### Required Software
1. **Python 3.10+**: Main development environment
2. **Firebird Database**: Database engine installation
3. **Google Cloud Console**: API credentials and project setup
4. **MEGA Account**: Cloud storage account for backup testing
5. **PyInstaller**: Executable generation tool

### Environment Configuration
```bash
# Virtual environment setup
python -m venv venv_py310
venv_py310\Scripts\activate

# Dependency installation
pip install -r requirements.txt

# Build environment
pyinstaller --onefile ifess_client_hidden.spec
```

## Cross-References
- **Architecture Details**: See `systemArchitecture.md` for component relationships
- **Implementation Progress**: See `progressTracker.md` for current status
- **Active Development**: See `currentFocus.md` for ongoing work
- **Project Scope**: See `projectOverview.md` for overall objectives
