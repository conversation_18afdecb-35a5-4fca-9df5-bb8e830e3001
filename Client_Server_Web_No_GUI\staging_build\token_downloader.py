"""
Token Downloader Module for IFESS
---------------------------------
This module downloads the latest OAuth token from a Google Drive folder.
It looks for token files with format token_YYYYMMDD_HHMMSS.json and selects the most recent one.

The module will:
1. Get a list of all token files from the shared Google Drive folder
2. Sort them by date/time in the filename (newest first)
3. Download the most recent valid token
4. Validate and use the token for authentication
"""

import os
import sys
import json
import time
import logging
import requests
import traceback
import re
import io
from datetime import datetime

# Try to import Google API libraries
try:
    from google.oauth2.credentials import Credentials
    from google.oauth2 import service_account
    from googleapiclient.discovery import build
    from googleapiclient.errors import HttpError
    from googleapiclient.http import MediaIoBaseDownload
    GOOGLE_API_AVAILABLE = True
except ImportError:
    GOOGLE_API_AVAILABLE = False

# Setup logging
log_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(log_dir, "token_downloader.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("token_downloader")

# Determine base directory - whether running as script or frozen executable
if getattr(sys, 'frozen', False):
    # Running in a bundled executable
    BASE_DIR = os.path.dirname(sys.executable)
else:
    # Running as a script
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# File paths
TOKEN_FILE = os.path.join(BASE_DIR, "token.json")
TOKEN_BACKUP_FILE = os.path.join(BASE_DIR, "token_backup.json")

# Google Drive folder ID containing token files
TOKEN_FOLDER_ID = "*********************************"

# Maximum number of download attempts
MAX_DOWNLOAD_ATTEMPTS = 10

# Delay between download attempts (in seconds)
DOWNLOAD_RETRY_DELAY = 3

# Maximum number of token validation attempts
MAX_VALIDATION_ATTEMPTS = 5

# Maximum token age in days (tokens older than this will be ignored)
MAX_TOKEN_AGE_DAYS = 30

# Token filename pattern (YYYYMMDD_HHMMSS)
TOKEN_FILENAME_PATTERN = r'token_(\d{8})_(\d{6})\.json'

# Google API scopes
SCOPES = ['https://www.googleapis.com/auth/drive.readonly']

# Service account file path
SERVICE_ACCOUNT_FILE = os.path.join(BASE_DIR, "ptrj-backup-services-account.json")

# Always use service account for authentication
USE_SERVICE_ACCOUNT = True

# Service object for Google Drive API
drive_service = None

def get_service_account_credentials():
    """Get service account credentials"""
    try:
        # Check if service account file exists
        if not os.path.exists(SERVICE_ACCOUNT_FILE):
            logger.error(f"Service account file not found: {SERVICE_ACCOUNT_FILE}")
            return None

        # Load service account credentials
        credentials = service_account.Credentials.from_service_account_file(
            SERVICE_ACCOUNT_FILE, scopes=SCOPES)

        logger.info(f"Successfully loaded service account credentials from {SERVICE_ACCOUNT_FILE}")
        return credentials
    except Exception as e:
        logger.error(f"Error loading service account credentials: {e}")
        logger.error(traceback.format_exc())
        return None

def get_authenticated_service():
    """Get an authenticated Google Drive service object using service account"""
    global drive_service

    # If service already exists, return it
    if drive_service:
        return drive_service

    # Check if Google API libraries are available
    if not GOOGLE_API_AVAILABLE:
        logger.warning("Google API libraries not available, cannot create authenticated service")
        return None

    try:
        # Get service account credentials
        credentials = get_service_account_credentials()

        if not credentials:
            logger.error("Failed to get service account credentials")
            return None

        # Create and return the Drive service
        drive_service = build('drive', 'v3', credentials=credentials)
        logger.info("Created authenticated Google Drive service using service account")
        return drive_service
    except Exception as e:
        logger.error(f"Error creating authenticated service: {e}")
        logger.error(traceback.format_exc())
        return None

def get_token_list_from_folder(attempt=1):
    """Get list of token files from Google Drive folder using service account

    Returns a list containing the single token file in the folder
    """
    try:
        logger.info(f"Getting token file from folder ID: {TOKEN_FOLDER_ID} (attempt {attempt}/{MAX_DOWNLOAD_ATTEMPTS})...")

        # Use service account authentication
        if USE_SERVICE_ACCOUNT and GOOGLE_API_AVAILABLE:
            try:
                # Get authenticated service
                service = get_authenticated_service()

                if service:
                    logger.info("Using service account to list files")

                    # List files in the folder (any file, not just token_*)
                    results = service.files().list(
                        q=f"'{TOKEN_FOLDER_ID}' in parents and trashed=false",
                        fields="files(id, name, createdTime, modifiedTime)",
                        orderBy="modifiedTime desc"
                    ).execute()

                    files = results.get('files', [])
                    logger.info(f"Found {len(files)} files in the folder using service account")

                    # Process files
                    if files:
                        # Just return the files, no need for complex processing
                        # Log the file details
                        for file in files:
                            file_name = file.get('name', '')
                            modified_time = file.get('modifiedTime', '')
                            logger.info(f"Found file: {file_name} (Modified: {modified_time})")

                        return files
                    else:
                        logger.warning("No files found in the folder using service account")

                        # Retry if we haven't reached the maximum number of attempts
                        if attempt < MAX_DOWNLOAD_ATTEMPTS:
                            logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
                            time.sleep(DOWNLOAD_RETRY_DELAY)
                            return get_token_list_from_folder(attempt + 1)
                        else:
                            logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
                            return []
                else:
                    logger.error("Could not create authenticated service using service account")

                    # Retry if we haven't reached the maximum number of attempts
                    if attempt < MAX_DOWNLOAD_ATTEMPTS:
                        logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
                        time.sleep(DOWNLOAD_RETRY_DELAY)
                        return get_token_list_from_folder(attempt + 1)
                    else:
                        logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
                        return []
            except Exception as auth_error:
                logger.error(f"Error using service account: {auth_error}")
                logger.error(traceback.format_exc())

                # Retry if we haven't reached the maximum number of attempts
                if attempt < MAX_DOWNLOAD_ATTEMPTS:
                    logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
                    time.sleep(DOWNLOAD_RETRY_DELAY)
                    return get_token_list_from_folder(attempt + 1)
                else:
                    logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
                    return []
        else:
            logger.error("Service account authentication not available")

            # Retry if we haven't reached the maximum number of attempts
            if attempt < MAX_DOWNLOAD_ATTEMPTS:
                logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
                time.sleep(DOWNLOAD_RETRY_DELAY)
                return get_token_list_from_folder(attempt + 1)
            else:
                logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
                return []

    except Exception as e:
        logger.error(f"Error getting token list from folder: {e}")
        logger.error(traceback.format_exc())

        # Retry if we haven't reached the maximum number of attempts
        if attempt < MAX_DOWNLOAD_ATTEMPTS:
            logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
            time.sleep(DOWNLOAD_RETRY_DELAY)
            return get_token_list_from_folder(attempt + 1)
        else:
            logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
            return []

def is_token_likely_valid(file_info):
    """Check if a token is likely to be valid based on its filename date/time

    Args:
        file_info: Dictionary containing token file information

    Returns:
        bool: True if the token is likely to be valid, False otherwise
    """
    # If no parsed_datetime, we can't tell
    if 'parsed_datetime' not in file_info:
        return True

    # Get current time in UTC
    now = datetime.now()

    # Get token datetime
    token_datetime = file_info['parsed_datetime']

    # If token datetime is in the future, it's likely valid
    if token_datetime > now:
        return True

    # If token datetime is more than 7 days old, it's likely expired
    # (Google OAuth tokens typically expire after 7 days)
    if (now - token_datetime).days > 7:
        logger.warning(f"Token {file_info.get('name')} is more than 7 days old, likely expired")
        return False

    # Otherwise, we'll assume it might be valid
    return True

def process_token_files(files):
    """Process token files and sort them by date/time in the filename with improved filtering

    Args:
        files: List of token files from Google Drive

    Returns:
        list: Sorted and filtered list of token files
    """
    try:
        if not files:
            logger.warning("No token files to process")
            return []

        # Process files to extract date and time from filename
        token_files = []
        current_time = datetime.now()
        max_age = datetime.timedelta(days=MAX_TOKEN_AGE_DAYS)

        for file in files:
            file_name = file.get('name', '')
            match = re.match(TOKEN_FILENAME_PATTERN, file_name)

            if match:
                try:
                    date_str = match.group(1)  # YYYYMMDD
                    time_str = match.group(2)  # HHMMSS
                    date_time_str = f"{date_str}_{time_str}"
                    date_time = datetime.strptime(date_time_str, "%Y%m%d_%H%M%S")

                    # Check if token is too old
                    age = current_time - date_time
                    days_old = age.days
                    hours_old = age.total_seconds() / 3600

                    if age > max_age:
                        logger.warning(f"Token {file_name} is too old ({days_old} days), ignoring")
                        continue

                    # Add parsed information to the item
                    file['parsed_datetime'] = date_time
                    file['date_str'] = date_str
                    file['time_str'] = time_str
                    file['age_days'] = days_old
                    file['age_hours'] = hours_old

                    # Check if token is likely expired based on filename date
                    if days_old > 7:  # Google OAuth tokens typically expire after 7 days
                        file['likely_expired'] = True
                        logger.info(f"Found token file: {file_name} (Date: {date_str}, Time: {time_str}) - Likely expired ({days_old} days old)")
                    else:
                        file['likely_expired'] = False
                        logger.info(f"Found token file: {file_name} (Date: {date_str}, Time: {time_str}) - Possibly valid ({hours_old:.1f} hours old)")

                    token_files.append(file)
                except Exception as parse_error:
                    logger.warning(f"Error parsing date/time from filename {file_name}: {parse_error}")
                    # Still add the file, but without parsed_datetime
                    token_files.append(file)
            else:
                logger.warning(f"File {file_name} doesn't match expected pattern {TOKEN_FILENAME_PATTERN}")
                # If filename doesn't match expected format, still include it
                token_files.append(file)

        # First separate valid and likely expired tokens
        valid_tokens = [f for f in token_files if not f.get('likely_expired', False)]
        expired_tokens = [f for f in token_files if f.get('likely_expired', False)]

        # Sort each group by date (newest first)
        valid_tokens.sort(
            key=lambda x: (
                x.get('parsed_datetime', datetime.min) if 'parsed_datetime' in x
                else datetime.fromisoformat(x.get('modifiedTime', '').replace('Z', '+00:00'))
            ),
            reverse=True
        )

        expired_tokens.sort(
            key=lambda x: (
                x.get('parsed_datetime', datetime.min) if 'parsed_datetime' in x
                else datetime.fromisoformat(x.get('modifiedTime', '').replace('Z', '+00:00'))
            ),
            reverse=True
        )

        # Combine the lists with valid tokens first
        sorted_tokens = valid_tokens + expired_tokens

        # Log the sorted files
        logger.info("Sorted token files (newest valid first, then expired):")
        for i, file in enumerate(sorted_tokens[:5]):  # Log only the first 5 files
            file_name = file.get('name', '')
            if 'parsed_datetime' in file:
                expired_str = " (likely expired)" if file.get('likely_expired', False) else ""
                logger.info(f"  {i+1}. {file_name} (Date: {file.get('date_str')}, Time: {file.get('time_str')}, Age: {file.get('age_days')} days){expired_str}")
            else:
                modified_time = datetime.fromisoformat(file.get('modifiedTime', '').replace('Z', '+00:00'))
                logger.info(f"  {i+1}. {file_name} (Modified: {modified_time})")

        if len(sorted_tokens) > 5:
            logger.info(f"  ... and {len(sorted_tokens) - 5} more files")

        return sorted_tokens
    except Exception as e:
        logger.error(f"Error processing token files: {e}")
        logger.error(traceback.format_exc())
        return []

# No longer needed - using service account instead

def download_token(attempt=1, files=None):
    """Download the token file from Google Drive folder using service account

    Args:
        attempt: Current attempt number
        files: List of files (if already retrieved)

    Returns:
        bool: True if token was successfully downloaded, False otherwise
    """
    try:
        logger.info(f"Downloading token from Google Drive folder (attempt {attempt}/{MAX_DOWNLOAD_ATTEMPTS})...")

        # First, try to backup existing token if it exists
        if os.path.exists(TOKEN_FILE):
            try:
                logger.info(f"Backing up existing token to {TOKEN_BACKUP_FILE}")
                with open(TOKEN_FILE, 'r') as f:
                    token_data = f.read()
                with open(TOKEN_BACKUP_FILE, 'w') as f:
                    f.write(token_data)
            except Exception as backup_error:
                logger.warning(f"Failed to backup existing token: {backup_error}")

        # Get list of files from folder if not provided
        if files is None:
            files = get_token_list_from_folder()

        if not files:
            logger.error("No files found in the folder")

            # Retry if we haven't reached the maximum number of attempts
            if attempt < MAX_DOWNLOAD_ATTEMPTS:
                logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
                time.sleep(DOWNLOAD_RETRY_DELAY)
                return download_token(attempt + 1)
            else:
                logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
                return False

        # Get the token file (should be only one in the folder)
        token_file = files[0]
        file_id = token_file.get('id')
        file_name = token_file.get('name')
        modified_time = token_file.get('modifiedTime', '')

        # Log file details
        logger.info(f"Downloading token file: {file_name} (Modified: {modified_time}, ID: {file_id})")

        # Use service account to download the file
        if USE_SERVICE_ACCOUNT and GOOGLE_API_AVAILABLE:
            try:
                # Get authenticated service
                service = get_authenticated_service()

                if service:
                    logger.info("Using service account to download token")

                    # Download the file
                    request = service.files().get_media(fileId=file_id)

                    # Use MediaIoBaseDownload for larger files
                    fh = io.BytesIO()
                    downloader = MediaIoBaseDownload(fh, request)

                    done = False
                    while not done:
                        status, done = downloader.next_chunk()
                        logger.info(f"Download progress: {int(status.progress() * 100)}%")

                    # Get the content
                    content = fh.getvalue()

                    # Check if the response is valid JSON
                    try:
                        token_data = json.loads(content.decode('utf-8'))

                        # Process the token data
                        return process_downloaded_token(token_data, file_id, file_name)
                    except json.JSONDecodeError:
                        logger.error("Downloaded content is not valid JSON")
                        logger.debug(f"Downloaded content: {content[:100]}...")  # Log first 100 chars

                        # Retry if we haven't reached the maximum number of attempts
                        if attempt < MAX_DOWNLOAD_ATTEMPTS:
                            logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
                            time.sleep(DOWNLOAD_RETRY_DELAY)
                            return download_token(attempt + 1)
                        else:
                            logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
                            return False
                else:
                    logger.error("Could not create authenticated service using service account")

                    # Retry if we haven't reached the maximum number of attempts
                    if attempt < MAX_DOWNLOAD_ATTEMPTS:
                        logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
                        time.sleep(DOWNLOAD_RETRY_DELAY)
                        return download_token(attempt + 1)
                    else:
                        logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
                        return False
            except Exception as auth_error:
                logger.error(f"Error using service account to download token: {auth_error}")
                logger.error(traceback.format_exc())

                # Retry if we haven't reached the maximum number of attempts
                if attempt < MAX_DOWNLOAD_ATTEMPTS:
                    logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
                    time.sleep(DOWNLOAD_RETRY_DELAY)
                    return download_token(attempt + 1)
                else:
                    logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
                    return False
        else:
            logger.error("Service account authentication not available")

            # Retry if we haven't reached the maximum number of attempts
            if attempt < MAX_DOWNLOAD_ATTEMPTS:
                logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
                time.sleep(DOWNLOAD_RETRY_DELAY)
                return download_token(attempt + 1)
            else:
                logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
                return False

    except Exception as e:
        logger.error(f"Error downloading token: {e}")
        logger.error(traceback.format_exc())

        # Retry if we haven't reached the maximum number of attempts
        if attempt < MAX_DOWNLOAD_ATTEMPTS:
            logger.info(f"Retrying in {DOWNLOAD_RETRY_DELAY} seconds...")
            time.sleep(DOWNLOAD_RETRY_DELAY)
            return download_token(attempt + 1)
        else:
            logger.error(f"Maximum number of attempts ({MAX_DOWNLOAD_ATTEMPTS}) reached")
            return False

def process_downloaded_token(token_data, file_id=None, file_name=None):
    """Process downloaded token data

    Args:
        token_data: The token data to process
        file_id: The ID of the file (for logging)
        file_name: The name of the file (for logging)

    Returns:
        bool: True if token was successfully processed, False otherwise
    """
    # Log which file we're processing
    if file_name:
        logger.info(f"Processing token data from file: {file_name}")

    # Validate token data
    required_keys = ["token", "refresh_token", "token_uri", "client_id", "client_secret", "scopes"]
    missing_keys = [key for key in required_keys if key not in token_data]

    if not missing_keys:
        # Save the token file
        with open(TOKEN_FILE, 'w') as f:
            json.dump(token_data, f)

        logger.info(f"Token downloaded and saved to {TOKEN_FILE}")

        # Log token expiry if available
        if 'expiry' in token_data:
            logger.info(f"Token expiry: {token_data['expiry']}")

        return True
    else:
        logger.error(f"Downloaded token is missing required fields: {', '.join(missing_keys)}")
        return False

def is_token_expiring_soon(token_data, threshold_seconds=3600):
    """Check if token is expiring soon (within threshold_seconds)"""
    try:
        # Check if token has expiry information
        if 'expiry' in token_data:
            import datetime

            # Parse expiry time
            expiry_str = token_data['expiry']
            # Handle different date formats
            try:
                # Try ISO format first
                expiry_time = datetime.datetime.fromisoformat(expiry_str.replace('Z', '+00:00'))
            except ValueError:
                try:
                    # Try RFC 3339 format
                    import dateutil.parser
                    expiry_time = dateutil.parser.parse(expiry_str)
                except:
                    # Fall back to simple format
                    expiry_time = datetime.datetime.strptime(expiry_str, "%Y-%m-%dT%H:%M:%S.%fZ")

            # Get current time in UTC
            now = datetime.datetime.now(datetime.timezone.utc)

            # Make sure expiry_time has timezone info
            if expiry_time.tzinfo is None:
                expiry_time = expiry_time.replace(tzinfo=datetime.timezone.utc)

            # Calculate time until expiry
            time_until_expiry = expiry_time - now

            # Check if token expires soon
            if time_until_expiry.total_seconds() < threshold_seconds:
                logger.info(f"Token expires in {time_until_expiry.total_seconds()/60:.1f} minutes (threshold: {threshold_seconds/60:.1f} minutes)")
                return True
            else:
                logger.info(f"Token is valid for {time_until_expiry.total_seconds()/60:.1f} more minutes")
                return False
        else:
            # If no expiry info, assume it might expire soon
            logger.warning("Token does not have expiry information, assuming it might expire soon")
            return True
    except Exception as e:
        logger.error(f"Error checking token expiry: {e}")
        logger.error(traceback.format_exc())
        # If we can't determine expiry, assume it might expire soon
        return True

def validate_token(token_data):
    """Validate token data and check if it's usable"""
    try:
        # Check required fields
        required_keys = ["token", "refresh_token", "token_uri", "client_id", "client_secret", "scopes"]
        if not all(key in token_data for key in required_keys):
            logger.error("Token is missing required fields")
            return False

        # Check if token is not empty
        if not token_data.get("token") or not token_data.get("refresh_token"):
            logger.error("Token or refresh_token is empty")
            return False

        # Check if token is not expired (if expiry info is available)
        if 'expiry' in token_data:
            try:
                import datetime

                # Parse expiry time
                expiry_str = token_data['expiry']
                # Handle different date formats
                try:
                    # Try ISO format first
                    expiry_time = datetime.datetime.fromisoformat(expiry_str.replace('Z', '+00:00'))
                except ValueError:
                    try:
                        # Try RFC 3339 format
                        import dateutil.parser
                        expiry_time = dateutil.parser.parse(expiry_str)
                    except:
                        # Fall back to simple format
                        expiry_time = datetime.datetime.strptime(expiry_str, "%Y-%m-%dT%H:%M:%S.%fZ")

                # Get current time in UTC
                now = datetime.datetime.now(datetime.timezone.utc)

                # Make sure expiry_time has timezone info
                if expiry_time.tzinfo is None:
                    expiry_time = expiry_time.replace(tzinfo=datetime.timezone.utc)

                # Check if token is already expired
                if expiry_time <= now:
                    logger.error("Token is already expired")
                    return False
            except Exception as e:
                logger.warning(f"Error checking token expiry: {e}")
                # Continue even if we can't check expiry

        return True
    except Exception as e:
        logger.error(f"Error validating token: {e}")
        logger.error(traceback.format_exc())
        return False

def get_token(max_attempts=5):
    """Get the token from local file or download it if not available or expiring soon

    This function will:
    1. Try to use the existing token if it's valid and not expiring soon
    2. Download a fresh token from Google Drive if needed
    3. Fall back to backup token if all else fails
    4. Continue trying indefinitely if requested

    Args:
        max_attempts: Maximum number of attempts to get a valid token

    Returns:
        dict: Token data if successful, None otherwise
    """
    attempt = 1
    continuous_mode = max_attempts <= 0  # If max_attempts <= 0, try indefinitely

    if continuous_mode:
        logger.info("Running in continuous mode - will keep trying to get a valid token indefinitely")
        effective_max_attempts = 999999  # Effectively infinite
    else:
        effective_max_attempts = max_attempts

    while attempt <= effective_max_attempts:
        logger.info(f"Getting token (attempt {attempt}/{effective_max_attempts if not continuous_mode else 'unlimited'})...")

        # Try to use existing token first
        if os.path.exists(TOKEN_FILE):
            try:
                # Load the token
                with open(TOKEN_FILE, 'r') as f:
                    token_data = json.load(f)

                # Validate token data
                if validate_token(token_data):
                    # Check if token is expiring soon
                    if is_token_expiring_soon(token_data):
                        logger.info("Token is expiring soon, downloading fresh token from Google Drive")

                        # Get list of token files first
                        token_files = get_token_list_from_folder()

                        if token_files:
                            # Process token files to find the most recent one
                            sorted_tokens = process_token_files(token_files)

                            if sorted_tokens:
                                # Get the newest token
                                newest_file = sorted_tokens[0]
                                if 'parsed_datetime' in newest_file:
                                    newest_date_str = newest_file.get('date_str')
                                    newest_time_str = newest_file.get('time_str')

                                    # Log the newest token file info
                                    logger.info(f"Newest token in Google Drive: {newest_file.get('name')} (Date: {newest_date_str}, Time: {newest_time_str})")

                                    # Download the newest token
                                    if download_token(files=sorted_tokens):
                                        # Load the newly downloaded token
                                        try:
                                            with open(TOKEN_FILE, 'r') as f:
                                                new_token_data = json.load(f)

                                            # Validate the new token
                                            if validate_token(new_token_data):
                                                logger.info("Successfully downloaded and validated fresh token")
                                                return new_token_data
                                            else:
                                                logger.warning("Downloaded token is invalid, using existing token")
                                                return token_data
                                        except Exception as load_error:
                                            logger.error(f"Error loading newly downloaded token: {load_error}")
                                            logger.warning("Using existing token despite expiry")
                                            return token_data
                                    else:
                                        logger.warning("Failed to download fresh token, using existing token")
                                        return token_data
                                else:
                                    logger.warning("Newest token file doesn't have date/time information, using existing token")
                                    return token_data
                            else:
                                logger.warning("No valid tokens found after processing, using existing token")
                                return token_data
                        else:
                            logger.warning("No token files found in Google Drive, using existing token")
                            return token_data
                    else:
                        logger.info(f"Using existing valid token from {TOKEN_FILE}")
                        return token_data
                else:
                    logger.warning("Existing token is invalid, deleting and downloading new token")

                    # Delete invalid token
                    try:
                        os.remove(TOKEN_FILE)
                        logger.info(f"Deleted invalid token file: {TOKEN_FILE}")
                    except Exception as del_error:
                        logger.warning(f"Failed to delete invalid token file: {del_error}")

                    # Download new token
                    if download_token():
                        # Load the newly downloaded token
                        try:
                            with open(TOKEN_FILE, 'r') as f:
                                new_token_data = json.load(f)

                            # Validate the new token
                            if validate_token(new_token_data):
                                logger.info("Successfully downloaded and validated new token")
                                return new_token_data
                            else:
                                logger.error("Downloaded token is invalid")
                                # Continue to next attempt
                        except Exception as load_error:
                            logger.error(f"Error loading newly downloaded token: {load_error}")
                            logger.error(traceback.format_exc())
                            # Continue to next attempt
                    else:
                        logger.error("Failed to download new token")
                        # Continue to next attempt
            except Exception as e:
                logger.error(f"Error loading token: {e}")
                logger.error(traceback.format_exc())

                # Delete potentially corrupted token file
                try:
                    os.remove(TOKEN_FILE)
                    logger.info(f"Deleted potentially corrupted token file: {TOKEN_FILE}")
                except Exception as del_error:
                    logger.warning(f"Failed to delete corrupted token file: {del_error}")

                # Try to restore from backup
                if os.path.exists(TOKEN_BACKUP_FILE):
                    try:
                        logger.info(f"Trying to restore token from backup: {TOKEN_BACKUP_FILE}")
                        with open(TOKEN_BACKUP_FILE, 'r') as f:
                            backup_data = f.read()
                        with open(TOKEN_FILE, 'w') as f:
                            f.write(backup_data)

                        # Try to load the restored token
                        with open(TOKEN_FILE, 'r') as f:
                            token_data = json.load(f)

                        # Validate the restored token
                        if validate_token(token_data):
                            logger.info("Successfully restored and validated token from backup")
                            return token_data
                        else:
                            logger.warning("Restored token from backup is invalid")
                            # Continue to download new token
                    except Exception as restore_error:
                        logger.error(f"Error restoring token from backup: {restore_error}")
                        logger.error(traceback.format_exc())
                        # Continue to download new token

                # Download new token
                logger.info("Trying to download a new token")
                if download_token():
                    # Load the newly downloaded token
                    try:
                        with open(TOKEN_FILE, 'r') as f:
                            new_token_data = json.load(f)

                        # Validate the new token
                        if validate_token(new_token_data):
                            logger.info("Successfully downloaded and validated new token")
                            return new_token_data
                        else:
                            logger.error("Downloaded token is invalid")
                            # Continue to next attempt
                    except Exception as load_error:
                        logger.error(f"Error loading newly downloaded token: {load_error}")
                        logger.error(traceback.format_exc())
                        # Continue to next attempt
                else:
                    logger.error("Failed to download new token")
                    # Continue to next attempt
        else:
            logger.info("Token file not found, downloading from Google Drive")
            if download_token():
                # Load the newly downloaded token
                try:
                    with open(TOKEN_FILE, 'r') as f:
                        new_token_data = json.load(f)

                    # Validate the new token
                    if validate_token(new_token_data):
                        logger.info("Successfully downloaded and validated new token")
                        return new_token_data
                    else:
                        logger.error("Downloaded token is invalid")
                        # Continue to next attempt
                except Exception as load_error:
                    logger.error(f"Error loading newly downloaded token: {load_error}")
                    logger.error(traceback.format_exc())
                    # Continue to next attempt
            else:
                logger.error("Failed to download token")
                # Continue to next attempt

        # Increment attempt counter and wait before retrying
        attempt += 1

        # If in continuous mode or still have attempts left, wait and retry
        if continuous_mode or attempt <= effective_max_attempts:
            retry_delay = min(DOWNLOAD_RETRY_DELAY * attempt, 60)  # Increase delay with each attempt, max 60 seconds
            logger.info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)

    # If we've exhausted all attempts, try to use backup as last resort
    if os.path.exists(TOKEN_BACKUP_FILE):
        try:
            logger.info(f"Trying to use backup token as last resort: {TOKEN_BACKUP_FILE}")
            with open(TOKEN_BACKUP_FILE, 'r') as f:
                backup_data = json.load(f)

            # Validate the backup token
            if validate_token(backup_data):
                logger.info("Using backup token as last resort")
                return backup_data
            else:
                logger.error("Backup token is also invalid")
        except Exception as backup_error:
            logger.error(f"Error using backup token: {backup_error}")
            logger.error(traceback.format_exc())

    logger.critical("Failed to get a valid token after multiple attempts")
    return None

def main():
    """Main function to test the token downloader"""
    try:
        # Print banner
        print("\n" + "=" * 80)
        print("Token Downloader Test (Service Account Version - Simplified)")
        print("=" * 80)

        # Print configuration
        print(f"\nConfiguration:")
        print(f"  Token folder ID: {TOKEN_FOLDER_ID}")
        print(f"  Token file: {TOKEN_FILE}")
        print(f"  Backup token file: {TOKEN_BACKUP_FILE}")
        print(f"  Service account file: {SERVICE_ACCOUNT_FILE}")
        print(f"  Google API available: {GOOGLE_API_AVAILABLE}")
        print(f"  Use service account: {USE_SERVICE_ACCOUNT}")

        # Check if service account file exists
        if os.path.exists(SERVICE_ACCOUNT_FILE):
            print(f"  Service account file exists: Yes")

            # Try to load service account credentials
            try:
                credentials = get_service_account_credentials()
                if credentials:
                    print(f"  Service account credentials loaded: Yes")
                    print(f"  Service account email: {credentials.service_account_email}")
                else:
                    print(f"  Service account credentials loaded: No")
            except Exception as e:
                print(f"  Service account credentials loaded: No (Error: {str(e)})")
        else:
            print(f"  Service account file exists: No")

        # List files in the folder
        print("\nListing files in Google Drive folder...")
        files = get_token_list_from_folder()

        if files:
            print(f"\nFound {len(files)} files in the folder:")
            for i, file in enumerate(files):
                file_name = file.get('name', '')
                modified_time = file.get('modifiedTime', '')
                print(f"  {i+1}. {file_name} (Modified: {modified_time})")
        else:
            print("\nNo files found in Google Drive folder")

        # Test the token downloader
        print("\nTesting token downloader...")
        token_data = get_token()

        if token_data:
            print("\nSuccess! Token retrieved successfully.")

            # Print token information
            if 'expiry' in token_data:
                print(f"  Token expiry: {token_data['expiry']}")

                # Check if token is expiring soon
                if is_token_expiring_soon(token_data):
                    print("  Token is expiring soon")
                else:
                    print("  Token is not expiring soon")

            # Print token scopes
            if 'scopes' in token_data:
                print(f"  Token scopes: {', '.join(token_data['scopes'])}")

            print("\nToken file saved to:")
            print(f"  {TOKEN_FILE}")
        else:
            print("\nError: Failed to get token")

        print("\n" + "=" * 80)
    except Exception as e:
        print(f"\nError in main: {e}")
        logger.error(f"Error in main: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    # Run the main function
    main()
