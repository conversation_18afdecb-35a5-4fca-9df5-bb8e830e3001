('D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\IFESS_Server_Web\\IFESS_Server_Web.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\IFESS_Server_Web\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\IFESS_Server_Web\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\IFESS_Server_Web\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\IFESS_Server_Web\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\IFESS_Server_Web\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\IFESS_Server_Web\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('run_server',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\run_server.py',
   'PYSOURCE'),
  ('python313.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_decimal.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program '
   'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Microsoft\\jdk-*********-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program '
   'Files\\Microsoft\\jdk-*********-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('server_web\\common\\__init__.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__init__.py',
   'DATA'),
  ('server_web\\common\\__pycache__\\__init__.cpython-310.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\__init__.cpython-310.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\__init__.cpython-312.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\__init__.cpython-312.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\__init__.cpython-313.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\__init__.cpython-313.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\db_utils.cpython-310.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\db_utils.cpython-310.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\db_utils.cpython-312.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\db_utils.cpython-312.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\db_utils.cpython-313.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\db_utils.cpython-313.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\network.cpython-310.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\network.cpython-310.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\network.cpython-312.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\network.cpython-312.pyc',
   'DATA'),
  ('server_web\\common\\__pycache__\\network.cpython-313.pyc',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\__pycache__\\network.cpython-313.pyc',
   'DATA'),
  ('server_web\\common\\db_utils.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\db_utils.py',
   'DATA'),
  ('server_web\\common\\network.py',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\common\\network.py',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014500.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014500.fdb',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014600.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014600.fdb',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014700.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_014700.fdb',
   'DATA'),
  ('server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_015547.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\client_fdb-client-monitoring_127.0.0.1\\test_db_20250419_015547.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\PTRJ_AB1_20250423_102329.FDB',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\PTRJ_AB1_20250423_102329.FDB',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb.backup_1745388300',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_105816.fdb.backup_1745388300',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_110113.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_110113.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_115319.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_115319.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\database_20250423_131517.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\database_20250423_131517.fdb',
   'DATA'),
  ('server_web\\static\\backups\\import_76b3e573\\test.txt',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\import_76b3e573\\test.txt',
   'DATA'),
  ('server_web\\static\\backups\\test_client\\test_db_20250419_020051.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_client\\test_db_20250419_020051.fdb',
   'DATA'),
  ('server_web\\static\\backups\\test_client\\test_db_20250419_020554.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_client\\test_db_20250419_020554.fdb',
   'DATA'),
  ('server_web\\static\\backups\\test_client\\test_db_20250419_020828.fdb',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_client\\test_db_20250419_020828.fdb',
   'DATA'),
  ('server_web\\static\\backups\\test_db.txt',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\backups\\test_db.txt',
   'DATA'),
  ('server_web\\static\\css\\style.css',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\css\\style.css',
   'DATA'),
  ('server_web\\static\\js\\main.js',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\js\\main.js',
   'DATA'),
  ('server_web\\static\\js\\robust_transfer.js',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\js\\robust_transfer.js',
   'DATA'),
  ('server_web\\static\\saved_queries.db',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\saved_queries.db',
   'DATA'),
  ('server_web\\static\\sql_queries\\Employee_Overtime.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\Employee_Overtime.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\Find_Diff_Job_Date_Overtime.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\Find_Diff_Job_Date_Overtime.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\Find_Helper_Hasnt_Veh_No.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\Find_Helper_Hasnt_Veh_No.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\General_Work_Has_YY.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\General_Work_Has_YY.sql',
   'DATA'),
  ('server_web\\static\\sql_queries\\test_dynamic_variables.sql',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\static\\sql_queries\\test_dynamic_variables.sql',
   'DATA'),
  ('server_web\\templates\\backups.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\backups.html',
   'DATA'),
  ('server_web\\templates\\base.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\base.html',
   'DATA'),
  ('server_web\\templates\\clients.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\clients.html',
   'DATA'),
  ('server_web\\templates\\index.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\index.html',
   'DATA'),
  ('server_web\\templates\\layout.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\layout.html',
   'DATA'),
  ('server_web\\templates\\monitoring.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\monitoring.html',
   'DATA'),
  ('server_web\\templates\\monitoring_simple.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\monitoring_simple.html',
   'DATA'),
  ('server_web\\templates\\query.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\query.html',
   'DATA'),
  ('server_web\\templates\\query_history.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\query_history.html',
   'DATA'),
  ('server_web\\templates\\standalone_monitoring.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\standalone_monitoring.html',
   'DATA'),
  ('server_web\\templates\\test_query.html',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\server_web\\templates\\test_query.html',
   'DATA'),
  ('base_library.zip',
   'D:\\Gawean Rebinmas\\Monitoring '
   'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\build\\IFESS_Server_Web\\base_library.zip',
   'DATA')],
 'python313.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
