#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script for GDriveSubfolderHandler
--------------------------------------
This script tests the GDriveSubfolderHandler class to ensure it uploads files
to the shared folder, not to the service account's own drive.
"""

import os
import sys
import logging
import argparse
from gdrive_subfolder_handler import GDriveSubfolderHandler

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_gdrive_shared_folder_handler')

def main():
    parser = argparse.ArgumentParser(description="Test GDriveSubfolderHandler")
    parser.add_argument("file_path", help="Path to the file to upload")
    parser.add_argument("--main-folder", default="Backup_PTRJ", help="Name of the main folder")
    parser.add_argument("--subfolder", default="IFESS", help="Name of the subfolder")
    parser.add_argument("--client-id", default="test_client", help="Client ID for subfolder")
    parser.add_argument("--credentials", default=None, help="Path to credentials file")

    args = parser.parse_args()

    logger.info(f"Testing GDriveSubfolderHandler with file: {args.file_path}")
    logger.info(f"Main folder: {args.main_folder}")
    logger.info(f"Subfolder: {args.subfolder}")
    logger.info(f"Client ID: {args.client_id}")
    logger.info(f"Credentials file: {args.credentials}")

    # Find credentials file if not provided
    credentials_file = args.credentials
    if not credentials_file or not os.path.exists(credentials_file):
        # Try to find the credentials file in the current directory
        possible_files = [
            "ptrj-backup-services-account.json",
            "staging_build/ptrj-backup-services-account.json",
            "../staging_build/ptrj-backup-services-account.json",
            "client_secret.json",
            "staging_build/client_secret.json",
            "../staging_build/client_secret.json"
        ]
        
        for file in possible_files:
            if os.path.exists(file):
                credentials_file = file
                logger.info(f"Found credentials file: {credentials_file}")
                break
    
    if not credentials_file or not os.path.exists(credentials_file):
        logger.error("Credentials file not found")
        return 1

    # Create handler
    handler = GDriveSubfolderHandler(credentials_file)

    # Define progress callback
    def progress_callback(bytes_uploaded, progress):
        logger.info(f"Upload progress: {bytes_uploaded} bytes ({progress}%)")

    # Create client subfolder path
    client_subfolder = f"{args.subfolder}/{args.client_id}"

    # Upload file
    result = handler.upload_file(args.file_path, args.main_folder, client_subfolder, progress_callback)

    # Print result
    logger.info(f"Upload result: {result}")
    
    return 0 if result.get('success') else 1

if __name__ == "__main__":
    sys.exit(main())
