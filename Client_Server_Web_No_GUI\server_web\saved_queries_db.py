"""
Saved Queries Database Module

This module provides functions for managing saved queries in a SQLite database.
"""

import os
import sqlite3
import time
import datetime
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Database file path
current_dir = os.path.dirname(os.path.abspath(__file__))
DB_PATH = os.path.join(current_dir, 'static', 'saved_queries.db')

def init_db():
    """Initialize the database and create tables if they don't exist"""
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(DB_PATH), exist_ok=True)
        
        # Connect to database
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Create saved_queries table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS saved_queries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT,
            query_text TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        
        conn.commit()
        conn.close()
        logger.info(f"Initialized saved queries database at {DB_PATH}")
        return True
    except Exception as e:
        logger.error(f"Error initializing database: {e}")
        return False

def add_query(name, query_text, description=""):
    """Add a new query to the database"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Check if a query with this name already exists
        cursor.execute("SELECT id FROM saved_queries WHERE name = ?", (name,))
        existing = cursor.fetchone()
        
        if existing:
            # Update existing query
            cursor.execute(
                "UPDATE saved_queries SET query_text = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (query_text, description, existing[0])
            )
            query_id = existing[0]
            logger.info(f"Updated existing query: {name} (ID: {query_id})")
        else:
            # Insert new query
            cursor.execute(
                "INSERT INTO saved_queries (name, query_text, description) VALUES (?, ?, ?)",
                (name, query_text, description)
            )
            query_id = cursor.lastrowid
            logger.info(f"Added new query: {name} (ID: {query_id})")
        
        conn.commit()
        conn.close()
        return query_id
    except Exception as e:
        logger.error(f"Error adding query: {e}")
        return None

def update_query(query_id, name=None, query_text=None, description=None):
    """Update an existing query"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Get current values
        cursor.execute("SELECT name, query_text, description FROM saved_queries WHERE id = ?", (query_id,))
        current = cursor.fetchone()
        
        if not current:
            logger.warning(f"Query with ID {query_id} not found")
            conn.close()
            return False
        
        # Use current values if new ones not provided
        name = name if name is not None else current[0]
        query_text = query_text if query_text is not None else current[1]
        description = description if description is not None else current[2]
        
        # Update the query
        cursor.execute(
            "UPDATE saved_queries SET name = ?, query_text = ?, description = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
            (name, query_text, description, query_id)
        )
        
        conn.commit()
        conn.close()
        logger.info(f"Updated query ID {query_id}")
        return True
    except Exception as e:
        logger.error(f"Error updating query: {e}")
        return False

def delete_query(query_id):
    """Delete a query from the database"""
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        
        # Delete the query
        cursor.execute("DELETE FROM saved_queries WHERE id = ?", (query_id,))
        
        if cursor.rowcount == 0:
            logger.warning(f"Query with ID {query_id} not found")
            conn.close()
            return False
        
        conn.commit()
        conn.close()
        logger.info(f"Deleted query ID {query_id}")
        return True
    except Exception as e:
        logger.error(f"Error deleting query: {e}")
        return False

def get_query(query_id):
    """Get a specific query by ID"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # Return rows as dictionaries
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, description, query_text, 
                   datetime(created_at) as created_at, 
                   datetime(updated_at) as updated_at 
            FROM saved_queries 
            WHERE id = ?
        """, (query_id,))
        
        query = cursor.fetchone()
        conn.close()
        
        if query:
            return dict(query)
        else:
            logger.warning(f"Query with ID {query_id} not found")
            return None
    except Exception as e:
        logger.error(f"Error getting query: {e}")
        return None

def get_all_queries():
    """Get all saved queries"""
    try:
        conn = sqlite3.connect(DB_PATH)
        conn.row_factory = sqlite3.Row  # Return rows as dictionaries
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, name, description, query_text, 
                   datetime(created_at) as created_at, 
                   datetime(updated_at) as updated_at 
            FROM saved_queries 
            ORDER BY updated_at DESC
        """)
        
        queries = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return queries
    except Exception as e:
        logger.error(f"Error getting all queries: {e}")
        return []

# Initialize the database when the module is imported
init_db()
