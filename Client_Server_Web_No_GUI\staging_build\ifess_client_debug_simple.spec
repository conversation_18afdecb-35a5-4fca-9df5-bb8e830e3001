# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\ifess_client_debug_simple.py'],
    pathex=[],
    binaries=[],
    datas=[('D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\client_config.json', '.'), ('D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\mega_client_py310.py', '.'), ('D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\gdrive_client_module.py', '.'), ('D:\\Gawean Rebinmas\\Monitoring Database\\ifess\\Client_Server_Web_No_GUI\\staging_build\\ptrj-backup-services-account.json', '.')],
    hiddenimports=['mega', 'fdb', 'socket', 'json', 'requests', 'googleapiclient', 'googleapiclient.discovery', 'google.oauth2', 'google.oauth2.service_account'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ifess_client_debug_simple',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
