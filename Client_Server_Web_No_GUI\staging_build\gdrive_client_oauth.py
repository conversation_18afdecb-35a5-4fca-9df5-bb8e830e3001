"""
Google Drive Client Module with OAuth Authentication for IFESS
------------------------------------------------------------
This module handles Google Drive uploads for the IFESS client applications using OAuth authentication.
It creates a folder structure in the format:
root/Backup_PTRJ/IFESS/{client_id}/{client_id}_date_time.zip

This version uses OAuth authentication with a personal Google account instead of a service account,
which allows files to be owned by the personal account and stored in the personal Google Drive.

Features:
- Auto-login with saved credentials
- Auto-refresh of expired OAuth tokens
- Files are owned by the personal account (<EMAIL>)
- Compatible with compiled applications
"""

import os
import sys
import time
import json
import logging
import traceback
import tempfile
import shutil
import webbrowser
import zipfile
from datetime import datetime
from pathlib import Path

# Folder structure constants
BACKUP_FOLDER_NAME = "Backup_PTRJ"
IFESS_FOLDER_NAME = "IFESS"

# Setup logging
log_dir = os.path.dirname(os.path.abspath(__file__))
log_file = os.path.join(log_dir, "gdrive_client.log")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("gdrive_client_oauth")

# Determine base directory - whether running as script or frozen executable
if getattr(sys, 'frozen', False):
    # Running in a bundled executable
    BASE_DIR = os.path.dirname(sys.executable)
else:
    # Running as a script
    BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# File paths for credentials and tokens
CLIENT_SECRETS_FILE = os.path.join(BASE_DIR, "client_secrets.json")
TOKEN_FILE = os.path.join(BASE_DIR, "token.json")

# Try to import Google Drive API
try:
    from googleapiclient.discovery import build
    from googleapiclient.http import MediaFileUpload
    from google_auth_oauthlib.flow import InstalledAppFlow
    from google.auth.transport.requests import Request
    from google.oauth2.credentials import Credentials
    GDRIVE_API_AVAILABLE = True
except ImportError:
    GDRIVE_API_AVAILABLE = False
    logger.error("Google Drive API not available. Please install with pip:")
    logger.error("pip install google-auth google-auth-oauthlib google-api-python-client")

class GDriveClient:
    """Handles Google Drive operations for IFESS client using OAuth authentication"""

    # Scopes needed for Google Drive access
    SCOPES = ['https://www.googleapis.com/auth/drive.file']

    def __init__(self, client_id, client_secrets_file=None):
        """Initialize Google Drive Client with OAuth authentication

        Args:
            client_id: Client ID for folder naming
            client_secrets_file: Path to OAuth client secrets JSON file
        """
        self.client_id = client_id
        self.client_secrets_file = client_secrets_file or CLIENT_SECRETS_FILE
        self.credentials = None
        self.service = None
        self.token_file = TOKEN_FILE

        logger.info(f"Initializing Google Drive client for client_id: {client_id}")

        # Check if Google Drive API is available
        if not GDRIVE_API_AVAILABLE:
            logger.error("Google Drive API not available - please install required packages")
            return

        # Find client secrets file if not provided
        if not self.client_secrets_file or not os.path.exists(self.client_secrets_file):
            logger.info(f"Client secrets file not found at {self.client_secrets_file}, searching for it...")
            self.client_secrets_file = self._find_client_secrets_file()
        else:
            logger.info(f"Using provided client secrets file: {self.client_secrets_file}")

        # Initialize Google Drive service
        if self.client_secrets_file and os.path.exists(self.client_secrets_file):
            try:
                logger.info(f"Loading credentials from: {self.client_secrets_file}")
                self._load_credentials()
                logger.info(f"Google Drive client initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing Google Drive client: {e}")
                logger.error(traceback.format_exc())
        else:
            logger.warning(f"Google Drive client secrets file not found - uploads will fail")

    def _find_client_secrets_file(self):
        """Find client secrets JSON file in various locations"""
        possible_paths = [
            # Current directory
            "client_secrets.json",
            # Executable directory
            os.path.join(BASE_DIR, "client_secrets.json"),
            # Config directory
            os.path.join(BASE_DIR, "config", "client_secrets.json"),
            # Additional paths
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "client_secrets.json"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "staging_build", "client_secrets.json"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Client_Server_Web_No_GUI", "client_secrets.json"),
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "Client_Server_Web_No_GUI", "staging_build", "client_secrets.json")
        ]

        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Found client secrets file at: {path}")
                return path

        logger.warning("Could not find client secrets JSON file")
        return None

    def _load_credentials(self):
        """Load or refresh OAuth credentials"""
        try:
            creds = None
            # Check if token file exists
            if os.path.exists(self.token_file):
                logger.info(f"Loading token from: {self.token_file}")
                try:
                    creds = Credentials.from_authorized_user_info(
                        json.load(open(self.token_file, 'r')),
                        self.SCOPES
                    )
                except Exception as e:
                    logger.error(f"Error loading token: {e}")
                    logger.error(traceback.format_exc())
                    creds = None

            # If there are no valid credentials, get new ones
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    logger.info("Refreshing expired token...")
                    try:
                        creds.refresh(Request())
                        logger.info("Token refreshed successfully")
                    except Exception as e:
                        logger.error(f"Error refreshing token: {e}")
                        logger.error(traceback.format_exc())
                        creds = None

                # If still no valid credentials, need to get new ones
                if not creds or not creds.valid:
                    logger.info("No valid credentials found, starting OAuth flow...")
                    if not os.path.exists(self.client_secrets_file):
                        raise Exception(f"Client secrets file not found: {self.client_secrets_file}")

                    # Create the flow
                    flow = InstalledAppFlow.from_client_secrets_file(
                        self.client_secrets_file, self.SCOPES)

                    # Run the OAuth flow
                    logger.info("Opening browser for authentication...")
                    print("\n\n==== ATTENTION ====")
                    print("A browser window will open for Google Drive authentication.")
                    print("Please log in with your Google account (<EMAIL>)")
                    print("and grant the requested permissions.")
                    print("==================\n\n")

                    # Try to use automated login with Selenium if available
                    try:
                        import selenium
                        from selenium import webdriver
                        from selenium.webdriver.common.by import By
                        from selenium.webdriver.support.ui import WebDriverWait
                        from selenium.webdriver.support import expected_conditions as EC
                        from selenium.common.exceptions import TimeoutException

                        logger.info("Using automated login with Selenium")

                        # Get the authorization URL
                        auth_url = flow.authorization_url()[0]

                        # Open browser and navigate to the auth URL
                        options = webdriver.ChromeOptions()
                        options.add_argument("--start-maximized")
                        driver = webdriver.Chrome(options=options)
                        driver.get(auth_url)

                        # Wait for email field and enter email
                        try:
                            email_field = WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.ID, "identifierId"))
                            )
                            email_field.send_keys("<EMAIL>")

                            # Click Next
                            next_button = driver.find_element(By.ID, "identifierNext")
                            next_button.click()

                            # Wait for password field and enter password
                            password_field = WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.NAME, "password"))
                            )
                            password_field.send_keys("windows0819")

                            # Click Next
                            password_next = driver.find_element(By.ID, "passwordNext")
                            password_next.click()

                            # Wait for consent page if it appears
                            try:
                                consent_button = WebDriverWait(driver, 10).until(
                                    EC.presence_of_element_located((By.ID, "submit_approve_access"))
                                )
                                consent_button.click()
                            except TimeoutException:
                                # Consent page might not appear if already granted
                                logger.info("No consent page appeared, continuing...")

                            # Wait for the redirect URL with the authorization code
                            WebDriverWait(driver, 30).until(
                                lambda d: "localhost" in d.current_url and "code=" in d.current_url
                            )

                            # Extract the authorization code from the URL
                            auth_code = driver.current_url.split("code=")[1].split("&")[0]

                            # Exchange the authorization code for credentials
                            flow.fetch_token(code=auth_code)
                            creds = flow.credentials

                            logger.info("Successfully authenticated with automated login")
                        except Exception as auto_login_error:
                            logger.error(f"Error during automated login: {auto_login_error}")
                            logger.error(traceback.format_exc())
                            driver.quit()
                            raise
                        finally:
                            # Close the browser
                            driver.quit()

                    except ImportError:
                        logger.warning("Selenium not available, falling back to manual authentication")
                        # Try to run local server flow
                        try:
                            creds = flow.run_local_server(port=0)
                        except Exception as local_server_error:
                            logger.error(f"Error running local server flow: {local_server_error}")
                            logger.error("Falling back to console flow...")

                            # Fall back to console flow
                            try:
                                creds = flow.run_console()
                            except Exception as console_error:
                                logger.error(f"Error running console flow: {console_error}")
                                raise Exception("Failed to authenticate with Google Drive")
                    except Exception as selenium_error:
                        logger.error(f"Error setting up automated login: {selenium_error}")
                        logger.error(traceback.format_exc())

                        # Try to run local server flow
                        try:
                            creds = flow.run_local_server(port=0)
                        except Exception as local_server_error:
                            logger.error(f"Error running local server flow: {local_server_error}")
                            logger.error("Falling back to console flow...")

                            # Fall back to console flow
                            try:
                                creds = flow.run_console()
                            except Exception as console_error:
                                logger.error(f"Error running console flow: {console_error}")
                                raise Exception("Failed to authenticate with Google Drive")

                    logger.info("Authentication successful")

                # Save the credentials for the next run
                if creds and creds.valid:
                    logger.info(f"Saving token to: {self.token_file}")
                    with open(self.token_file, 'w') as token:
                        token.write(creds.to_json())
                    logger.info("Token saved successfully")

            # Set credentials
            self.credentials = creds

            # Create service
            logger.info("Building Google Drive API service...")
            self.service = build('drive', 'v3', credentials=self.credentials)
            logger.info("Google Drive API service created successfully")

        except Exception as e:
            logger.error(f"Error loading credentials: {e}")
            logger.error(traceback.format_exc())
            self.credentials = None
            raise

    def _compress_file(self, file_path):
        """Compress a file using ZIP format

        Args:
            file_path: Path to the file to compress

        Returns:
            str: Path to the compressed file
        """
        logger.info(f"Compressing file: {file_path}")

        try:
            # Create a temporary directory for the compressed file
            temp_dir = tempfile.mkdtemp(prefix="ifess_gdrive_")
            logger.info(f"Created temporary directory: {temp_dir}")

            # Get the original file name and create a zip file name
            original_filename = os.path.basename(file_path)
            zip_filename = f"{os.path.splitext(original_filename)[0]}.zip"
            zip_path = os.path.join(temp_dir, zip_filename)

            logger.info(f"Creating ZIP file: {zip_path}")

            # Create the ZIP file
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add the file to the ZIP with its original name
                logger.info(f"Adding {original_filename} to ZIP")
                zipf.write(file_path, arcname=original_filename)

            # Check if the ZIP file was created successfully
            if not os.path.exists(zip_path):
                raise Exception(f"Failed to create ZIP file: {zip_path}")

            # Get file sizes for logging
            original_size = os.path.getsize(file_path)
            compressed_size = os.path.getsize(zip_path)
            compression_ratio = (1 - (compressed_size / original_size)) * 100 if original_size > 0 else 0

            logger.info(f"Compression complete:")
            logger.info(f"  Original size: {original_size / (1024*1024):.2f} MB")
            logger.info(f"  Compressed size: {compressed_size / (1024*1024):.2f} MB")
            logger.info(f"  Compression ratio: {compression_ratio:.2f}%")

            return zip_path

        except Exception as e:
            logger.error(f"Error compressing file: {e}")
            logger.error(traceback.format_exc())
            raise

    def _find_or_create_folder(self, folder_name, parent_id=None):
        """Find a folder or create it if it doesn't exist

        Args:
            folder_name: Name of the folder to find or create
            parent_id: ID of the parent folder (None for root)

        Returns:
            str: Folder ID
        """
        if not self.service:
            logger.error("Google Drive service not initialized")
            return None

        try:
            # Search for existing folder
            query = f"name='{folder_name}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
            if parent_id:
                query += f" and '{parent_id}' in parents"

            logger.info(f"Searching for folder: {folder_name}" + (f" in parent: {parent_id}" if parent_id else " in root"))
            logger.info(f"Query: {query}")

            results = self.service.files().list(
                q=query,
                spaces='drive',
                fields='files(id, name, parents)'
            ).execute()

            items = results.get('files', [])
            logger.info(f"Found {len(items)} matching folders")

            # If folder exists, return its ID
            if items:
                folder_id = items[0]['id']
                parents = items[0].get('parents', [])
                parent_str = f" in {parents}" if parents else ""
                logger.info(f"Found existing folder: {folder_name} (ID: {folder_id}){parent_str}")
                return folder_id

            # If folder doesn't exist, create it
            logger.info(f"Folder '{folder_name}' not found, creating it" + (f" in parent: {parent_id}" if parent_id else " in root"))
            folder_metadata = {
                'name': folder_name,
                'mimeType': 'application/vnd.google-apps.folder'
            }

            if parent_id:
                folder_metadata['parents'] = [parent_id]

            logger.info(f"Creating folder with metadata: {folder_metadata}")

            folder = self.service.files().create(
                body=folder_metadata,
                fields='id,name,parents'
            ).execute()

            folder_id = folder.get('id')
            parents = folder.get('parents', [])
            parent_str = f" in {parents}" if parents else ""
            logger.info(f"Successfully created folder: {folder_name} (ID: {folder_id}){parent_str}")
            return folder_id

        except Exception as e:
            logger.error(f"Error creating/finding folder {folder_name}: {e}")
            logger.error(traceback.format_exc())
            return None

    def _create_folder_structure(self):
        """Create folder structure: Backup_PTRJ/IFESS/{client_id}/

        Returns:
            str: ID of the client folder
        """
        if not self.service:
            logger.error("Google Drive service not initialized")
            return None

        try:
            # Create or find Backup_PTRJ folder
            backup_folder_id = self._find_or_create_folder(BACKUP_FOLDER_NAME)
            if not backup_folder_id:
                logger.error(f"Failed to find or create {BACKUP_FOLDER_NAME} folder")
                return None

            logger.info(f"Using {BACKUP_FOLDER_NAME} folder with ID: {backup_folder_id}")

            # Create or find IFESS folder
            ifess_folder_id = self._find_or_create_folder(IFESS_FOLDER_NAME, backup_folder_id)
            if not ifess_folder_id:
                logger.error(f"Failed to find or create {IFESS_FOLDER_NAME} folder")
                return None

            logger.info(f"Using {IFESS_FOLDER_NAME} folder with ID: {ifess_folder_id}")

            # Create or find client folder
            client_folder_id = self._find_or_create_folder(self.client_id, ifess_folder_id)
            if not client_folder_id:
                logger.error(f"Failed to find or create {self.client_id} folder")
                return None

            logger.info(f"Using client folder {self.client_id} with ID: {client_folder_id}")
            logger.info(f"Successfully created/found folder structure: {BACKUP_FOLDER_NAME}/{IFESS_FOLDER_NAME}/{self.client_id}")
            return client_folder_id

        except Exception as e:
            logger.error(f"Error creating folder structure: {e}")
            logger.error(traceback.format_exc())
            return None

    def upload_file(self, file_path, progress_callback=None):
        """Upload file to Google Drive

        Args:
            file_path: Path to the file to upload
            progress_callback: Callback function for progress updates

        Returns:
            dict: Information about the uploaded file
        """
        if not GDRIVE_API_AVAILABLE:
            logger.error("Google Drive API not available")
            return {'success': False, 'error': 'Google Drive API not available'}

        logger.info(f"===== STARTING UPLOAD TO GOOGLE DRIVE =====")
        logger.info(f"File path: {file_path}")

        # Validate service
        if not self.service:
            logger.warning("Service not initialized, trying to initialize again")
            try:
                self._load_credentials()
                if not self.service:
                    logger.error("Failed to initialize Google Drive service after retry")
                    return {'success': False, 'error': 'Failed to initialize Google Drive service'}
            except Exception as e:
                logger.error(f"Error loading credentials: {e}")
                logger.error(traceback.format_exc())
                return {'success': False, 'error': f'Error loading credentials: {str(e)}'}

        # Validate file
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return {'success': False, 'error': f'File not found: {file_path}'}

        if not os.path.isfile(file_path):
            logger.error(f"Path is not a file: {file_path}")
            return {'success': False, 'error': f'Path is not a file: {file_path}'}

        # Validate file size
        try:
            file_size = os.path.getsize(file_path)
            logger.info(f"File size: {file_size} bytes ({file_size / (1024*1024):.2f} MB)")

            if file_size == 0:
                logger.error(f"File is empty (0 bytes): {file_path}")
                return {'success': False, 'error': f'File is empty (0 bytes): {file_path}'}

            if file_size > 1024 * 1024 * 1024:  # 1GB
                logger.warning(f"File is very large ({file_size / (1024*1024*1024):.2f} GB), upload may take a long time")
        except Exception as e:
            logger.error(f"Error checking file size: {e}")
            return {'success': False, 'error': f'Error checking file size: {str(e)}'}

        # Start upload process
        upload_start_time = time.time()
        compressed_file_path = None
        temp_dir = None

        try:
            # Compress the file first
            logger.info("Compressing database file before upload...")
            compressed_file_path = self._compress_file(file_path)
            temp_dir = os.path.dirname(compressed_file_path)

            # Create folder structure
            client_folder_id = self._create_folder_structure()
            if not client_folder_id:
                logger.error("Failed to create folder structure")
                return {'success': False, 'error': 'Failed to create folder structure'}

            # Prepare file metadata
            timestamp = datetime.now().strftime("%d-%m-%Y")
            upload_file_name = f"{self.client_id}_{timestamp}.zip"

            file_metadata = {
                'name': upload_file_name,
                'description': f'IFESS Database backup {timestamp} (Compressed)',
                'parents': [client_folder_id]
            }
            logger.info(f"File metadata: {file_metadata}")

            # Setup media upload with resumable
            logger.info("Preparing media upload...")
            try:
                media = MediaFileUpload(
                    compressed_file_path,
                    mimetype='application/zip',
                    resumable=True,
                    chunksize=1024*1024  # 1MB per chunk
                )
            except Exception as e:
                logger.error(f"Error creating MediaFileUpload: {e}")
                logger.error(traceback.format_exc())
                return {'success': False, 'error': f'Error creating MediaFileUpload: {str(e)}'}

            # Create request
            logger.info("Creating upload request...")
            try:
                request = self.service.files().create(
                    body=file_metadata,
                    media_body=media,
                    fields='id,name,webViewLink'
                )
            except Exception as e:
                logger.error(f"Error creating upload request: {e}")
                logger.error(traceback.format_exc())
                return {'success': False, 'error': f'Error creating upload request: {str(e)}'}

            # Upload with progress
            logger.info("Starting upload...")
            response = None
            last_progress = 0
            last_progress_time = time.time()
            stall_count = 0

            try:
                while response is None:
                    try:
                        status, response = request.next_chunk()

                        # Reset stall counter because we successfully got a chunk
                        stall_count = 0

                        if status:
                            current_time = time.time()
                            progress = int(status.progress() * 100)

                            # Only log if progress changed or more than 5 seconds passed
                            if progress != last_progress or (current_time - last_progress_time) > 5:
                                elapsed = current_time - upload_start_time
                                bytes_uploaded = int(file_size * (progress / 100))
                                bytes_remaining = file_size - bytes_uploaded

                                # Calculate upload speed (bytes per second)
                                if elapsed > 0:
                                    upload_speed = bytes_uploaded / elapsed
                                    # Estimate remaining time
                                    if upload_speed > 0:
                                        eta_seconds = bytes_remaining / upload_speed
                                        eta_str = f"{int(eta_seconds / 60)}m {int(eta_seconds % 60)}s"
                                    else:
                                        eta_str = "unknown"
                                else:
                                    upload_speed = 0
                                    eta_str = "calculating..."

                                logger.info(f"[GDRIVE] Upload progress: {progress}% ({bytes_uploaded}/{file_size} bytes, {upload_speed/1024:.2f} KB/s, ETA: {eta_str})")

                                # Log for debug client to capture
                                print(f"[GDRIVE] Uploaded: {bytes_uploaded / (1024*1024):.2f} MB of {file_size / (1024*1024):.2f} MB")
                                print(f"[GDRIVE] Progress: {progress}%")
                                print(f"[GDRIVE] Speed: {upload_speed/1024:.2f} KB/s, ETA: {eta_str}")

                                if progress_callback:
                                    progress_callback(progress)

                                last_progress = progress
                                last_progress_time = current_time
                    except Exception as chunk_error:
                        stall_count += 1
                        logger.warning(f"Error during chunk upload (attempt {stall_count}): {chunk_error}")

                        if stall_count >= 5:
                            raise Exception(f"Upload stalled after {stall_count} failed attempts: {chunk_error}")

                        # Wait before retrying
                        time.sleep(2)
            except Exception as upload_error:
                logger.error(f"Error during upload process: {upload_error}")
                logger.error(traceback.format_exc())
                return {'success': False, 'error': f'Error during upload process: {str(upload_error)}'}

            if not response:
                logger.error("Upload failed: No response received")
                return {'success': False, 'error': 'Upload failed: No response received'}

            logger.info(f"Upload complete, response: {response}")
            upload_duration = time.time() - upload_start_time
            logger.info(f"Upload duration: {upload_duration:.2f} seconds")

            # Clean up temporary files
            if compressed_file_path and os.path.exists(compressed_file_path):
                try:
                    logger.info(f"Cleaning up temporary compressed file: {compressed_file_path}")
                    os.remove(compressed_file_path)
                except Exception as cleanup_error:
                    logger.warning(f"Error cleaning up compressed file: {cleanup_error}")

            if temp_dir and os.path.exists(temp_dir):
                try:
                    logger.info(f"Cleaning up temporary directory: {temp_dir}")
                    shutil.rmtree(temp_dir)
                except Exception as cleanup_error:
                    logger.warning(f"Error cleaning up temporary directory: {cleanup_error}")

            # Set file permission to be accessible via link
            logger.info("Setting file permissions...")
            try:
                self.service.permissions().create(
                    fileId=response['id'],
                    body={'type': 'anyone', 'role': 'reader'}
                ).execute()
            except Exception as perm_error:
                logger.error(f"Error setting file permissions: {perm_error}")
                logger.error(traceback.format_exc())
                # Continue anyway, we still have the file uploaded

            # Get sharing link
            logger.info("Getting sharing link...")
            try:
                file = self.service.files().get(
                    fileId=response['id'],
                    fields='id,name,webViewLink,webContentLink'
                ).execute()
            except Exception as link_error:
                logger.error(f"Error getting file links: {link_error}")
                logger.error(traceback.format_exc())
                # Return with limited info
                return {
                    'success': True,
                    'file_id': response.get('id'),
                    'name': response.get('name'),
                    'web_view_link': response.get('webViewLink'),
                    'upload_duration': upload_duration
                }

            logger.info(f"===== UPLOAD SUCCESSFUL =====")
            logger.info(f"File: {file.get('name')}")
            logger.info(f"File ID: {file.get('id')}")
            logger.info(f"View Link: {file.get('webViewLink')}")
            logger.info(f"Download Link: {file.get('webContentLink')}")
            logger.info(f"Duration: {upload_duration:.2f} seconds")

            return {
                'success': True,
                'file_id': file.get('id'),
                'name': file.get('name'),
                'web_view_link': file.get('webViewLink'),
                'web_content_link': file.get('webContentLink'),
                'upload_duration': upload_duration
            }

        except Exception as e:
            upload_duration = time.time() - upload_start_time
            logger.error(f"===== UPLOAD ERROR =====")
            logger.error(f"Error uploading file: {e}")
            logger.error(f"Upload duration before error: {upload_duration:.2f} seconds")
            logger.error(traceback.format_exc())

            # Clean up temporary files even if there was an error
            if compressed_file_path and os.path.exists(compressed_file_path):
                try:
                    logger.info(f"Cleaning up temporary compressed file after error: {compressed_file_path}")
                    os.remove(compressed_file_path)
                except Exception as cleanup_error:
                    logger.warning(f"Error cleaning up compressed file after error: {cleanup_error}")

            if temp_dir and os.path.exists(temp_dir):
                try:
                    logger.info(f"Cleaning up temporary directory after error: {temp_dir}")
                    shutil.rmtree(temp_dir)
                except Exception as cleanup_error:
                    logger.warning(f"Error cleaning up temporary directory after error: {cleanup_error}")

            return {
                'success': False,
                'error': str(e),
                'upload_duration': upload_duration
            }

# Test function
def test_upload(file_path, client_secrets_file=None):
    """Test function to upload a file to Google Drive"""
    client = GDriveClient("test_client", client_secrets_file)

    def progress_callback(progress):
        print(f"Upload progress: {progress}%")

    result = client.upload_file(file_path, progress_callback)
    print(f"Upload result: {result}")

if __name__ == "__main__":
    # If run directly, perform a test upload
    import argparse

    parser = argparse.ArgumentParser(description="Test Google Drive upload with OAuth")
    parser.add_argument("file_path", help="Path to file to upload")
    parser.add_argument("--client_secrets", help="Path to client_secrets.json file")

    args = parser.parse_args()

    test_upload(args.file_path, args.client_secrets)
