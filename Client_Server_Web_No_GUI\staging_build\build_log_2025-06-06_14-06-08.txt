IFESS Client Suite - Comprehensive Build Log 
Build started: 06/06/2025 14:06:08,13 
================================================ 
 
Current directory: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build 
[1/7] Performing pre-build checks... 
Python 3.13.3
PyInstaller command line access verified 
WARNING: UPX not found - compression will be disabled 
Checking source files... 
Found: ifess_client_hidden.py 
Found: ifess_config_gui.py 
Pre-build checks completed successfully 
[2/7] Cleaning build environment... 
Removing old dist directory... 
Removing old build directory... 
Build environment cleaned 
[3/7] Checking and installing dependencies... 
Installing/updating Python dependencies... 
Installing: google-api-python-client 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-api-python-client in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.171.0)
Requirement already satisfied: httplib2<1.0.0,>=0.19.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (0.22.0)
Requirement already satisfied: google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (2.40.3)
Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (0.2.0)
Requirement already satisfied: google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (2.24.2)
Requirement already satisfied: uritemplate<5,>=3.0.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-python-client) (4.1.1)
Requirement already satisfied: googleapis-common-protos<2.0.0,>=1.56.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.70.0)
Requirement already satisfied: protobuf!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0,>=3.19.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (5.29.5)
Requirement already satisfied: proto-plus<2.0.0,>=1.22.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.26.1)
Requirement already satisfied: requests<3.0.0,>=2.18.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (2.32.3)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (4.9)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from httplib2<1.0.0,>=0.19.0->google-api-python-client) (3.2.1)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests<3.0.0,>=2.18.0->google-api-core!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0,>=1.31.5->google-api-python-client) (2025.1.31)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth!=2.24.0,!=2.25.0,<3.0.0,>=1.32.0->google-api-python-client) (0.6.1)
Installing: google-auth 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.40.3)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth) (0.6.1)
Installing: google-auth-oauthlib 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth-oauthlib in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (1.2.2)
Requirement already satisfied: google-auth>=2.15.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-oauthlib) (2.40.3)
Requirement already satisfied: requests-oauthlib>=0.7.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-oauthlib) (2.0.0)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth>=2.15.0->google-auth-oauthlib) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth>=2.15.0->google-auth-oauthlib) (0.6.1)
Requirement already satisfied: oauthlib>=3.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.2.2)
Requirement already satisfied: requests>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests-oauthlib>=0.7.0->google-auth-oauthlib) (2.32.3)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests>=2.0.0->requests-oauthlib>=0.7.0->google-auth-oauthlib) (2025.1.31)
Installing: google-auth-httplib2 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: google-auth-httplib2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (0.2.0)
Requirement already satisfied: google-auth in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-httplib2) (2.40.3)
Requirement already satisfied: httplib2>=0.19.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth-httplib2) (0.22.0)
Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from httplib2>=0.19.0->google-auth-httplib2) (3.2.1)
Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (5.5.2)
Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (0.4.2)
Requirement already satisfied: rsa<5,>=3.1.4 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from google-auth->google-auth-httplib2) (4.9)
Requirement already satisfied: pyasn1>=0.1.3 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from rsa<5,>=3.1.4->google-auth->google-auth-httplib2) (0.6.1)
Installing: requests 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: requests in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.32.3)
Requirement already satisfied: charset-normalizer<4,>=2 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (3.4.1)
Requirement already satisfied: idna<4,>=2.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (3.10)
Requirement already satisfied: urllib3<3,>=1.21.1 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (1.26.20)
Requirement already satisfied: certifi>=2017.4.17 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from requests) (2025.1.31)
Installing: fdb 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: fdb in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (2.0.3)
Requirement already satisfied: firebird-base~=2.0 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from fdb) (2.0.2)
Requirement already satisfied: python-dateutil~=2.8 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from fdb) (2.9.0.post0)
Requirement already satisfied: protobuf~=5.29 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from firebird-base~=2.0->fdb) (5.29.5)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (from python-dateutil~=2.8->fdb) (1.17.0)
Installing: schedule 
Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: schedule in c:\users\<USER>\appdata\local\packages\pythonsoftwarefoundation.python.3.13_qbz5n2kfra8p0\localcache\local-packages\python313\site-packages (1.2.2)
Dependencies check completed 
[4/7] Building IFESS Hidden Client... 
Building ifess_client_hidden.exe... 
485 INFO: PyInstaller: 6.14.0, contrib hooks: 2025.4
486 INFO: Python: 3.13.3
579 INFO: Platform: Windows-11-10.0.26100-SP0
580 INFO: Python environment: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0
592 INFO: Module search paths (PYTHONPATH):
['D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.zip',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Pythonwin',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build']
1828 INFO: Appending 'datas' from .spec
1830 INFO: checking Analysis
1831 INFO: Building Analysis because Analysis-00.toc is non existent
1831 INFO: Running Analysis Analysis-00.toc
1831 INFO: Target bytecode optimization level: 0
1831 INFO: Initializing module dependency graph...
1833 INFO: Initializing module graph hook caches...
1848 INFO: Analyzing modules for base_library.zip ...
4896 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
6261 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
7592 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
9086 INFO: Caching module dependency graph...
9138 INFO: Looking for Python shared library...
9139 INFO: Using Python shared library: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\python313.dll
9139 INFO: Analyzing D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_client_hidden.py
9285 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
9403 INFO: Processing standard module hook 'hook-pytz.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
9707 INFO: Processing standard module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
10059 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
10722 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
10841 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
12238 INFO: Processing standard module hook 'hook-google.api_core.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
12349 INFO: Processing standard module hook 'hook-cryptography.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
14640 INFO: hook-cryptography: cryptography does not seem to be using dynamically linked OpenSSL.
15576 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
15625 INFO: Processing pre-safe-import-module hook 'hook-urllib3.packages.six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
16610 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
16728 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
16730 INFO: SetuptoolsInfo: initializing cached setuptools info...
22973 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
23322 INFO: Processing standard module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
23815 INFO: Processing standard module hook 'hook-setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
23830 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
23866 INFO: Processing pre-safe-import-module hook 'hook-jaraco.functools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
23867 INFO: Setuptools: 'jaraco.functools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.functools'!
23875 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
23876 INFO: Setuptools: 'more_itertools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.more_itertools'!
24105 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
24332 INFO: Processing pre-safe-import-module hook 'hook-jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
24332 INFO: Setuptools: 'jaraco.text' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.text'!
24353 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
24358 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
24363 INFO: Processing pre-safe-import-module hook 'hook-jaraco.context.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
24365 INFO: Setuptools: 'jaraco.context' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.context'!
24382 INFO: Processing pre-safe-import-module hook 'hook-backports.tarfile.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
24386 INFO: Setuptools: 'backports.tarfile' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.backports.tarfile'!
24527 INFO: Processing standard module hook 'hook-backports.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
24638 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
24639 INFO: Setuptools: 'importlib_metadata' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.importlib_metadata'!
24669 INFO: Processing standard module hook 'hook-setuptools._vendor.importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
24672 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
24673 INFO: Setuptools: 'zipp' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.zipp'!
25059 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
25060 INFO: Setuptools: 'tomli' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.tomli'!
25890 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
26789 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
26801 INFO: Processing standard module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
27067 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28141 INFO: Processing standard module hook 'hook-httplib2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
28635 INFO: Processing standard module hook 'hook-jinja2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
29385 INFO: Processing standard module hook 'hook-googleapiclient.model.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
30286 INFO: Processing standard module hook 'hook-dateutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
30476 INFO: Processing pre-safe-import-module hook 'hook-six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
30782 INFO: Analyzing hidden import 'sqlite3'
30783 INFO: Processing standard module hook 'hook-sqlite3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
31724 INFO: Analyzing hidden import 'fdb'
32443 INFO: Analyzing hidden import 'firebirdsql'
32653 INFO: Processing standard module hook 'hook-zoneinfo.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
32732 INFO: Processing standard module hook 'hook-Crypto.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
34055 INFO: Analyzing hidden import 'mega'
34344 INFO: Processing module hooks (post-graph stage)...
34520 INFO: Processing standard module hook 'hook-tzdata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
35556 INFO: Performing binary vs. data reclassification (1846 entries)
35803 INFO: Looking for ctypes DLLs
36033 INFO: Analyzing run-time hooks ...
36046 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
36053 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
36059 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
36063 INFO: Including run-time hook 'pyi_rth_setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
36069 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
36078 INFO: Including run-time hook 'pyi_rth_cryptography_openssl.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
36321 INFO: Creating base_library.zip...
36524 INFO: Looking for dynamic libraries
39151 INFO: Extra DLL search directories (AddDllDirectory): []
39151 INFO: Extra DLL search directories (PATH): []
40488 INFO: Warnings written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_client_hidden_comprehensive\warn-ifess_client_hidden_comprehensive.txt
40607 INFO: Graph cross-reference written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_client_hidden_comprehensive\xref-ifess_client_hidden_comprehensive.html
40711 INFO: checking PYZ
40712 INFO: Building PYZ because PYZ-00.toc is non existent
40712 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_client_hidden_comprehensive\PYZ-00.pyz
42453 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_client_hidden_comprehensive\PYZ-00.pyz completed successfully.
42552 INFO: checking PKG
42552 INFO: Building PKG because PKG-00.toc is non existent
42553 INFO: Building PKG (CArchive) ifess_client_hidden.pkg
50683 INFO: Building PKG (CArchive) ifess_client_hidden.pkg completed successfully.
50725 INFO: Bootloader C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\bootloader\Windows-64bit-intel\runw.exe
50725 INFO: checking EXE
50725 INFO: Building EXE because EXE-00.toc is non existent
50725 INFO: Building EXE from EXE-00.toc
50725 INFO: Copying bootloader EXE to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ifess_client_hidden.exe
50750 INFO: Copying icon to EXE
51537 INFO: Copying 0 resources to EXE
51537 INFO: Embedding manifest in EXE
52178 INFO: Appending PKG archive to EXE
52499 INFO: Fixing EXE headers
58925 INFO: Building EXE from EXE-00.toc completed successfully.
58979 INFO: Build complete! The results are available in: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_backup.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_module.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secret.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secrets.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\common -> common
SUCCESS: ifess_client_hidden.exe built successfully 
File size: ******** bytes 
[5/7] Skipping debug client build (using batch file for debugging)... 
Debug functionality will be provided by debug_hidden_client.bat 
[6/7] Building IFESS Config GUI... 
Building ifess_config_gui.exe... 
454 INFO: PyInstaller: 6.14.0, contrib hooks: 2025.4
456 INFO: Python: 3.13.3
547 INFO: Platform: Windows-11-10.0.26100-SP0
547 INFO: Python environment: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0
556 INFO: Module search paths (PYTHONPATH):
['D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\python313.zip',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\DLLs',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\win32\\lib',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\Pythonwin',
 'C:\\Program '
 'Files\\WindowsApps\\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\\Lib\\site-packages',
 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\setuptools\\_vendor',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build',
 'D:\\Gawean Rebinmas\\Monitoring '
 'Database\\ifess\\Client_Server_Web_No_GUI\\staging_build']
1837 INFO: Appending 'datas' from .spec
1839 INFO: checking Analysis
1839 INFO: Building Analysis because Analysis-00.toc is non existent
1840 INFO: Running Analysis Analysis-00.toc
1840 INFO: Target bytecode optimization level: 0
1840 INFO: Initializing module dependency graph...
1842 INFO: Initializing module graph hook caches...
1862 INFO: Analyzing modules for base_library.zip ...
6066 INFO: Processing standard module hook 'hook-encodings.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
8782 INFO: Processing standard module hook 'hook-pickle.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
9844 INFO: Processing standard module hook 'hook-heapq.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
10859 INFO: Caching module dependency graph...
10923 INFO: Looking for Python shared library...
10924 INFO: Using Python shared library: C:\Program Files\WindowsApps\PythonSoftwareFoundation.Python.3.13_3.13.1008.0_x64__qbz5n2kfra8p0\python313.dll
10924 INFO: Analyzing D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ifess_config_gui.py
10973 INFO: Processing pre-find-module-path hook 'hook-tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_find_module_path'
10974 INFO: TclTkInfo: initializing cached Tcl/Tk info...
11431 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
11576 INFO: Processing standard module hook 'hook-platform.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
11824 INFO: Processing standard module hook 'hook-urllib3.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
11867 INFO: Processing pre-safe-import-module hook 'hook-urllib3.packages.six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13116 INFO: Processing standard module hook 'hook-charset_normalizer.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
13239 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
13239 INFO: SetuptoolsInfo: initializing cached setuptools info...
20296 INFO: Processing standard module hook 'hook-cryptography.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
23212 INFO: hook-cryptography: cryptography does not seem to be using dynamically linked OpenSSL.
23656 INFO: Processing standard module hook 'hook-certifi.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
24700 INFO: Processing standard module hook 'hook-difflib.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
25149 INFO: Processing standard module hook 'hook-sysconfig.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
25815 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
25940 INFO: Processing standard module hook 'hook-xml.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
27699 INFO: Processing standard module hook 'hook-pycparser.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
28230 INFO: Processing standard module hook 'hook-setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
28248 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28291 INFO: Processing pre-safe-import-module hook 'hook-jaraco.functools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28291 INFO: Setuptools: 'jaraco.functools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.functools'!
28303 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28303 INFO: Setuptools: 'more_itertools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.more_itertools'!
28498 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28651 INFO: Processing pre-safe-import-module hook 'hook-jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28651 INFO: Setuptools: 'jaraco.text' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.text'!
28675 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
28679 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28680 INFO: Processing pre-safe-import-module hook 'hook-jaraco.context.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28681 INFO: Setuptools: 'jaraco.context' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco.context'!
28693 INFO: Processing pre-safe-import-module hook 'hook-backports.tarfile.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28693 INFO: Setuptools: 'backports.tarfile' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.backports.tarfile'!
28811 INFO: Processing standard module hook 'hook-backports.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
28920 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28921 INFO: Setuptools: 'importlib_metadata' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.importlib_metadata'!
28951 INFO: Processing standard module hook 'hook-setuptools._vendor.importlib_metadata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
28955 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
28956 INFO: Setuptools: 'zipp' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.zipp'!
29348 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
29349 INFO: Setuptools: 'tomli' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.tomli'!
30224 INFO: Processing standard module hook 'hook-pkg_resources.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
31269 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
31285 INFO: Processing standard module hook 'hook-platformdirs.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
31583 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
32583 INFO: Processing standard module hook 'hook-google.api_core.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
32650 INFO: Processing standard module hook 'hook-httplib2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
33183 INFO: Processing standard module hook 'hook-jinja2.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
33830 INFO: Processing standard module hook 'hook-googleapiclient.model.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
34292 INFO: Processing standard module hook 'hook-dateutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
34491 INFO: Processing pre-safe-import-module hook 'hook-six.moves.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\pre_safe_import_module'
34787 INFO: Processing standard module hook 'hook-Crypto.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
36903 INFO: Analyzing hidden import 'tkinter.scrolledtext'
36913 INFO: Analyzing hidden import 'tkinter.font'
36926 INFO: Analyzing hidden import 'fdb'
37787 INFO: Analyzing hidden import 'firebirdsql'
38070 INFO: Processing standard module hook 'hook-zoneinfo.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
38321 INFO: Analyzing hidden import 'google_auth_oauthlib'
38825 INFO: Processing module hooks (post-graph stage)...
39031 INFO: Processing standard module hook 'hook-_tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks'
39035 INFO: Processing standard module hook 'hook-tzdata.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks'
40630 INFO: Performing binary vs. data reclassification (2168 entries)
41010 INFO: Looking for ctypes DLLs
41218 INFO: Analyzing run-time hooks ...
41228 INFO: Including run-time hook 'pyi_rth_inspect.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
41234 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
41240 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
41244 INFO: Including run-time hook 'pyi_rth_setuptools.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
41246 INFO: Including run-time hook 'pyi_rth_pkgres.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
41252 INFO: Including run-time hook 'pyi_rth_cryptography_openssl.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\_pyinstaller_hooks_contrib\\rthooks'
41254 INFO: Including run-time hook 'pyi_rth__tkinter.py' from 'C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks'
41353 INFO: Creating base_library.zip...
41425 INFO: Looking for dynamic libraries
43349 INFO: Extra DLL search directories (AddDllDirectory): []
43349 INFO: Extra DLL search directories (PATH): []
44415 INFO: Warnings written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\warn-ifess_config_gui_comprehensive.txt
44520 INFO: Graph cross-reference written to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\xref-ifess_config_gui_comprehensive.html
44632 INFO: checking PYZ
44632 INFO: Building PYZ because PYZ-00.toc is non existent
44632 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\PYZ-00.pyz
46120 INFO: Building PYZ (ZlibArchive) D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\build\ifess_config_gui_comprehensive\PYZ-00.pyz completed successfully.
46203 INFO: checking PKG
46203 INFO: Building PKG because PKG-00.toc is non existent
46204 INFO: Building PKG (CArchive) ifess_config_gui.pkg
54626 INFO: Building PKG (CArchive) ifess_config_gui.pkg completed successfully.
54675 INFO: Bootloader C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\LocalCache\local-packages\Python313\site-packages\PyInstaller\bootloader\Windows-64bit-intel\runw.exe
54675 INFO: checking EXE
54675 INFO: Building EXE because EXE-00.toc is non existent
54675 INFO: Building EXE from EXE-00.toc
54675 INFO: Copying bootloader EXE to D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ifess_config_gui.exe
54688 INFO: Copying icon to EXE
55285 INFO: Copying 0 resources to EXE
55285 INFO: Embedding manifest in EXE
55903 WARNING: Execution of 'write_manifest_to_executable' failed on attempt #1 / 20: error(110, 'EndUpdateResourceW', 'The system cannot open the device or file specified.'). Retrying in 0.05 second(s)...
55962 INFO: Appending PKG archive to EXE
56246 INFO: Fixing EXE headers
63342 INFO: Building EXE from EXE-00.toc completed successfully.
63395 INFO: Build complete! The results are available in: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_config_oauth_tokens.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_module.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_simple.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\gdrive_client_oauth.py -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\ptrj-backup-services-account.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secret.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\client_secrets.json -> .
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\common -> common
Including: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\token_downloader.py -> .
SUCCESS: ifess_config_gui.exe built successfully 
File size: ******** bytes 
[7/7] Post-build tasks and packaging... 
Copying additional files to dist directory... 
Copied: client_config.json 
Copied: client_config_oauth_tokens.json 
Copied: token.json 
Copied: token_backup.json 
Copied: ptrj-backup-services-account.json 
Copied: client_secret.json 
Copied: client_secrets.json 
Creating launcher batch files... 
Created launcher batch files 
Copied: setup_portable_credentials.bat 
Setting up portable credentials configuration... 
Creating distribution README... 
Created distribution README 
Build completed: 06/06/2025 14:08:38,90 
Results: 
  Successful builds: 2/3 
  Errors: 0 
  Warnings: 1 
✓ ifess_client_hidden.exe - Ready (******** bytes) 
✓ ifess_config_gui.exe - Ready (******** bytes) 
Distribution ready in: D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\staging_build\dist\ 
Build log saved as: build_log_2025-06-06_14-06-08.txt 
   BUILD COMPLETED SUCCESSFULLY 
