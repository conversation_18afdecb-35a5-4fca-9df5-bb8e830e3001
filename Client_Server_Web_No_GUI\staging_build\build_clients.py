#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
IFESS Client Build Script

This script builds the IFESS client applications (hidden, GUI, and debug) using PyInstaller.
It creates standalone executables for each application.

Requirements:
- PyInstaller: pip install pyinstaller
"""

import os
import sys
import shutil
import subprocess
import argparse
import logging
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("build.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("IFESS-Build")

# Constants
DIST_DIR = "dist"
BUILD_DIR = "build"
ICON_FILE = "MAINICON.ico"
VERSION_FILE = "version.txt"
VERSION = "1.0.0"  # Default version

def check_requirements():
    """Check if PyInstaller is installed"""
    try:
        import PyInstaller
        logger.info("PyInstaller is installed.")
        return True
    except ImportError:
        logger.error("PyInstaller is not installed. Please install it using: pip install pyinstaller")
        return False

def create_version_file():
    """Create version file for the build"""
    with open(VERSION_FILE, "w") as f:
        f.write(f"version={VERSION}\n")
        f.write(f"build_date={time.strftime('%Y-%m-%d %H:%M:%S')}\n")
    logger.info(f"Created version file: {VERSION_FILE}")

def build_hidden_client():
    """Build the hidden client application"""
    logger.info("Building hidden client...")
    
    # Define PyInstaller command
    cmd = [
        "pyinstaller",
        "--name=ifess_client_hidden",
        "--onefile",
        "--noconsole",  # No console window
        "--clean",
        f"--distpath={DIST_DIR}",
        f"--workpath={BUILD_DIR}",
        "--add-data=common;common",  # Include common module
    ]
    
    # Add icon if available
    if os.path.exists(ICON_FILE):
        cmd.append(f"--icon={ICON_FILE}")
    
    # Add version file if available
    if os.path.exists(VERSION_FILE):
        cmd.append(f"--version-file={VERSION_FILE}")
    
    # Add main script
    cmd.append("ifess_client_hidden.py")
    
    # Run PyInstaller
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        logger.info("Hidden client built successfully.")
        logger.debug(result.stdout)
        return True
    else:
        logger.error(f"Failed to build hidden client: {result.stderr}")
        return False

def build_config_gui():
    """Build the configuration GUI application"""
    logger.info("Building configuration GUI...")
    
    # Define PyInstaller command
    cmd = [
        "pyinstaller",
        "--name=ifess_config_gui",
        "--onefile",
        "--windowed",  # GUI application
        "--clean",
        f"--distpath={DIST_DIR}",
        f"--workpath={BUILD_DIR}",
        "--add-data=common;common",  # Include common module
    ]
    
    # Add icon if available
    if os.path.exists(ICON_FILE):
        cmd.append(f"--icon={ICON_FILE}")
    
    # Add version file if available
    if os.path.exists(VERSION_FILE):
        cmd.append(f"--version-file={VERSION_FILE}")
    
    # Add main script
    cmd.append("ifess_config_gui.py")
    
    # Run PyInstaller
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        logger.info("Configuration GUI built successfully.")
        logger.debug(result.stdout)
        return True
    else:
        logger.error(f"Failed to build configuration GUI: {result.stderr}")
        return False

def build_debug_client():
    """Build the debug client application"""
    logger.info("Building debug client...")
    
    # Define PyInstaller command
    cmd = [
        "pyinstaller",
        "--name=ifess_client_debug",
        "--onefile",
        "--windowed",  # GUI application
        "--clean",
        f"--distpath={DIST_DIR}",
        f"--workpath={BUILD_DIR}",
        "--add-data=common;common",  # Include common module
    ]
    
    # Add icon if available
    if os.path.exists(ICON_FILE):
        cmd.append(f"--icon={ICON_FILE}")
    
    # Add version file if available
    if os.path.exists(VERSION_FILE):
        cmd.append(f"--version-file={VERSION_FILE}")
    
    # Add main script
    cmd.append("ifess_client_debug.py")
    
    # Run PyInstaller
    logger.info(f"Running command: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        logger.info("Debug client built successfully.")
        logger.debug(result.stdout)
        return True
    else:
        logger.error(f"Failed to build debug client: {result.stderr}")
        return False

def copy_additional_files():
    """Copy additional files to the distribution directory"""
    logger.info("Copying additional files...")
    
    # Create a default config file if it doesn't exist
    if not os.path.exists("client_config.json"):
        default_config = {
            "server_address": "localhost",
            "server_port": 5555,
            "reconnect_interval": 5,
            "client_id": "",  # Will be generated by the client
            "display_name": "",  # Will be generated by the client
            "database": {
                "path": "",
                "username": "SYSDBA",
                "password": "masterkey",
                "isql_path": "",
                "use_localhost": True
            }
        }
        
        import json
        with open("client_config.json", "w") as f:
            json.dump(default_config, f, indent=2)
        logger.info("Created default config file.")
    
    # Copy config file to dist directory
    try:
        shutil.copy("client_config.json", os.path.join(DIST_DIR, "client_config.json"))
        logger.info("Copied client_config.json to dist directory.")
    except Exception as e:
        logger.error(f"Failed to copy client_config.json: {e}")
    
    # Copy icon file if available
    if os.path.exists(ICON_FILE):
        try:
            shutil.copy(ICON_FILE, os.path.join(DIST_DIR, ICON_FILE))
            logger.info(f"Copied {ICON_FILE} to dist directory.")
        except Exception as e:
            logger.error(f"Failed to copy {ICON_FILE}: {e}")
    
    # Copy README if available
    if os.path.exists("README.md"):
        try:
            shutil.copy("README.md", os.path.join(DIST_DIR, "README.md"))
            logger.info("Copied README.md to dist directory.")
        except Exception as e:
            logger.error(f"Failed to copy README.md: {e}")

def create_launcher():
    """Create a launcher batch file"""
    logger.info("Creating launcher batch file...")
    
    launcher_content = """@echo off
echo IFESS Client Launcher
echo =====================
echo.
echo 1. Start Hidden Client
echo 2. Open Configuration GUI
echo 3. Open Debug Monitor
echo 4. Exit
echo.
choice /C 1234 /N /M "Select an option: "

if errorlevel 4 goto :exit
if errorlevel 3 goto :debug
if errorlevel 2 goto :config
if errorlevel 1 goto :hidden

:hidden
echo Starting Hidden Client...
start "" /b ifess_client_hidden.exe
goto :exit

:config
echo Opening Configuration GUI...
start "" ifess_config_gui.exe
goto :exit

:debug
echo Opening Debug Monitor...
start "" ifess_client_debug.exe
goto :exit

:exit
echo Exiting...
"""
    
    try:
        with open(os.path.join(DIST_DIR, "launcher.bat"), "w") as f:
            f.write(launcher_content)
        logger.info("Created launcher.bat in dist directory.")
    except Exception as e:
        logger.error(f"Failed to create launcher.bat: {e}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Build IFESS client applications")
    parser.add_argument("--version", help="Version number for the build", default=VERSION)
    parser.add_argument("--hidden", action="store_true", help="Build only the hidden client")
    parser.add_argument("--gui", action="store_true", help="Build only the configuration GUI")
    parser.add_argument("--debug", action="store_true", help="Build only the debug client")
    parser.add_argument("--all", action="store_true", help="Build all clients (default)")
    args = parser.parse_args()
    
    # Set version
    global VERSION
    VERSION = args.version
    
    # Check if PyInstaller is installed
    if not check_requirements():
        return
    
    # Create build directories if they don't exist
    os.makedirs(DIST_DIR, exist_ok=True)
    os.makedirs(BUILD_DIR, exist_ok=True)
    
    # Create version file
    create_version_file()
    
    # Determine which clients to build
    build_all = args.all or not (args.hidden or args.gui or args.debug)
    
    # Build clients
    if build_all or args.hidden:
        build_hidden_client()
    
    if build_all or args.gui:
        build_config_gui()
    
    if build_all or args.debug:
        build_debug_client()
    
    # Copy additional files
    copy_additional_files()
    
    # Create launcher
    create_launcher()
    
    logger.info("Build process completed.")

if __name__ == "__main__":
    main()
