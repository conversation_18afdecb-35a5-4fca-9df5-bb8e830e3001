-- Query untuk menemukan transaksi overtime untuk Helper dan Driver yang tidak memiliki kode nomor mobil
-- VARIABLES_START
-- VAR:START_DATE::
-- VAR:END_DATE::
-- VARIABLES_END
SELECT 
  e.EMPCODE AS KaryawanCode,
  o.ID AS OvertimeID,
  o.TRANSSTATUS AS TRANSSTATUS,
  l.NAME AS TransStatus_Name,
  o.INPDATE AS TanggalOvertime,
  o.HOURS AS JamKerja,
  SUBSTRING(f.FIELDNO FROM 1 FOR 6) AS KodeField,
  (j.EXPTYPE || j.<PERSON><PERSON>N<PERSON> || j.SUBNO) AS AccCode,
  j.DESCRIPTION AS DeskripsiPekerjaan,
  o.BASICRATE AS TarifDasar,
  o.ADDRATE AS TarifTambahan,
  o.HOURS * o.BASICRATE AS NilaiDasar,
  o.HOURS * o.ADDRATE AS NilaiTambahan,
  o.REMARKS AS Catatan,
  SUBSTRING(f.FIELDNO FROM 1 FOR 2) AS KodeField_2Char,
  SUBSTRING(j.<PERSON>X<PERSON>TYPE FROM 1 FOR 2) AS AccCode_2Char,
  a.VEHNO AS NomorKendaraan,
  a.MODEL AS ModelKendaraan,
  o.VEHID AS VehicleID
FROM OVERTIME o
JOIN JOBCODE j ON o.JOBID = j.ID
JOIN OCFIELD f ON o.FIELDID = f.ID
LEFT JOIN VEHCODE a ON o.VEHID = a.ID
JOIN EMP e ON o.EMPID = e.ID
LEFT JOIN LOOKUP l ON o.TRANSSTATUS = l.ID
WHERE o.INPDATE BETWEEN '#START_DATE#' AND '#END_DATE#'
  AND SUBSTRING(j.EXPTYPE FROM 1 FOR 2) = 'GA'
  AND NOT (
    j.EXPTYPE = 'GA' AND j.ITEMNO = '91' AND j.SUBNO = '10'
    AND SUBSTRING(f.FIELDNO FROM 1 FOR 2) = 'YY'
  )
ORDER BY e.EMPCODE, o.INPDATE