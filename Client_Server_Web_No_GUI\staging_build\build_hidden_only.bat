@echo off
echo Building IFESS Client Hidden Application
echo ====================================
echo.

REM Menghentikan aplikasi yang sedang berjalan
echo Menghentikan aplikasi yang sedang berjalan...
taskkill /f /im ifess_client_hidden.exe 2>nul
taskkill /f /fi "IMAGENAME eq pythonw.exe" /fi "COMMANDLINE eq *ifess_client_hidden.py*" 2>nul
timeout /t 2 /nobreak > nul

REM Mendapatkan direktori saat ini
set CURRENT_DIR=%CD%
echo Direktori saat ini: %CURRENT_DIR%

REM Membersihkan direktori dist
echo Membersihkan direktori dist...
if not exist dist mkdir dist
if exist dist\ifess_client_hidden.exe (
    echo Menghapus executable yang ada...
    del /f /q dist\ifess_client_hidden.exe 2>nul
)

REM Membersihkan direktori build
echo Membersihkan direktori build...
if not exist build mkdir build

REM Membersihkan spec file
echo Membersihkan spec file...
if exist ifess_client_hidden.spec (
    del /f /q ifess_client_hidden.spec 2>nul
)

REM Memeriksa apakah Python terinstall
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python tidak terinstall atau tidak ada di PATH. Harap install Python 3.8 atau lebih tinggi.
    goto :exit
)

REM Menginstall PyInstaller jika belum terinstall
echo Menginstall PyInstaller...
python -m pip install pyinstaller
if %ERRORLEVEL% NEQ 0 (
    echo Gagal menginstall PyInstaller. Harap periksa koneksi internet Anda.
    goto :exit
)

REM Menginstall paket yang diperlukan
echo Menginstall paket yang diperlukan...
python -m pip install mega.py
python -m pip install fdb
python -m pip install requests
python -m pip install google-auth google-api-python-client
if %ERRORLEVEL% NEQ 0 (
    echo Gagal menginstall paket yang diperlukan. Harap periksa koneksi internet Anda.
    goto :exit
)

REM Mengatasi masalah pathlib yang konflik dengan PyInstaller
echo Mengatasi masalah pathlib yang tidak kompatibel dengan PyInstaller...
python -m pip uninstall -y pathlib
echo Mencoba cara alternatif untuk menghapus pathlib...
"C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\python.exe" -m pip uninstall -y pathlib
echo Paket pathlib berhasil dihapus.

REM ========== BUILD HIDDEN CLIENT ==========
echo.
echo Membangun Hidden Client...
echo Ini mungkin membutuhkan beberapa menit...
python -m PyInstaller --name=ifess_client_hidden --onefile --noconsole --clean --noupx ^
    --hidden-import=mega ^
    --hidden-import=mega.mega ^
    --hidden-import=fdb ^
    --hidden-import=socket ^
    --hidden-import=json ^
    --hidden-import=threading ^
    --hidden-import=requests ^
    --hidden-import=googleapiclient ^
    --hidden-import=googleapiclient.discovery ^
    --hidden-import=google.oauth2 ^
    --hidden-import=google.oauth2.service_account ^
    --add-data "%CURRENT_DIR%\client_config.json;." ^
    --add-data "%CURRENT_DIR%\mega_client_py310.py;." ^
    --add-data "%CURRENT_DIR%\gdrive_client_module.py;." ^
    --add-data "%CURRENT_DIR%\ptrj-backup-services-account.json;." ^
    --distpath "%CURRENT_DIR%\dist" ^
    --workpath "%CURRENT_DIR%\build" ^
    "%CURRENT_DIR%\ifess_client_hidden.py"

if %ERRORLEVEL% NEQ 0 (
    echo Gagal membangun hidden client.
    goto :exit
)

REM Menyalin file konfigurasi default dan kredensial
echo.
echo Menyalin file konfigurasi default dan kredensial...
copy "%CURRENT_DIR%\client_config.json" "%CURRENT_DIR%\dist\client_config.json"
copy "%CURRENT_DIR%\ptrj-backup-services-account.json" "%CURRENT_DIR%\dist\ptrj-backup-services-account.json"

echo.
echo Build hidden client selesai dengan sukses!
echo Executable tersedia di folder 'dist'.
echo.

:exit
pause