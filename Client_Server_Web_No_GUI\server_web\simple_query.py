import fdb

# Database connection parameters
db_path = r"C:\Gawean Rebinmas\Monitoring Database\Ifess Monitoring\PTRJ_P1A_08042025\PTRJ_P1A.FDB"
username = "sysdba"
password = "masterkey"

try:
    # Connect to the database
    print(f"Connecting to database: {db_path}")
    conn = fdb.connect(
        dsn=db_path,
        user=username,
        password=password
    )
    
    print("Connected successfully!")
    
    # Close the connection
    conn.close()
    
except Exception as e:
    print(f"Error: {e}")

input("\nPress Enter to exit...")
