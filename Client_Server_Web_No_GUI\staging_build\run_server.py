import os, sys 
import webbrowser 
 
# Add the server_web directory to the path 
server_web_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'server_web') 
if server_web_dir not in sys.path: 
    sys.path.insert(0, server_web_dir) 
 
# Import the Flask app from server_web.py 
from server_web import app 
 
if __name__ == '__main__': 
    print("Starting IFESS Server Web...") 
    print("Server will be accessible at http://localhost:5000") 
    # Open the browser 
    webbrowser.open('http://localhost:5000') 
    # Run the Flask app 
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True) 
