"""
Standalone script to run the robust transfer server.
"""

import os
import sys
import logging
import time
from flask import Flask, request, jsonify

# Add parent directory to path to import common modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Import robust transfer module
from robust_transfer import get_transfer_manager

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('robust_transfer.log')
    ]
)
logger = logging.getLogger('robust_transfer')

# Initialize Flask app
app = Flask(__name__)
app.secret_key = 'robust_transfer_secret_key'

# Helper functions
def format_file_size(size_bytes):
    """Format file size in human-readable format"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes/1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes/(1024*1024):.1f} MB"
    else:
        return f"{size_bytes/(1024*1024*1024):.1f} GB"

# Initialize transfer manager
def init_transfer_manager():
    backup_dir = os.path.join(current_dir, 'static', 'backups')
    os.makedirs(backup_dir, exist_ok=True)
    logger.info(f"Initializing robust transfer manager with backup directory: {backup_dir}")
    return get_transfer_manager(backup_dir)

# Robust transfer API routes
@app.route('/api/robust-transfer/initiate/<client_id>', methods=['POST'])
def api_initiate_transfer(client_id):
    """API endpoint to initiate a new database transfer"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })

        # Get file info from request
        file_info = request.json or {}
        client_name = file_info.get('client_name', client_id)

        # Initiate transfer
        transfer_info = transfer_manager.initiate_transfer(client_id, client_name, file_info)

        return jsonify({
            'success': True,
            'message': f"Transfer initiated for {client_name}",
            'transfer_info': {
                'file_name': transfer_info['file_name'],
                'file_path': transfer_info['file_path'],
                'file_size': transfer_info['file_size'],
                'file_size_formatted': format_file_size(transfer_info['file_size']),
                'state': transfer_info['state'],
                'chunk_size': transfer_info['chunk_size']
            }
        })
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error initiating transfer: {e}")
        return jsonify({
            'success': False,
            'message': f"Error initiating transfer: {str(e)}"
        })

@app.route('/api/robust-transfer/chunk/<client_id>', methods=['POST'])
def api_upload_chunk(client_id):
    """API endpoint to upload a chunk of data"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })

        # Get chunk info from request
        chunk_info = request.json or {}

        # Process chunk
        result = transfer_manager.process_chunk(client_id, chunk_info)

        # Return simplified result
        response = {
            'success': result.get('success', False),
            'message': result.get('message', "Unknown error")
        }

        # Add transfer info if available
        if 'transfer_info' in result:
            transfer_info = result['transfer_info']
            response['progress'] = transfer_info.get('progress', 0)
            response['bytes_received'] = transfer_info.get('bytes_received', 0)
            response['chunks_received'] = transfer_info.get('chunks_received', 0)

        return jsonify(response)
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error processing chunk: {e}")
        return jsonify({
            'success': False,
            'message': f"Error processing chunk: {str(e)}"
        })

@app.route('/api/robust-transfer/status/<client_id>', methods=['GET'])
def api_transfer_status(client_id):
    """API endpoint to get the status of a transfer"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })

        # Get transfer status
        status = transfer_manager.get_transfer_status(client_id)

        return jsonify(status)
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error getting transfer status: {e}")
        return jsonify({
            'success': False,
            'message': f"Error getting transfer status: {str(e)}"
        })

@app.route('/api/robust-transfer/resume/<client_id>', methods=['POST'])
def api_resume_transfer(client_id):
    """API endpoint to resume a paused or stalled transfer"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })

        # Resume transfer
        result = transfer_manager.resume_transfer(client_id)

        return jsonify(result)
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error resuming transfer: {e}")
        return jsonify({
            'success': False,
            'message': f"Error resuming transfer: {str(e)}"
        })

@app.route('/api/robust-transfer/cancel/<client_id>', methods=['POST'])
def api_cancel_transfer(client_id):
    """API endpoint to cancel an active transfer"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })

        # Cancel transfer
        result = transfer_manager.cancel_transfer(client_id)

        return jsonify(result)
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error cancelling transfer: {e}")
        return jsonify({
            'success': False,
            'message': f"Error cancelling transfer: {str(e)}"
        })

# Main entry point
if __name__ == '__main__':
    # Initialize transfer manager
    init_transfer_manager()

    # Configure Flask to handle larger requests
    app.config['MAX_CONTENT_LENGTH'] = 1024 * 1024 * 1024  # 1GB max request size

    # Run the Flask app
    app.run(host='0.0.0.0', port=5001, debug=True)
