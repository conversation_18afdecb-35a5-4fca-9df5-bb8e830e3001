@echo off
echo Firebird Database Repair and Import Tool
echo =====================================
echo.

REM Check if a database file path is provided
if "%~1"=="" (
    echo Usage: repair_and_import.bat [database_file_path]
    echo.
    echo Example: repair_and_import.bat "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups\import_76b3e573\database_20250423_105816.fdb"
    echo.
    
    set /p DB_PATH=Enter the path to the database file to repair: 
) else (
    set DB_PATH=%~1
)

echo.
echo Starting repair of database: %DB_PATH%
echo.

REM First, analyze the database
echo Step 1: Analyzing database...
python analyze_firebird_db.py "%DB_PATH%"

echo.
echo Press any key to continue with repair...
pause > nul

REM Then, try to repair the database
echo.
echo Step 2: Attempting to repair database...
python repair_firebird_db.py "%DB_PATH%"

echo.
echo Press any key to continue with import...
pause > nul

REM Finally, try to import a new database from client
echo.
echo Step 3: Importing new database from client...
set /p CLIENT_ID=Enter the client ID to import from: 
set /p HOST=Enter the host (default: localhost): 
if "%HOST%"=="" set HOST=localhost
set /p PORT=Enter the port (default: 5555): 
if "%PORT%"=="" set PORT=5555

echo.
echo Starting database import from %CLIENT_ID% at %HOST%:%PORT%
echo.

python robust_db_transfer.py "%CLIENT_ID%" "%HOST%" "%PORT%"

echo.
echo All steps completed.
echo.
pause
