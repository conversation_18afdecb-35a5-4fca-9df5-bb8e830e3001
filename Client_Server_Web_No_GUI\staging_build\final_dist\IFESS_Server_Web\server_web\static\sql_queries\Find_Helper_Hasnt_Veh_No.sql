SELECT 
  o.EMPID AS KaryawanID,
  o.ID AS OvertimeID,
  o.INPDATE AS TanggalOvertime,
  o.HOURS AS JamKerja,
  SUBSTRING(f.FIELDNO FROM 1 FOR 6) AS KodeField,
  (j.EXPTYPE || j.<PERSON><PERSON><PERSON><PERSON> || j.<PERSON>UB<PERSON><PERSON>) AS AccCode,
  j.<PERSON>SCRIPTION AS DeskripsiPekerjaan,
  o.BASICRATE AS TarifDasar,
  o.ADDRATE AS TarifTambahan,
  o.HOURS * o.BASICRATE AS NilaiDasar,
  o.HOURS * o.ADDRATE AS NilaiTambahan,
  o.REMARKS AS Catatan,
  SUBSTRING(f.FIELDNO FROM 1 FOR 2) AS KodeField_2Char,
  SUBSTRING(j.EXPTYPE FROM 1 FOR 2) AS AccCode_2Char,
  a.VEHNO AS NomorKendaraan,
  a.MODEL AS ModelKendaraan,
  o.VEHID AS VehicleID
FROM OVERTIME o
JOIN JOBCODE j ON o.JOBID = j.ID
JOIN OCFIELD f ON o.FIELDID = f.ID
LEFT JOIN VEHCODE a ON o.VEHID = a.ID
WHERE o.INPDATE BETWEEN '2025-03-01' AND '2025-03-31'
  AND SUBSTRING(j.EXPTYPE FROM 1 FOR 2) = 'PT'
  AND a.ID IS NULL
ORDER BY o.EMPID, o.INPDATE