import socket
import json
import struct
import time

# Konstanta untuk komunikasi
DEFAULT_PORT = 5555
BUFFER_SIZE = 8192
ENCODING = 'utf-8'

class NetworkMessage:
    """
    Message class for network communication
    """

    # Message types
    TYPE_REGISTER = 'register'
    TYPE_QUERY = 'query'
    TYPE_RESULT = 'result'
    TYPE_ERROR = 'error'
    TYPE_PING = 'ping'
    TYPE_PONG = 'pong'
    TYPE_DB_REQUEST = 'db_request'
    TYPE_DB_INFO = 'db_info'
    TYPE_DB_CHUNK = 'db_chunk'
    TYPE_DB_COMPLETE = 'db_complete'
    TYPE_DB_ACK = 'db_ack'         # Acknowledgment of database info
    TYPE_DB_PROGRESS = 'db_progress'  # Progress update for database transfer
    TYPE_MEGA_UPLOAD_REQUEST = 'mega_upload_request'
    TYPE_MEGA_UPLOAD_ACK = 'mega_upload_ack'
    TYPE_MEGA_UPLOAD_PROGRESS = 'mega_upload_progress'
    TYPE_MEGA_UPLOAD_RESULT = 'mega_upload_result'

    def __init__(self, msg_type, data=None, client_id=None):
        """
        Initialize a new message
        
        Args:
            msg_type (str): Type of message
            data (dict, optional): Data to include in message
            client_id (str, optional): ID of client sending message
        """
        self.msg_type = msg_type  # Menggunakan msg_type secara konsisten
        self.data = data or {}
        self.client_id = client_id
        
        # Untuk backward compatibility dengan kode lama yang menggunakan 'type'
        self.type = msg_type
        self.timestamp = time.time()

    def to_json(self):
        """Konversi pesan ke format JSON"""
        return json.dumps({
            'msg_type': self.msg_type,
            'data': self.data,
            'client_id': self.client_id,
            'timestamp': self.timestamp
        })

    @classmethod
    def from_json(cls, json_str):
        """Buat objek pesan dari string JSON"""
        try:
            data = json.loads(json_str)
            # Cek apakah menggunakan 'type' atau 'msg_type'
            msg_type = data.get('msg_type')
            if msg_type is None:
                msg_type = data.get('type')

            return cls(
                msg_type,
                data.get('data'),
                data.get('client_id')
            )
        except json.JSONDecodeError:
            return cls(cls.TYPE_ERROR, "Invalid JSON message", None)

def send_message(sock, message):
    """
    Kirim pesan melalui socket.
    Protokol: [4-byte length prefix][message bytes]

    :param sock: Socket terhubung untuk mengirim data
    :param message: Objek NetworkMessage untuk dikirim
    :return: True jika berhasil, False jika gagal
    """
    try:
        # Konversi pesan ke JSON
        json_data = message.to_json()

        # Debug info tentang pesan yang akan dikirim
        msg_type = message.msg_type
        client_id = message.client_id
        data_keys = list(message.data.keys()) if isinstance(message.data, dict) else "non-dict"
        print(f"Sending message: type={msg_type}, client={client_id}, data_keys={data_keys}")

        # Special handling for backup request messages
        if msg_type == 'db_request':
            print(f"\n[BACKUP] ===== SENDING DATABASE BACKUP REQUEST =====")
            print(f"[BACKUP] Sending database backup request to client: {client_id}")
            print(f"[BACKUP] Request data: {message.data}")
            print(f"[BACKUP] Message type: {msg_type}")
            print(f"[BACKUP] Time: {time.strftime('%Y-%m-%d %H:%M:%S')}")

        if msg_type == 'result' and isinstance(message.data, dict) and 'result' in message.data:
            result_data = message.data['result']
            print(f"Result data: {len(result_data)} result sets")
            for i, rs in enumerate(result_data):
                headers = rs.get('headers', [])
                rows = rs.get('rows', [])
                print(f"  Result set {i+1}: {len(rows)} rows, {len(headers)} columns")

        # Konversi string JSON ke bytes
        data = json_data.encode(ENCODING)
        # Dapatkan panjang data dalam bytes
        msg_len = len(data)
        print(f"Message size: {msg_len} bytes")

        # Implement retry mechanism for important messages
        max_retries = 3
        for retry in range(max_retries):
            try:
                # Kirim panjang pesan sebagai unsigned int (4 bytes)
                sock.sendall(struct.pack('>I', msg_len))
                # Kirim data pesan
                sock.sendall(data)
                print(f"Message sent successfully on attempt {retry+1}")
                return True
            except (ConnectionError, socket.timeout, socket.error) as e:
                if retry < max_retries - 1:
                    print(f"Error sending message on attempt {retry+1}: {e}, retrying...")
                    time.sleep(0.5)  # Short delay before retry
                else:
                    print(f"Failed to send message after {max_retries} attempts: {e}")
                    raise
    except ConnectionError as ce:
        print(f"Connection error while sending message: {ce}")
        return False
    except socket.timeout as to:
        print(f"Socket timeout while sending message: {to}")
        return False
    except (socket.error, struct.error) as e:
        print(f"Error saat mengirim pesan: {e}")
        return False

def receive_message(sock):
    """
    Terima pesan dari socket.
    Protokol: [4-byte length prefix][message bytes]

    :param sock: Socket terhubung untuk menerima data
    :return: Objek NetworkMessage atau None jika terjadi kesalahan
    """
    try:
        # Terima 4 byte pertama yang menunjukkan panjang pesan
        len_bytes = b''
        bytes_received = 0
        max_attempts = 3
        attempt = 0

        # Coba beberapa kali untuk mendapatkan 4 byte panjang pesan
        while bytes_received < 4 and attempt < max_attempts:
            try:
                print(f"Waiting to receive message length (attempt {attempt+1}/{max_attempts})")
                chunk = sock.recv(4 - bytes_received)
                if not chunk:  # Koneksi ditutup
                    if attempt < max_attempts - 1:
                        print(f"No data received, trying again (attempt {attempt+1}/{max_attempts})")
                        time.sleep(0.5)  # Tunggu sebentar sebelum mencoba lagi
                        attempt += 1
                        continue
                    else:
                        print("Connection closed: no data received for message length after multiple attempts")
                        return None
                len_bytes += chunk
                bytes_received = len(len_bytes)
                print(f"Received {bytes_received}/4 bytes for message length")
            except socket.timeout:
                if attempt < max_attempts - 1:
                    print(f"Timeout while receiving message length, trying again (attempt {attempt+1}/{max_attempts})")
                    attempt += 1
                    continue
                else:
                    print("Repeated timeout while receiving message length")
                    return None

        if len(len_bytes) < 4:
            print(f"Could not receive 4 bytes for message length, got only {len(len_bytes)} bytes")
            return None  # Koneksi ditutup

        # Unpack 4 bytes menjadi unsigned int
        try:
            msg_len = struct.unpack('>I', len_bytes)[0]
            print(f"Message length: {msg_len} bytes")
        except struct.error as se:
            print(f"Error unpacking message length: {se}")
            return None

        # Validasi ukuran pesan untuk mencegah DoS
        if msg_len > 50 * 1024 * 1024:  # Meningkatkan batas maksimum dari 10MB ke 50MB
            print(f"Pesan terlalu besar: {msg_len} bytes")
            return None

        # Validasi tambahan untuk ukuran pesan yang tidak masuk akal
        if msg_len > 1000 * 1024 * 1024 or msg_len < 0 or msg_len > 0x7FFFFFFF:  # Lebih dari 1GB atau negatif atau lebih dari max int
            print(f"Ukuran pesan tidak valid: {msg_len} bytes")
            return None

        # Validasi tambahan untuk mendeteksi kesalahan protokol
        # Jika kita menerima 4 byte yang terlihat seperti awal pesan baru, bukan ukuran pesan
        if len_bytes[:1] == b'{':
            print(f"Kesalahan protokol: menerima awal pesan JSON bukan ukuran pesan")
            return None

        # Validasi tambahan untuk mendeteksi kesalahan protokol
        # Jika ukuran pesan terlalu besar, mungkin ada masalah dengan protokol
        # Biasanya pesan tidak lebih dari 1MB
        if msg_len > 1 * 1024 * 1024:  # Lebih dari 1MB
            print(f"Peringatan: Ukuran pesan sangat besar: {msg_len} bytes, mungkin ada masalah dengan protokol")
            # Coba baca 10 byte pertama untuk melihat apakah ini pesan yang valid
            try:
                peek_data = sock.recv(10, socket.MSG_PEEK)
                if not peek_data.startswith(b'{'):
                    print(f"Kesalahan protokol: Data tidak dimulai dengan '{{', mungkin bukan JSON")
                    return None
            except Exception as e:
                print(f"Error saat mengintip data: {e}")
                return None

        # Terima semua data pesan
        data = b''
        remaining = msg_len
        start_time = time.time()
        timeout = 120  # Meningkatkan timeout dari 60 detik menjadi 120 detik
        retry_count = 0
        max_retries = 5

        print(f"Starting to receive message data ({msg_len} bytes)")

        while remaining > 0:
            # Cek apakah sudah timeout
            if time.time() - start_time > timeout:
                print(f"Timeout after {timeout}s while receiving message data")
                return None

            try:
                # Calculate progress for large messages
                if msg_len > 1024 * 1024:  # Only for messages > 1MB
                    progress = 100 - (remaining * 100 // msg_len)
                    if progress % 10 == 0:  # Log every 10%
                        received = msg_len - remaining
                        print(f"Progress: {progress}% ({received}/{msg_len} bytes)")

                # Receive chunk with adjusted buffer size for large messages
                current_buffer = min(remaining, BUFFER_SIZE)
                chunk = sock.recv(current_buffer)

                if not chunk:
                    retry_count += 1
                    if retry_count <= max_retries:
                        print(f"No data received, retrying ({retry_count}/{max_retries})")
                        time.sleep(0.5)
                        continue
                    else:
                        print("Connection closed unexpectedly while receiving message data")
                        return None  # Koneksi ditutup secara tidak terduga

                # Reset retry counter on successful receive
                retry_count = 0
                data += chunk
                remaining -= len(chunk)
            except socket.timeout:
                retry_count += 1
                if retry_count <= max_retries:
                    print(f"Timeout while receiving chunk, retrying ({retry_count}/{max_retries})")
                    continue
                else:
                    print(f"Too many timeouts while receiving message data")
                    return None

        # Dekode data ke string JSON
        try:
            print(f"Received complete message, size: {len(data)} bytes")
            json_data = data.decode(ENCODING)
            # Parse pesan JSON
            message = NetworkMessage.from_json(json_data)

            # Log received message for debugging
            if message.msg_type == 'db_request':
                print(f"[BACKUP] Received database backup request for client: {message.client_id}")
                print(f"[BACKUP] Request data: {message.data}")

            return message
        except UnicodeDecodeError as ude:
            print(f"Error saat mendekode pesan: {ude}")
            return None
        except json.JSONDecodeError as jde:
            print(f"Error saat parsing JSON: {jde}")
            print(f"First 200 chars of data: {data[:200]}")
            return None
    except socket.timeout as to:
        print(f"Socket timeout: {to}")
        return None
    except ConnectionError as ce:
        print(f"Connection error: {ce}")
        return None
    except (socket.error, struct.error) as e:
        print(f"Error saat menerima pesan: {e}")
        return None