"""
Robust Database Transfer Module

This module provides a more reliable mechanism for transferring large database files
between clients and the server, with support for resumable transfers, integrity checking,
and better error handling.
"""

import os
import time
import json
import logging
import hashlib
import threading
import shutil
from datetime import datetime

# Configure logger
logger = logging.getLogger('server_web')

class TransferState:
    """Represents the state of a file transfer"""
    INITIALIZING = "initializing"
    TRANSFERRING = "transferring"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    VERIFYING = "verifying"

class TransferManager:
    """Manages database file transfers with resumable capabilities"""
    
    def __init__(self, backup_dir):
        """Initialize the transfer manager
        
        Args:
            backup_dir: Directory where database backups are stored
        """
        self.backup_dir = backup_dir
        self.active_transfers = {}  # client_id -> transfer_info
        self.transfer_lock = threading.RLock()
        self.state_file = os.path.join(backup_dir, "transfer_state.json")
        self._load_state()
    
    def _load_state(self):
        """Load transfer state from disk"""
        try:
            if os.path.exists(self.state_file):
                with open(self.state_file, 'r') as f:
                    saved_state = json.load(f)
                    # Convert saved state to active transfers
                    for client_id, transfer_info in saved_state.items():
                        # Only load incomplete transfers
                        if transfer_info.get('state') not in [TransferState.COMPLETED, TransferState.FAILED]:
                            self.active_transfers[client_id] = transfer_info
                            logger.info(f"[ROBUST-TRANSFER] Loaded saved transfer state for {client_id}")
        except Exception as e:
            logger.error(f"[ROBUST-TRANSFER] Error loading transfer state: {e}")
    
    def _save_state(self):
        """Save transfer state to disk"""
        try:
            with self.transfer_lock:
                with open(self.state_file, 'w') as f:
                    json.dump(self.active_transfers, f)
        except Exception as e:
            logger.error(f"[ROBUST-TRANSFER] Error saving transfer state: {e}")
    
    def initiate_transfer(self, client_id, client_name, file_info):
        """Initialize a new file transfer
        
        Args:
            client_id: Unique identifier for the client
            client_name: Display name of the client
            file_info: Dictionary containing file metadata (name, size, etc.)
            
        Returns:
            Dictionary with transfer details
        """
        with self.transfer_lock:
            # Create client directory if it doesn't exist
            client_dir = os.path.join(self.backup_dir, client_id)
            os.makedirs(client_dir, exist_ok=True)
            
            # Generate a unique filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = file_info.get('file_name', f"database_{client_name}_{timestamp}.fdb")
            if not file_name.endswith('.fdb'):
                file_name += '.fdb'
            
            file_path = os.path.join(client_dir, file_name)
            temp_path = f"{file_path}.part"
            
            # Check if we have an existing transfer for this client
            if client_id in self.active_transfers:
                existing = self.active_transfers[client_id]
                # If there's an active transfer, check if we should resume or start new
                if existing.get('state') in [TransferState.TRANSFERRING, TransferState.PAUSED]:
                    # If the file is the same, we can resume
                    if existing.get('file_size') == file_info.get('file_size'):
                        logger.info(f"[ROBUST-TRANSFER] Resuming transfer for {client_id}")
                        # Update the state to transferring
                        existing['state'] = TransferState.TRANSFERRING
                        existing['last_update'] = time.time()
                        self._save_state()
                        return existing
            
            # Create a new transfer record
            transfer_info = {
                'client_id': client_id,
                'client_name': client_name,
                'file_name': file_name,
                'file_path': file_path,
                'temp_path': temp_path,
                'file_size': file_info.get('file_size', 0),
                'bytes_received': 0,
                'chunks_received': 0,
                'chunks_total': 0,
                'chunk_size': file_info.get('chunk_size', 32768),
                'start_time': time.time(),
                'last_update': time.time(),
                'state': TransferState.INITIALIZING,
                'received_chunks': set(),  # Track which chunks we've received
                'error_message': None,
                'checksum': file_info.get('checksum')
            }
            
            # Initialize empty file
            try:
                with open(temp_path, 'wb') as f:
                    # If we know the file size, pre-allocate the file
                    if transfer_info['file_size'] > 0:
                        f.seek(transfer_info['file_size'] - 1)
                        f.write(b'\0')
                logger.info(f"[ROBUST-TRANSFER] Created empty file: {temp_path}")
            except Exception as e:
                logger.error(f"[ROBUST-TRANSFER] Error creating file: {e}")
                transfer_info['state'] = TransferState.FAILED
                transfer_info['error_message'] = f"Failed to create file: {str(e)}"
            
            # Store the transfer info
            self.active_transfers[client_id] = transfer_info
            self._save_state()
            
            return transfer_info
    
    def process_chunk(self, client_id, chunk_info):
        """Process a received chunk and update transfer state
        
        Args:
            client_id: Client identifier
            chunk_info: Dictionary with chunk data and metadata
            
        Returns:
            Updated transfer info dictionary
        """
        with self.transfer_lock:
            if client_id not in self.active_transfers:
                logger.error(f"[ROBUST-TRANSFER] No active transfer for {client_id}")
                return {'success': False, 'message': 'No active transfer found'}
            
            transfer_info = self.active_transfers[client_id]
            
            # Check if transfer is in a valid state
            if transfer_info['state'] not in [TransferState.INITIALIZING, TransferState.TRANSFERRING]:
                logger.warning(f"[ROBUST-TRANSFER] Transfer for {client_id} is in state {transfer_info['state']}, cannot process chunk")
                return {'success': False, 'message': f"Transfer is in state {transfer_info['state']}"}
            
            # Extract chunk information
            chunk_number = chunk_info.get('chunk_number', 0)
            offset = chunk_info.get('offset', 0)
            chunk_size = chunk_info.get('chunk_size', 0)
            data = chunk_info.get('data', '')
            is_last = chunk_info.get('is_last', False)
            checksum = chunk_info.get('checksum')
            
            # Update state to transferring if it was initializing
            if transfer_info['state'] == TransferState.INITIALIZING:
                transfer_info['state'] = TransferState.TRANSFERRING
            
            # Check if we already have this chunk
            if chunk_number in transfer_info['received_chunks']:
                logger.debug(f"[ROBUST-TRANSFER] Chunk {chunk_number} already received for {client_id}")
                return {
                    'success': True,
                    'message': f"Chunk {chunk_number} already processed",
                    'transfer_info': transfer_info
                }
            
            # Verify checksum if provided
            if checksum and isinstance(data, bytes):
                calculated_checksum = hashlib.md5(data).hexdigest()
                if calculated_checksum != checksum:
                    logger.warning(f"[ROBUST-TRANSFER] Checksum mismatch for chunk {chunk_number}")
                    return {
                        'success': False,
                        'message': f"Checksum mismatch for chunk {chunk_number}",
                        'transfer_info': transfer_info
                    }
            
            # Write chunk to file
            try:
                with open(transfer_info['temp_path'], 'r+b') as f:
                    f.seek(offset)
                    if isinstance(data, str):
                        # If data is base64 encoded, decode it
                        import base64
                        try:
                            decoded_data = base64.b64decode(data)
                            f.write(decoded_data)
                            chunk_size = len(decoded_data)
                        except:
                            # If not base64, try writing as UTF-8
                            f.write(data.encode('utf-8'))
                    else:
                        # Assume it's already bytes
                        f.write(data)
                        chunk_size = len(data)
                
                # Update transfer info
                transfer_info['bytes_received'] += chunk_size
                transfer_info['chunks_received'] += 1
                transfer_info['received_chunks'].add(chunk_number)
                transfer_info['last_update'] = time.time()
                
                # Calculate progress percentage
                if transfer_info['file_size'] > 0:
                    progress = min(99, int(transfer_info['bytes_received'] * 100 / transfer_info['file_size']))
                else:
                    progress = 0
                
                transfer_info['progress'] = progress
                
                # Check if this is the last chunk
                if is_last:
                    logger.info(f"[ROBUST-TRANSFER] Received last chunk for {client_id}")
                    self._finalize_transfer(client_id)
                
                # Save state periodically (every 10 chunks)
                if transfer_info['chunks_received'] % 10 == 0:
                    self._save_state()
                
                logger.debug(f"[ROBUST-TRANSFER] Processed chunk {chunk_number} for {client_id}, progress: {progress}%")
                
                return {
                    'success': True,
                    'message': f"Chunk {chunk_number} processed successfully",
                    'transfer_info': transfer_info
                }
                
            except Exception as e:
                logger.error(f"[ROBUST-TRANSFER] Error processing chunk {chunk_number} for {client_id}: {e}")
                return {
                    'success': False,
                    'message': f"Error processing chunk: {str(e)}",
                    'transfer_info': transfer_info
                }
    
    def _finalize_transfer(self, client_id):
        """Finalize a completed transfer
        
        Args:
            client_id: Client identifier
        """
        with self.transfer_lock:
            if client_id not in self.active_transfers:
                logger.error(f"[ROBUST-TRANSFER] No active transfer for {client_id}")
                return
            
            transfer_info = self.active_transfers[client_id]
            transfer_info['state'] = TransferState.VERIFYING
            
            # Verify file size
            try:
                actual_size = os.path.getsize(transfer_info['temp_path'])
                if transfer_info['file_size'] > 0 and actual_size != transfer_info['file_size']:
                    logger.warning(f"[ROBUST-TRANSFER] File size mismatch for {client_id}: expected {transfer_info['file_size']}, got {actual_size}")
                    transfer_info['state'] = TransferState.FAILED
                    transfer_info['error_message'] = f"File size mismatch: expected {transfer_info['file_size']}, got {actual_size}"
                    self._save_state()
                    return
                
                # Verify Firebird database if it's an FDB file
                if transfer_info['file_path'].lower().endswith('.fdb'):
                    is_valid = self._verify_firebird_db(transfer_info['temp_path'])
                    if not is_valid:
                        logger.warning(f"[ROBUST-TRANSFER] Invalid Firebird database for {client_id}")
                        transfer_info['state'] = TransferState.FAILED
                        transfer_info['error_message'] = "Invalid Firebird database file"
                        self._save_state()
                        return
                
                # Move temp file to final location
                shutil.move(transfer_info['temp_path'], transfer_info['file_path'])
                
                # Update transfer info
                transfer_info['state'] = TransferState.COMPLETED
                transfer_info['progress'] = 100
                transfer_info['end_time'] = time.time()
                transfer_info['elapsed_time'] = transfer_info['end_time'] - transfer_info['start_time']
                
                # Calculate transfer speed
                if transfer_info['elapsed_time'] > 0:
                    transfer_info['speed_kb'] = round(transfer_info['bytes_received'] / 1024 / transfer_info['elapsed_time'], 2)
                else:
                    transfer_info['speed_kb'] = 0
                
                logger.info(f"[ROBUST-TRANSFER] Transfer completed for {client_id}: {transfer_info['file_path']}")
                
                # Save final state
                self._save_state()
                
            except Exception as e:
                logger.error(f"[ROBUST-TRANSFER] Error finalizing transfer for {client_id}: {e}")
                transfer_info['state'] = TransferState.FAILED
                transfer_info['error_message'] = f"Error finalizing transfer: {str(e)}"
                self._save_state()
    
    def _verify_firebird_db(self, file_path):
        """Verify that a file is a valid Firebird database
        
        Args:
            file_path: Path to the file to verify
            
        Returns:
            True if the file appears to be a valid Firebird database, False otherwise
        """
        try:
            with open(file_path, 'rb') as f:
                # Read the first 1024 bytes to check for Firebird signatures
                header = f.read(1024)
                # Firebird DB usually contains these strings in the header
                return (b'Firebird' in header or b'INTERBASE' in header or
                        b'GENERAL' in header or b'ODS' in header)
        except Exception as e:
            logger.error(f"[ROBUST-TRANSFER] Error verifying Firebird database: {e}")
            return False
    
    def get_transfer_status(self, client_id):
        """Get the current status of a transfer
        
        Args:
            client_id: Client identifier
            
        Returns:
            Dictionary with transfer status information
        """
        with self.transfer_lock:
            if client_id not in self.active_transfers:
                return {
                    'success': False,
                    'message': "No active transfer found",
                    'status': 'not_found'
                }
            
            transfer_info = self.active_transfers[client_id]
            
            # Calculate elapsed time
            elapsed_time = round(time.time() - transfer_info['start_time'], 2)
            
            # Calculate transfer speed
            if elapsed_time > 0:
                speed_kb = round(transfer_info['bytes_received'] / 1024 / elapsed_time, 2)
            else:
                speed_kb = 0
            
            # Check for stalled transfers
            if (transfer_info['state'] == TransferState.TRANSFERRING and 
                time.time() - transfer_info['last_update'] > 30):
                # Transfer has stalled
                status = 'stalled'
                message = f"Transfer stalled for {int(time.time() - transfer_info['last_update'])} seconds"
            else:
                status = transfer_info['state']
                message = f"Transfer is {status}"
            
            # Format file size
            if transfer_info['file_size'] > 0:
                file_size_formatted = self._format_bytes(transfer_info['file_size'])
            else:
                file_size_formatted = "Unknown"
            
            # Prepare response
            response = {
                'success': True,
                'status': status,
                'message': message,
                'progress': transfer_info.get('progress', 0),
                'bytes_received': transfer_info['bytes_received'],
                'chunks_received': transfer_info['chunks_received'],
                'file_size': transfer_info['file_size'],
                'file_size_formatted': file_size_formatted,
                'file_name': transfer_info['file_name'],
                'elapsed_time': elapsed_time,
                'speed_kb': speed_kb,
                'last_update': transfer_info['last_update'],
                'should_continue_polling': status in [TransferState.INITIALIZING, TransferState.TRANSFERRING, TransferState.VERIFYING]
            }
            
            # Add download URL if transfer is complete
            if status == TransferState.COMPLETED:
                response['download_url'] = f"/static/backups/{client_id}/{transfer_info['file_name']}"
                response['polling_complete'] = True
            
            # Add error message if transfer failed
            if status == TransferState.FAILED:
                response['error_message'] = transfer_info.get('error_message', "Unknown error")
                response['polling_complete'] = True
            
            return response
    
    def resume_transfer(self, client_id):
        """Resume a paused or stalled transfer
        
        Args:
            client_id: Client identifier
            
        Returns:
            Dictionary with resume status
        """
        with self.transfer_lock:
            if client_id not in self.active_transfers:
                return {
                    'success': False,
                    'message': "No active transfer found"
                }
            
            transfer_info = self.active_transfers[client_id]
            
            # Check if transfer can be resumed
            if transfer_info['state'] in [TransferState.PAUSED, TransferState.TRANSFERRING]:
                transfer_info['state'] = TransferState.TRANSFERRING
                transfer_info['last_update'] = time.time()
                self._save_state()
                
                return {
                    'success': True,
                    'message': "Transfer resumed",
                    'transfer_info': transfer_info
                }
            else:
                return {
                    'success': False,
                    'message': f"Transfer is in state {transfer_info['state']}, cannot resume"
                }
    
    def cancel_transfer(self, client_id):
        """Cancel an active transfer
        
        Args:
            client_id: Client identifier
            
        Returns:
            Dictionary with cancellation status
        """
        with self.transfer_lock:
            if client_id not in self.active_transfers:
                return {
                    'success': False,
                    'message': "No active transfer found"
                }
            
            transfer_info = self.active_transfers[client_id]
            
            # Check if there's a temp file to clean up
            if os.path.exists(transfer_info['temp_path']):
                try:
                    # Save a copy for debugging if it's substantial
                    if os.path.getsize(transfer_info['temp_path']) > 1024*1024:  # 1MB
                        cancelled_path = f"{transfer_info['temp_path']}.cancelled"
                        shutil.copy2(transfer_info['temp_path'], cancelled_path)
                        logger.info(f"[ROBUST-TRANSFER] Saved partial transfer to {cancelled_path}")
                    
                    # Remove the temp file
                    os.remove(transfer_info['temp_path'])
                    logger.info(f"[ROBUST-TRANSFER] Removed partial file: {transfer_info['temp_path']}")
                except Exception as e:
                    logger.error(f"[ROBUST-TRANSFER] Error cleaning up temp file: {e}")
            
            # Update transfer info
            transfer_info['state'] = TransferState.FAILED
            transfer_info['error_message'] = "Transfer cancelled by user"
            transfer_info['end_time'] = time.time()
            self._save_state()
            
            return {
                'success': True,
                'message': "Transfer cancelled",
                'bytes_received': transfer_info['bytes_received'],
                'chunks_received': transfer_info['chunks_received']
            }
    
    def _format_bytes(self, bytes_value):
        """Format bytes to human-readable string
        
        Args:
            bytes_value: Number of bytes
            
        Returns:
            Formatted string (e.g., "1.23 MB")
        """
        if bytes_value == 0:
            return "0 Bytes"
        
        sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB"]
        i = 0
        while bytes_value >= 1024 and i < len(sizes) - 1:
            bytes_value /= 1024
            i += 1
        
        return f"{bytes_value:.2f} {sizes[i]}"

# Create a singleton instance
_transfer_manager = None

def get_transfer_manager(backup_dir=None):
    """Get the singleton TransferManager instance
    
    Args:
        backup_dir: Directory where database backups are stored
        
    Returns:
        TransferManager instance
    """
    global _transfer_manager
    if _transfer_manager is None and backup_dir is not None:
        _transfer_manager = TransferManager(backup_dir)
    return _transfer_manager
