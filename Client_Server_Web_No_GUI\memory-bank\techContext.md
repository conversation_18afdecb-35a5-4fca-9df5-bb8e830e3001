# Technical Context

## Technology Stack

### Core Technologies
- **Python 3.10+**: Main programming language
- **Tkinter**: GUI framework for debug interface
- **Socket Programming**: TCP/IP communication protocol
- **Threading**: Concurrent operations and background tasks
- **JSON**: Configuration and message serialization

### Database Integration
- **Firebird Database**: Primary database system
- **ISQL**: Firebird command-line interface
- **SQL Parameterization**: Secure query execution
- **Connection Pooling**: Efficient database resource management

### Cloud Storage Integrations
- **MEGA API**: Cloud backup storage
- **Google Drive API**: Alternative cloud storage
- **OAuth 2.0**: Authentication for Google Drive
- **Service Account**: Server-to-server authentication

### Development Environment
- **Platform**: Windows 10+ (win32 10.0.26100)
- **Shell**: PowerShell (C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe)
- **Build Tools**: PyInstaller for executable generation
- **Package Management**: pip with requirements.txt

## Dependencies

### Core Python Packages
```
# From requirements.txt analysis
logging
threading
socket
json
tkinter
time
uuid
platform
traceback
ctypes
tempfile
subprocess
datetime
base64
stat
```

### External Libraries
```
# Cloud storage libraries
mega.py (or variants: mega_client_*.py)
google-api-python-client
google-auth
google-auth-oauthlib
google-auth-httplib2

# Database connectivity
fdb (Firebird database driver)
```

### System Dependencies
- **Firebird Database Engine**: Required for database operations
- **ISQL Executable**: Located at `C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe`
- **Windows API**: For mutex handling and window management

## Technical Constraints

### Environment Limitations
1. **Windows-Specific**: Code uses Windows-specific APIs (ctypes, mutex)
2. **Python 3.10+ Compatibility**: asyncio.coroutine deprecation affects MEGA client
3. **Single Instance**: Mutex prevents multiple client instances
4. **File System Dependencies**: Specific paths for ISQL and database files

### Performance Constraints
1. **Memory Usage**: Rotating log files limited to 5MB with 3 backups
2. **Socket Timeouts**: No timeout set (indefinite wait)
3. **Thread Limitations**: Each connection uses dedicated threads
4. **File Transfer**: Chunked transfers for large database files

### Security Constraints
1. **Plain Text Config**: Passwords stored in JSON configuration
2. **No Encryption**: TCP connections not encrypted by default
3. **File Permissions**: Configuration files need access control
4. **Credential Management**: MEGA and GDrive credentials in config

## Development Setup

### Project Structure
```
staging_build/
├── ifess_client_hidden.py      # Main hidden client service
├── ifess_client_debug.py       # Debug GUI application
├── client_config.json          # Configuration file
├── token.json                  # OAuth tokens (generated)
├── common/                     # Shared modules
│   ├── network.py             # Network protocol
│   └── db_utils.py            # Database utilities
├── mega_client_*.py           # MEGA client variants
├── gdrive_client_*.py         # GDrive client variants
├── *.spec                     # PyInstaller specifications
└── build_*.bat               # Build scripts
```

### Build Configuration
- **PyInstaller**: Used for creating standalone executables
- **Spec Files**: Define build parameters and dependencies
- **Batch Scripts**: Automate build process
- **Icon Resources**: MAINICON.ico for application branding

### Configuration Management
```json
{
  "server_address": "localhost",
  "server_port": 5555,
  "reconnect_interval": 5,
  "client_id": "client_coba",
  "display_name": "FDB-Client-Monitoring",
  "database": {
    "path": "D:/path/to/database.FDB",
    "username": "sysdba",
    "password": "masterkey",
    "isql_path": "C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe",
    "use_localhost": true
  },
  "mega": {
    "email": "<EMAIL>",
    "password": "password"
  }
}
```

## Integration Challenges

### MEGA Client Compatibility
```python
# Current fallback chain for MEGA client imports
Fallback Order:
1. mega_client_compiled (built-in patch)
2. mega_client_patched (Python 3.10+ compatibility)
3. mega_client_fixed (progress tracking)
4. mega_client_py310 (Python 3.10 specific)
5. mega_client (standard client)
6. Dummy client (disabled functionality)
```

**Issues:**
- asyncio.coroutine deprecated in Python 3.10+
- Import complexity makes debugging difficult
- No standardized error reporting across variants

### Google Drive Integration
```python
# OAuth vs Service Account authentication
Authentication Methods:
1. OAuth (gdrive_client_oauth_simple.py) - Personal accounts
2. Service Account (gdrive_client_module.py) - Server-to-server
```

**Challenges:**
- Token refresh handling
- Permission management for shared folders
- Rate limiting and quota management

### Network Protocol
```python
# Message format and handling
class NetworkMessage:
    def __init__(self, msg_type, data=None, request_id=None):
        self.msg_type = msg_type
        self.data = data
        self.request_id = request_id
        self.timestamp = time.time()
```

**Considerations:**
- JSON serialization for all messages
- No built-in message versioning
- Error handling for malformed messages

## Logging and Monitoring

### Log Management
```python
# Rotating file handler configuration
log_handler = RotatingFileHandler(
    LOG_FILE,
    maxBytes=5*1024*1024,  # 5 MB
    backupCount=3
)
```

### Log Format
```
%(asctime)s - %(name)s - %(levelname)s - %(message)s
```

### Debug Interface Log Monitoring
- Real-time log file reading
- Pattern matching for connection events
- Filter capabilities for different event types
- Export functionality for troubleshooting

## Security Considerations

### Current Security Model
1. **No Authentication**: Basic client registration without validation
2. **Plain Text Storage**: Credentials stored in configuration files
3. **Network Security**: TCP connections without encryption
4. **Access Control**: Relies on file system permissions

### Security Enhancements (Future)
1. **Credential Encryption**: Encrypt sensitive configuration data
2. **SSL/TLS**: Implement secure connections
3. **Authentication**: Add proper client authentication
4. **Input Validation**: Comprehensive message validation

## Platform-Specific Considerations

### Windows Integration
```python
# Windows-specific features used
- ctypes for Windows API calls
- Global mutex for single instance
- Windows-style file paths
- PowerShell as default shell
```

### File System
- Case-insensitive file paths
- Windows path separators
- Long path support considerations
- File locking for configuration files

## Performance Optimization Opportunities

### Current Performance Characteristics
- Single-threaded message processing
- Synchronous database operations
- No connection pooling
- File-based configuration only

### Optimization Strategies
1. **Async I/O**: Consider asyncio for network operations
2. **Connection Pooling**: Database connection management
3. **Message Queuing**: Asynchronous message processing
4. **Caching**: Configuration and status caching
5. **Compression**: Large file transfer optimization 