# IFESS Google Drive Token (Updated)

Dokumen ini menjelaskan penggunaan token Google Drive yang disimpan di Google Drive untuk aplikasi IFESS, dengan fitur pemeriksaan kedaluwarsa proaktif dan GUI pengujian.

## Fitur Baru

1. **<PERSON><PERSON><PERSON><PERSON><PERSON>sa Proaktif**
   - Memeriksa apakah token akan kedaluwarsa dalam waktu dekat (default: 1 jam)
   - Mengunduh token baru dari Google Drive sebelum token lama kedaluwarsa
   - Menghindari gangguan layanan karena token kedaluwarsa

2. **GUI Pengujian Google Drive**
   - Antarmuka grafis untuk menguji upload file ke Google Drive
   - Menampilkan informasi token dan status kedaluwarsa
   - Memungkinkan pengujian upload file dengan mudah
   - Menampilkan hasil upload termasuk link ke file

## Cara Menggunakan

### Menjalankan Aplikasi dengan Token dari Google Drive

Gunakan script `run_with_gdrive_token.bat` untuk menjalankan aplikasi dengan token dari Google Drive:

```
run_with_gdrive_token.bat
```

Script ini akan:
- <PERSON><PERSON>tal dependensi yang diperlukan (termasuk python-dateutil)
- Mengunduh token dari Google Drive
- Menjalankan server web, client hidden, dan client debug

### Menjalankan GUI Pengujian

Gunakan script `run_gdrive_test_gui.bat` untuk menjalankan GUI pengujian:

```
run_gdrive_test_gui.bat
```

GUI pengujian memungkinkan Anda untuk:
- Mengunduh token dari Google Drive
- Memeriksa informasi token dan status kedaluwarsa
- Memilih file untuk diupload
- Melihat progres upload
- Melihat hasil upload termasuk link ke file

## Konfigurasi

### Token Google Drive

Token OAuth disimpan di Google Drive dan dapat diakses melalui link:
https://drive.google.com/file/d/1q2fJ-ORqJj2TQwdTKQgXG6RqJ7WqENAh/view?usp=sharing

Jika token perlu diperbarui:
1. Jalankan aplikasi di komputer dengan akses internet
2. Biarkan aplikasi melakukan refresh token
3. Salin file `token.json` yang diperbarui
4. Upload ke Google Drive dengan ID yang sama

### Threshold Kedaluwarsa

Secara default, token akan dianggap "akan kedaluwarsa segera" jika waktu yang tersisa kurang dari 1 jam (3600 detik). Anda dapat mengubah nilai ini di file `token_downloader.py` dengan mengedit parameter `threshold_seconds` di fungsi `is_token_expiring_soon()`.

## Troubleshooting

### Masalah Mengunduh Token

Jika token tidak dapat diunduh dari Google Drive:
1. Pastikan komputer memiliki akses internet
2. Periksa apakah link Google Drive masih valid
3. Aplikasi akan mencoba menggunakan token lokal jika ada
4. Jika tidak ada token lokal, aplikasi akan mencoba OAuth flow (memerlukan interaksi pengguna)

### Masalah Format Tanggal

Jika terjadi error saat memeriksa kedaluwarsa token:
1. Pastikan python-dateutil terinstal: `pip install python-dateutil`
2. Periksa format tanggal di token.json
3. Jika masih bermasalah, aplikasi akan menganggap token akan kedaluwarsa segera dan mencoba mengunduh token baru

## Informasi Debug

Aplikasi debug (ifess_client_debug.py) dan GUI pengujian (ifess_gdrive_test_gui.py) menampilkan informasi tentang status token dan upload Google Drive. Gunakan aplikasi ini untuk memantau proses dan melihat pesan error jika terjadi masalah.
