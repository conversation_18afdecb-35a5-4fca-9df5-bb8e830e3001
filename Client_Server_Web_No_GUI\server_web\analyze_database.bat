@echo off
echo Firebird Database Analysis Tool
echo ============================
echo.

if "%~1"=="" (
    echo Usage: analyze_database.bat [database_file_path]
    echo.
    echo Example: analyze_database.bat "D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups\import_76b3e573\database_20250423_105816.fdb"
    echo.
    
    set /p DB_PATH=Enter the path to the database file to analyze: 
) else (
    set DB_PATH=%~1
)

echo.
echo Starting analysis of database: %DB_PATH%
echo.

python analyze_firebird_db.py "%DB_PATH%"

echo.
pause
