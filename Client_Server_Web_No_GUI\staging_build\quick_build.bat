@echo off
REM ===============================================
REM IFESS Client Suite - Quick Build Script
REM For development and testing purposes
REM ===============================================

title IFESS Quick Builder
color 0B

echo.
echo ===============================================
echo     IFESS Client Suite - Quick Builder
echo ===============================================
echo.

REM Change to script directory
cd /d "%~dp0"

REM Quick checks
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python not found!
    pause
    exit /b 1
)

python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo Installing PyInstaller...
    pip install pyinstaller
)

REM Clean old builds
if exist "dist" rmdir /s /q "dist" 2>nul
mkdir "dist" 2>nul

echo Building applications...

REM Build Hidden Client
echo [1/3] Building Hidden Client...
python -m PyInstaller --noconfirm --onefile --noconsole ^
    --name "ifess_client_hidden" ^
    --add-data "client_config.json;." ^
    --add-data "client_config_oauth_tokens.json;." ^
    --add-data "token.json;." ^
    --add-data "gdrive_client_oauth_simple.py;." ^
    --add-data "common;common" ^
    --hidden-import "fdb" ^
    --hidden-import "googleapiclient.discovery" ^
    --hidden-import "google.oauth2.service_account" ^
    --hidden-import "schedule" ^
    "ifess_client_hidden.py" >nul 2>&1

if exist "dist\ifess_client_hidden.exe" (
    echo ✓ Hidden Client - Success
) else (
    echo ✗ Hidden Client - Failed
)

REM Create Debug Batch File
echo [2/3] Creating Debug Batch File...
echo @echo off > "dist\debug_hidden_client.bat"
echo title IFESS Hidden Client - DEBUG MODE >> "dist\debug_hidden_client.bat"
echo echo ============================================ >> "dist\debug_hidden_client.bat"
echo echo   IFESS Hidden Client - DEBUG MODE >> "dist\debug_hidden_client.bat"
echo echo ============================================ >> "dist\debug_hidden_client.bat"
echo echo. >> "dist\debug_hidden_client.bat"
echo echo Starting IFESS hidden client in debug mode... >> "dist\debug_hidden_client.bat"
echo echo This window will show real-time debug output and error messages. >> "dist\debug_hidden_client.bat"
echo echo Press Ctrl+C to stop the client. >> "dist\debug_hidden_client.bat"
echo echo. >> "dist\debug_hidden_client.bat"
echo "ifess_client_hidden.exe" --debug >> "dist\debug_hidden_client.bat"
echo echo. >> "dist\debug_hidden_client.bat"
echo echo ============================================ >> "dist\debug_hidden_client.bat"
echo echo   Hidden client has stopped >> "dist\debug_hidden_client.bat"
echo echo ============================================ >> "dist\debug_hidden_client.bat"
echo pause >> "dist\debug_hidden_client.bat"

if exist "dist\debug_hidden_client.bat" (
    echo ✓ Debug Batch File - Success
) else (
    echo ✗ Debug Batch File - Failed
)

REM Build Config GUI
echo [3/3] Building Config GUI...
python -m PyInstaller --noconfirm --onefile --noconsole ^
    --name "ifess_config_gui" ^
    --add-data "client_config.json;." ^
    --add-data "client_config_oauth_tokens.json;." ^
    --add-data "gdrive_client_oauth_simple.py;." ^
    --add-data "common;common" ^
    --hidden-import "tkinter" ^
    --hidden-import "tkinter.ttk" ^
    --hidden-import "googleapiclient.discovery" ^
    --hidden-import "fdb" ^
    "ifess_config_gui.py" >nul 2>&1

if exist "dist\ifess_config_gui.exe" (
    echo ✓ Config GUI - Success
) else (
    echo ✗ Config GUI - Failed
)

REM Copy credential files for portable operation
echo Copying credential files...
if exist "token.json" copy "token.json" "dist\" >nul 2>&1
if exist "token_backup.json" copy "token_backup.json" "dist\" >nul 2>&1
if exist "ptrj-backup-services-account.json" copy "ptrj-backup-services-account.json" "dist\" >nul 2>&1
if exist "client_secret.json" copy "client_secret.json" "dist\" >nul 2>&1
if exist "client_secrets.json" copy "client_secrets.json" "dist\" >nul 2>&1

REM Set up OAuth config as primary
cd dist
if exist "client_config_oauth_tokens.json" (
    if exist "client_config.json" copy "client_config.json" "client_config_basic_backup.json" >nul 2>&1
    copy "client_config_oauth_tokens.json" "client_config.json" >nul 2>&1
    echo ✓ OAuth configuration set as primary
)
cd ..

echo.
echo Build completed!
echo ✓ Executables ready in dist\ folder
echo ✓ Debug batch file created for troubleshooting
echo ✓ Credential files copied for portable operation
echo ✓ OAuth configuration set as primary

pause 