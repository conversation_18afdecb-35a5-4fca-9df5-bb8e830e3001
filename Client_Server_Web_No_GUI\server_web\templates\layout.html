<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Firebird Query Server{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-database me-2"></i>Firebird Query Server
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('index') %}active{% endif %}" href="{{ url_for('index') }}">
                            <i class="fas fa-home me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('client_list') %}active{% endif %}" href="{{ url_for('client_list') }}">
                            <i class="fas fa-laptop me-1"></i>Clients
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('query_page') %}active{% endif %}" href="{{ url_for('query_page') }}">
                            <i class="fas fa-code me-1"></i>Query
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('monitoring') %}active{% endif %}" href="{{ url_for('monitoring') }}">
                            <i class="fas fa-chart-line me-1"></i>Monitoring
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('query_history_page') %}active{% endif %}" href="{{ url_for('query_history_page') }}">
                            <i class="fas fa-history me-1"></i>History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == url_for('backups_page') or request.path == url_for('backup_page') %}active{% endif %}" href="{{ url_for('backup_page') }}">
                            <i class="fas fa-download me-1"></i>Import
                        </a>
                    </li>
                </ul>
                <div class="ms-auto d-flex">
                    <div class="server-status me-3">
                        <span id="server-status-indicator" class="badge {% if server_status == 'Running' %}bg-success{% else %}bg-danger{% endif %}">
                            <i class="fas {% if server_status == 'Running' %}fa-play{% else %}fa-stop{% endif %} me-1"></i>
                            <span id="server-status-text">{{ server_status }}</span>
                        </span>
                    </div>
                    <div class="server-controls">
                        <button id="start-server-btn" class="btn btn-sm btn-success {% if server_status == 'Running' %}d-none{% endif %}">
                            <i class="fas fa-play me-1"></i>Start
                        </button>
                        <button id="stop-server-btn" class="btn btn-sm btn-danger {% if server_status != 'Running' %}d-none{% endif %}">
                            <i class="fas fa-stop me-1"></i>Stop
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-5 py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">Firebird Query Server &copy; 2025</span>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
