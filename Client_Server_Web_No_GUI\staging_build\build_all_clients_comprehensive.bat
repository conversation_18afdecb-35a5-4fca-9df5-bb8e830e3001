@echo off
REM ===============================================
REM IFESS Client Suite - Comprehensive Build Script
REM ===============================================
REM Builds two IFESS client applications:
REM 1. ifess_client_hidden.exe - Background service
REM 2. ifess_config_gui.exe - Configuration GUI
REM Note: Debug functionality provided by debug_hidden_client.bat
REM ===============================================

setlocal enabledelayedexpansion

REM Set script title and colors
title IFESS Client Suite - Comprehensive Builder
color 0F

REM Set build configuration
set BUILD_DATE=%date:~-4,4%-%date:~-10,2%-%date:~-7,2%
set BUILD_TIME=%time:~0,2%-%time:~3,2%-%time:~6,2%
set LOG_FILE=build_log_%BUILD_DATE%_%BUILD_TIME%.txt
set ERROR_COUNT=0
set WARNING_COUNT=0
set SUCCESS_COUNT=0

echo.
echo ===============================================
echo        IFESS Client Suite Builder
echo ===============================================
echo Build Date: %BUILD_DATE%
echo Build Time: %BUILD_TIME%
echo Log File: %LOG_FILE%
echo.

REM Create log file header
echo IFESS Client Suite - Comprehensive Build Log > %LOG_FILE%
echo Build started: %date% %time% >> %LOG_FILE%
echo ================================================ >> %LOG_FILE%
echo. >> %LOG_FILE%

REM Change to script directory
cd /d "%~dp0"
echo Current directory: %CD%
echo Current directory: %CD% >> %LOG_FILE%
echo.

REM ===============================================
REM Pre-build checks and environment preparation
REM ===============================================

echo [1/7] Performing pre-build checks...
echo [1/7] Performing pre-build checks... >> %LOG_FILE%

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH!
    echo ERROR: Python is not installed or not in PATH! >> %LOG_FILE%
    set /a ERROR_COUNT+=1
    goto :error_exit
)

python --version
python --version >> %LOG_FILE%

REM Check PyInstaller installation
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PyInstaller is not installed!
    echo Installing PyInstaller...
    echo ERROR: PyInstaller is not installed! >> %LOG_FILE%
    echo Installing PyInstaller... >> %LOG_FILE%
    
    REM Remove conflicting pathlib package (Python 3.13+ issue)
    echo Removing obsolete pathlib package... >> %LOG_FILE%
    pip uninstall pathlib -y >> %LOG_FILE% 2>&1
    
    pip install pyinstaller >> %LOG_FILE% 2>&1
    if errorlevel 1 (
        echo ERROR: Failed to install PyInstaller!
        echo ERROR: Failed to install PyInstaller! >> %LOG_FILE%
        set /a ERROR_COUNT+=1
        goto :error_exit
    )
)

REM Verify PyInstaller command line access
python -m PyInstaller --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: PyInstaller not accessible via python -m PyInstaller
    echo ERROR: PyInstaller not accessible via python -m PyInstaller >> %LOG_FILE%
    set /a ERROR_COUNT+=1
    goto :error_exit
) else (
    echo PyInstaller command line access verified
    echo PyInstaller command line access verified >> %LOG_FILE%
)

REM Check UPX availability (optional)
upx --version >nul 2>&1
if errorlevel 1 (
    echo WARNING: UPX not found - compression will be disabled
    echo WARNING: UPX not found - compression will be disabled >> %LOG_FILE%
    set /a WARNING_COUNT+=1
) else (
    echo UPX compression available
    echo UPX compression available >> %LOG_FILE%
)

REM Verify source files exist
echo Checking source files...
echo Checking source files... >> %LOG_FILE%

set SOURCE_FILES=ifess_client_hidden.py ifess_config_gui.py

for %%f in (%SOURCE_FILES%) do (
    if not exist "%%f" (
        echo ERROR: Source file not found: %%f
        echo ERROR: Source file not found: %%f >> %LOG_FILE%
        set /a ERROR_COUNT+=1
    ) else (
        echo Found: %%f
        echo Found: %%f >> %LOG_FILE%
    )
)

if !ERROR_COUNT! gtr 0 (
    goto :error_exit
)

echo Pre-build checks completed successfully
echo Pre-build checks completed successfully >> %LOG_FILE%
echo.

REM ===============================================
REM Clean build environment
REM ===============================================

echo [2/7] Cleaning build environment...
echo [2/7] Cleaning build environment... >> %LOG_FILE%

REM Create fresh dist directory
if exist "dist" (
    echo Removing old dist directory...
    echo Removing old dist directory... >> %LOG_FILE%
    rmdir /s /q "dist" 2>nul
)

if exist "build" (
    echo Removing old build directory...
    echo Removing old build directory... >> %LOG_FILE%
    rmdir /s /q "build" 2>nul
)

mkdir "dist" 2>nul
mkdir "build" 2>nul

echo Build environment cleaned
echo Build environment cleaned >> %LOG_FILE%
echo.

REM ===============================================
REM Install/Update dependencies
REM ===============================================

echo [3/7] Checking and installing dependencies...
echo [3/7] Checking and installing dependencies... >> %LOG_FILE%

REM Core dependencies
set DEPENDENCIES=google-api-python-client google-auth google-auth-oauthlib google-auth-httplib2 requests fdb schedule

echo Installing/updating Python dependencies...
echo Installing/updating Python dependencies... >> %LOG_FILE%

for %%d in (%DEPENDENCIES%) do (
    echo Installing: %%d
    echo Installing: %%d >> %LOG_FILE%
    pip install --upgrade %%d >> %LOG_FILE% 2>&1
    if errorlevel 1 (
        echo WARNING: Failed to install %%d
        echo WARNING: Failed to install %%d >> %LOG_FILE%
        set /a WARNING_COUNT+=1
    )
)

echo Dependencies check completed
echo Dependencies check completed >> %LOG_FILE%
echo.

REM ===============================================
REM Build IFESS Hidden Client
REM ===============================================

echo [4/7] Building IFESS Hidden Client...
echo [4/7] Building IFESS Hidden Client... >> %LOG_FILE%

echo Building ifess_client_hidden.exe...
echo Building ifess_client_hidden.exe... >> %LOG_FILE%

python -m PyInstaller --noconfirm ifess_client_hidden_comprehensive.spec >> %LOG_FILE% 2>&1

if errorlevel 1 (
    echo ERROR: Failed to build ifess_client_hidden.exe
    echo ERROR: Failed to build ifess_client_hidden.exe >> %LOG_FILE%
    set /a ERROR_COUNT+=1
) else (
    if exist "dist\ifess_client_hidden.exe" (
        echo SUCCESS: ifess_client_hidden.exe built successfully
        echo SUCCESS: ifess_client_hidden.exe built successfully >> %LOG_FILE%
        set /a SUCCESS_COUNT+=1
        
        REM Get file size
        for %%i in ("dist\ifess_client_hidden.exe") do set HIDDEN_SIZE=%%~zi
        echo File size: !HIDDEN_SIZE! bytes
        echo File size: !HIDDEN_SIZE! bytes >> %LOG_FILE%
    ) else (
        echo ERROR: ifess_client_hidden.exe not found in dist directory
        echo ERROR: ifess_client_hidden.exe not found in dist directory >> %LOG_FILE%
        set /a ERROR_COUNT+=1
    )
)

echo.

REM ===============================================
REM Skip Debug Client - Using Batch File Instead
REM ===============================================

echo [5/7] Skipping debug client build (using batch file for debugging)...
echo [5/7] Skipping debug client build (using batch file for debugging)... >> %LOG_FILE%

echo Debug functionality will be provided by debug_hidden_client.bat
echo Debug functionality will be provided by debug_hidden_client.bat >> %LOG_FILE%

echo.

REM ===============================================
REM Build IFESS Config GUI
REM ===============================================

echo [6/7] Building IFESS Config GUI...
echo [6/7] Building IFESS Config GUI... >> %LOG_FILE%

echo Building ifess_config_gui.exe...
echo Building ifess_config_gui.exe... >> %LOG_FILE%

python -m PyInstaller --noconfirm ifess_config_gui_comprehensive.spec >> %LOG_FILE% 2>&1

if errorlevel 1 (
    echo ERROR: Failed to build ifess_config_gui.exe
    echo ERROR: Failed to build ifess_config_gui.exe >> %LOG_FILE%
    set /a ERROR_COUNT+=1
) else (
    if exist "dist\ifess_config_gui.exe" (
        echo SUCCESS: ifess_config_gui.exe built successfully
        echo SUCCESS: ifess_config_gui.exe built successfully >> %LOG_FILE%
        set /a SUCCESS_COUNT+=1
        
        REM Get file size
        for %%i in ("dist\ifess_config_gui.exe") do set GUI_SIZE=%%~zi
        echo File size: !GUI_SIZE! bytes
        echo File size: !GUI_SIZE! bytes >> %LOG_FILE%
    ) else (
        echo ERROR: ifess_config_gui.exe not found in dist directory
        echo ERROR: ifess_config_gui.exe not found in dist directory >> %LOG_FILE%
        set /a ERROR_COUNT+=1
    )
)

echo.

REM ===============================================
REM Post-build tasks and packaging
REM ===============================================

echo [7/7] Post-build tasks and packaging...
echo [7/7] Post-build tasks and packaging... >> %LOG_FILE%

REM Copy additional files to dist directory
echo Copying additional files to dist directory...
echo Copying additional files to dist directory... >> %LOG_FILE%

REM Copy configuration templates
if exist "client_config.json" (
    copy "client_config.json" "dist\" >nul 2>&1
    echo Copied: client_config.json
    echo Copied: client_config.json >> %LOG_FILE%
)

if exist "client_config_oauth_tokens.json" (
    copy "client_config_oauth_tokens.json" "dist\" >nul 2>&1
    echo Copied: client_config_oauth_tokens.json
    echo Copied: client_config_oauth_tokens.json >> %LOG_FILE%
)

REM Copy credential files
if exist "token.json" (
    copy "token.json" "dist\" >nul 2>&1
    echo Copied: token.json
    echo Copied: token.json >> %LOG_FILE%
)

if exist "token_backup.json" (
    copy "token_backup.json" "dist\" >nul 2>&1
    echo Copied: token_backup.json
    echo Copied: token_backup.json >> %LOG_FILE%
)

if exist "ptrj-backup-services-account.json" (
    copy "ptrj-backup-services-account.json" "dist\" >nul 2>&1
    echo Copied: ptrj-backup-services-account.json
    echo Copied: ptrj-backup-services-account.json >> %LOG_FILE%
)

if exist "client_secret.json" (
    copy "client_secret.json" "dist\" >nul 2>&1
    echo Copied: client_secret.json
    echo Copied: client_secret.json >> %LOG_FILE%
)

if exist "client_secrets.json" (
    copy "client_secrets.json" "dist\" >nul 2>&1
    echo Copied: client_secrets.json
    echo Copied: client_secrets.json >> %LOG_FILE%
)

REM Copy batch files
if exist "run_client_debug.bat" (
    copy "run_client_debug.bat" "dist\" >nul 2>&1
    echo Copied: run_client_debug.bat
    echo Copied: run_client_debug.bat >> %LOG_FILE%
)

if exist "run_client_with_debug_gui.bat" (
    copy "run_client_with_debug_gui.bat" "dist\" >nul 2>&1
    echo Copied: run_client_with_debug_gui.bat
    echo Copied: run_client_with_debug_gui.bat >> %LOG_FILE%
)

REM Create launcher batch files for the executables
echo Creating launcher batch files...
echo Creating launcher batch files... >> %LOG_FILE%

REM Hidden client launcher
echo @echo off > "dist\run_hidden_client.bat"
echo title IFESS Hidden Client >> "dist\run_hidden_client.bat"
echo start "" "ifess_client_hidden.exe" >> "dist\run_hidden_client.bat"
echo echo IFESS Hidden Client started in background >> "dist\run_hidden_client.bat"
echo pause >> "dist\run_hidden_client.bat"

REM Debug client launcher - runs hidden client with visible console
echo @echo off > "dist\debug_hidden_client.bat"
echo title IFESS Hidden Client - DEBUG MODE >> "dist\debug_hidden_client.bat"
echo echo ============================================ >> "dist\debug_hidden_client.bat"
echo echo   IFESS Hidden Client - DEBUG MODE >> "dist\debug_hidden_client.bat"
echo echo ============================================ >> "dist\debug_hidden_client.bat"
echo echo. >> "dist\debug_hidden_client.bat"
echo echo Starting IFESS hidden client in debug mode... >> "dist\debug_hidden_client.bat"
echo echo This window will show real-time debug output and error messages. >> "dist\debug_hidden_client.bat"
echo echo Press Ctrl+C to stop the client. >> "dist\debug_hidden_client.bat"
echo echo. >> "dist\debug_hidden_client.bat"
echo "ifess_client_hidden.exe" --debug >> "dist\debug_hidden_client.bat"
echo echo. >> "dist\debug_hidden_client.bat"
echo echo ============================================ >> "dist\debug_hidden_client.bat"
echo echo   Hidden client has stopped >> "dist\debug_hidden_client.bat"
echo echo ============================================ >> "dist\debug_hidden_client.bat"
echo pause >> "dist\debug_hidden_client.bat"

REM Config GUI launcher
echo @echo off > "dist\run_config_gui.bat"
echo title IFESS Configuration GUI >> "dist\run_config_gui.bat"
echo "ifess_config_gui.exe" >> "dist\run_config_gui.bat"

echo Created launcher batch files
echo Created launcher batch files >> %LOG_FILE%

REM Copy portable credentials setup script
if exist "setup_portable_credentials.bat" (
    copy "setup_portable_credentials.bat" "dist\" >nul 2>&1
    echo Copied: setup_portable_credentials.bat
    echo Copied: setup_portable_credentials.bat >> %LOG_FILE%
)

REM Set up portable credentials configuration
echo Setting up portable credentials configuration...
echo Setting up portable credentials configuration... >> %LOG_FILE%

cd dist
if exist "client_config_oauth_tokens.json" (
    if not exist "client_config_basic_backup.json" (
        if exist "client_config.json" (
            copy "client_config.json" "client_config_basic_backup.json" >nul 2>&1
            echo Backed up basic config >> %LOG_FILE%
        )
    )
    
    REM Use OAuth config as primary to ensure Google Drive credentials are available
    copy "client_config_oauth_tokens.json" "client_config.json" >nul 2>&1
    echo OAuth config set as primary configuration >> %LOG_FILE%
    echo OAuth configuration set as primary for Google Drive access
)
cd ..

REM Create README for distribution
echo Creating distribution README...
echo Creating distribution README... >> %LOG_FILE%

echo IFESS Client Suite - Standalone Distribution > "dist\README.txt"
echo ============================================== >> "dist\README.txt"
echo Built on: %date% %time% >> "dist\README.txt"
echo. >> "dist\README.txt"
echo This package contains two standalone IFESS applications and one debug tool: >> "dist\README.txt"
echo. >> "dist\README.txt"
echo 1. ifess_client_hidden.exe - Main background service client >> "dist\README.txt"
echo    - Run with: run_hidden_client.bat >> "dist\README.txt"
echo    - Runs silently in background >> "dist\README.txt"
echo. >> "dist\README.txt"
echo 2. debug_hidden_client.bat - Debug mode for hidden client >> "dist\README.txt"
echo    - Run with: debug_hidden_client.bat >> "dist\README.txt"
echo    - Shows console output and error messages for troubleshooting >> "dist\README.txt"
echo. >> "dist\README.txt"
echo 3. ifess_config_gui.exe - Configuration GUI >> "dist\README.txt"
echo    - Run with: run_config_gui.bat >> "dist\README.txt"
echo    - Configure database and server settings >> "dist\README.txt"
echo. >> "dist\README.txt"
echo CREDENTIAL FILES INCLUDED: >> "dist\README.txt"
echo - token.json: Google Drive authentication token >> "dist\README.txt"
echo - ptrj-backup-services-account.json: Service account credentials >> "dist\README.txt"
echo - client_secrets.json: OAuth client secrets >> "dist\README.txt"
echo - client_config_oauth_tokens.json: Full configuration with credentials >> "dist\README.txt"
echo. >> "dist\README.txt"
echo All executables are completely standalone and portable. >> "dist\README.txt"
echo Credential files are bundled and configured for immediate use. >> "dist\README.txt"
echo No additional authentication setup required. >> "dist\README.txt"
echo No Python installation or additional dependencies required. >> "dist\README.txt"

echo Created distribution README
echo Created distribution README >> %LOG_FILE%

REM ===============================================
REM Build summary and completion
REM ===============================================

echo.
echo ===============================================
echo           BUILD SUMMARY
echo ===============================================
echo Build completed: %date% %time%
echo Build completed: %date% %time% >> %LOG_FILE%
echo.
echo Results:
echo Results: >> %LOG_FILE%
echo   Successful builds: !SUCCESS_COUNT!/3
echo   Successful builds: !SUCCESS_COUNT!/3 >> %LOG_FILE%
echo   Errors: !ERROR_COUNT!
echo   Errors: !ERROR_COUNT! >> %LOG_FILE%
echo   Warnings: !WARNING_COUNT!
echo   Warnings: !WARNING_COUNT! >> %LOG_FILE%
echo.

if exist "dist\ifess_client_hidden.exe" (
    echo ✓ ifess_client_hidden.exe - Ready (!HIDDEN_SIZE! bytes^)
    echo ✓ ifess_client_hidden.exe - Ready (!HIDDEN_SIZE! bytes^) >> %LOG_FILE%
)

if exist "dist\ifess_client_debug.exe" (
    echo ✓ ifess_client_debug.exe - Ready (!DEBUG_SIZE! bytes^)
    echo ✓ ifess_client_debug.exe - Ready (!DEBUG_SIZE! bytes^) >> %LOG_FILE%
)

if exist "dist\ifess_config_gui.exe" (
    echo ✓ ifess_config_gui.exe - Ready (!GUI_SIZE! bytes^)
    echo ✓ ifess_config_gui.exe - Ready (!GUI_SIZE! bytes^) >> %LOG_FILE%
)

echo.
echo Distribution ready in: %CD%\dist\
echo Distribution ready in: %CD%\dist\ >> %LOG_FILE%
echo Build log saved as: %LOG_FILE%
echo Build log saved as: %LOG_FILE% >> %LOG_FILE%

if !ERROR_COUNT! equ 0 (
    echo.
    echo ===============================================
    echo    BUILD COMPLETED SUCCESSFULLY!
    echo ===============================================
    echo All applications built and packaged successfully.
    echo Ready for distribution and deployment.
    echo ===============================================
    echo    BUILD COMPLETED SUCCESSFULLY! >> %LOG_FILE%
    color 0A
    goto :success_exit
) else (
    goto :error_exit
)

:error_exit
echo.
echo ===============================================
echo    BUILD FAILED!
echo ===============================================
echo Build failed with !ERROR_COUNT! error(s) and !WARNING_COUNT! warning(s).
echo Check the build log for details: %LOG_FILE%
echo ===============================================
echo    BUILD FAILED! >> %LOG_FILE%
color 0C
echo.
echo Press any key to exit...
pause >nul
exit /b 1

:success_exit
echo.
echo Press any key to exit...
pause >nul
exit /b 0 