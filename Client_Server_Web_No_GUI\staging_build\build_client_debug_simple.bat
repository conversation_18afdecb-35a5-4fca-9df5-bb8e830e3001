@echo off
setlocal enabledelayedexpansion

echo Building IFESS Client Debug Simple Application
echo ===================================

REM Menghentikan aplikasi yang sedang berjalan
echo Menghentikan aplikasi yang sedang berjalan...
taskkill /f /im ifess_client_debug_simple.exe 2>nul

REM Mendapatkan direktori saat ini
set "CURRENT_DIR=%~dp0"
set "CURRENT_DIR=%CURRENT_DIR:~0,-1%"
echo Direktori saat ini: %CURRENT_DIR%

REM Mendapatkan direktori output
set "OUTPUT_DIR=%CURRENT_DIR%\dist"
echo Output direktori: %OUTPUT_DIR%

REM Membersihkan direktori dist jika belum ada
if not exist "%OUTPUT_DIR%" (
    mkdir "%OUTPUT_DIR%"
)

REM Menghapus executable yang ada
echo Membersihkan direktori dist...
echo Menghapus executable yang ada...
if exist "%OUTPUT_DIR%\ifess_client_debug_simple.exe" (
    del /f /q "%OUTPUT_DIR%\ifess_client_debug_simple.exe"
)

REM Membersihkan direktori build
echo Membersihkan direktori build...
if exist "%CURRENT_DIR%\build" (
    rmdir /s /q "%CURRENT_DIR%\build"
)

REM Membersihkan spec file
echo Membersihkan spec file...
if exist "%CURRENT_DIR%\ifess_client_debug_simple.spec" (
    del /f /q "%CURRENT_DIR%\ifess_client_debug_simple.spec"
)

REM Menginstall PyInstaller
echo Menginstall PyInstaller...
python -m pip install pyinstaller

REM Menginstall paket yang diperlukan
echo Menginstall paket yang diperlukan...
python -m pip install mega.py
python -m pip install fdb
python -m pip install requests
python -m pip install google-auth google-api-python-client

REM Mengatasi masalah pathlib yang konflik dengan PyInstaller
echo Mengatasi masalah pathlib yang tidak kompatibel dengan PyInstaller...
python -m pip uninstall -y pathlib
echo Mencoba cara alternatif untuk menghapus pathlib...
"C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\PythonSoftwareFoundation.Python.3.13_qbz5n2kfra8p0\python.exe" -m pip uninstall -y pathlib
echo Paket pathlib berhasil dihapus.

REM Membangun aplikasi dengan PyInstaller
echo.
echo Membangun Aplikasi Client Debug Simple...
echo Ini mungkin membutuhkan beberapa menit...

python -m PyInstaller ^
    --name="ifess_client_debug_simple" ^
    --onefile ^
    --windowed ^
    --hidden-import=mega ^
    --hidden-import=fdb ^
    --hidden-import=socket ^
    --hidden-import=json ^
    --hidden-import=requests ^
    --hidden-import=googleapiclient ^
    --hidden-import=googleapiclient.discovery ^
    --hidden-import=google.oauth2 ^
    --hidden-import=google.oauth2.service_account ^
    --add-data "%CURRENT_DIR%\client_config.json;." ^
    --add-data "%CURRENT_DIR%\mega_client_py310.py;." ^
    --add-data "%CURRENT_DIR%\gdrive_client_module.py;." ^
    --add-data "%CURRENT_DIR%\ptrj-backup-services-account.json;." ^
    "%CURRENT_DIR%\ifess_client_debug_simple.py"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Gagal membangun client debug simple.
    pause
    exit /b 1
)

REM Menyalin file konfigurasi default dan kredensial jika belum ada
echo.
echo Menyalin file konfigurasi default dan kredensial...
if not exist "%OUTPUT_DIR%\client_config.json" (
    copy "%CURRENT_DIR%\client_config.json" "%OUTPUT_DIR%\client_config.json"
)
if not exist "%OUTPUT_DIR%\ptrj-backup-services-account.json" (
    copy "%CURRENT_DIR%\ptrj-backup-services-account.json" "%OUTPUT_DIR%\ptrj-backup-services-account.json"
)

echo.
echo Build Client Debug Simple selesai dengan sukses!
echo Executable tersedia di folder '%OUTPUT_DIR%'.
echo.

pause
