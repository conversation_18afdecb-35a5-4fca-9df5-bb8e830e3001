#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Firebird Database Analysis Tool

This tool analyzes a Firebird database file and provides detailed diagnostics
about its structure, header, and potential issues.

Usage:
    python analyze_firebird_db.py <path_to_database_file>

Example:
    python analyze_firebird_db.py D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups\import_76b3e573\database_20250423_105816.fdb
"""

import os
import sys
import time
import logging
import argparse
import binascii
import struct

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('analyze_firebird_db.log')
    ]
)
logger = logging.getLogger('analyze_firebird_db')

# Firebird signatures to check for in the database header
FIREBIRD_SIGNATURES = [
    b'Firebird', 
    b'INTERBASE', 
    b'GENERAL', 
    b'ODS',
    b'FILE FORMAT', 
    b'Database', 
    b'\x01\x00\x09\x00',  # ODS signature
    b'\x16\x00',  # Firebird/InterBase ODS marker
]

def check_file_exists(file_path):
    """Check if the file exists and has content"""
    if not os.path.exists(file_path):
        logger.error(f"File does not exist: {file_path}")
        return False, 0
        
    file_size = os.path.getsize(file_path)
    if file_size == 0:
        logger.error(f"File is empty: {file_path}")
        return False, 0
        
    logger.info(f"File exists and has size: {file_size} bytes")
    return True, file_size

def analyze_header(file_path):
    """Analyze the database header"""
    try:
        with open(file_path, 'rb') as f:
            # Read the first 8KB for analysis
            header = f.read(8192)
            
            print("\n=== HEADER ANALYSIS ===")
            print(f"Header size: {len(header)} bytes")
            
            # Check for common Firebird signatures
            found_signatures = []
            for signature in FIREBIRD_SIGNATURES:
                if signature in header:
                    found_signatures.append(signature)
                    pos = header.find(signature)
                    print(f"Found signature '{signature}' at position {pos}")
            
            if not found_signatures:
                print("WARNING: No known Firebird signatures found in header!")
            
            # Check for null bytes at the beginning
            null_count = 0
            for b in header[:100]:
                if b == 0:
                    null_count += 1
                else:
                    break
            
            if null_count > 0:
                print(f"WARNING: Found {null_count} null bytes at the beginning of the file")
            
            # Find first non-null byte
            first_non_null = 0
            while first_non_null < len(header) and header[first_non_null] == 0:
                first_non_null += 1
            
            if first_non_null < len(header):
                print(f"First non-null byte at position {first_non_null}: 0x{header[first_non_null]:02x}")
            else:
                print("WARNING: Header consists entirely of null bytes!")
            
            # Print hex dump of first 256 bytes
            print("\n=== HEX DUMP (first 256 bytes) ===")
            hex_dump(header[:256])
            
            # Print ASCII representation
            print("\n=== ASCII REPRESENTATION (first 256 bytes) ===")
            ascii_dump(header[:256])
            
            return found_signatures
    except Exception as e:
        logger.error(f"Error analyzing header: {e}")
        return []

def analyze_file_structure(file_path, file_size):
    """Analyze the overall file structure"""
    try:
        print("\n=== FILE STRUCTURE ANALYSIS ===")
        print(f"File size: {file_size} bytes ({format_size(file_size)})")
        
        # Check if file size is reasonable for a Firebird database
        if file_size < 1024:
            print("WARNING: File is too small to be a valid Firebird database")
        elif file_size < 1024 * 1024:
            print("WARNING: File is smaller than 1MB, which is unusual for a Firebird database")
        
        # Check for page boundaries
        page_sizes = [1024, 2048, 4096, 8192, 16384]
        for page_size in page_sizes:
            if file_size % page_size == 0:
                print(f"File size is a multiple of {page_size} bytes (potential page size)")
        
        # Sample data from different parts of the file
        with open(file_path, 'rb') as f:
            # Beginning
            f.seek(0)
            beginning = f.read(1024)
            
            # Middle
            f.seek(file_size // 2)
            middle = f.read(1024)
            
            # End
            f.seek(max(0, file_size - 1024))
            end = f.read(1024)
        
        # Check for all zeros in these sections
        if all(b == 0 for b in beginning):
            print("WARNING: Beginning of file consists entirely of null bytes")
        
        if all(b == 0 for b in middle):
            print("WARNING: Middle of file consists entirely of null bytes")
        
        if all(b == 0 for b in end):
            print("WARNING: End of file consists entirely of null bytes")
        
        # Check for repeating patterns
        check_repeating_patterns(beginning, "beginning")
        check_repeating_patterns(middle, "middle")
        check_repeating_patterns(end, "end")
        
    except Exception as e:
        logger.error(f"Error analyzing file structure: {e}")

def check_repeating_patterns(data, section_name):
    """Check for repeating patterns in the data"""
    if len(data) < 16:
        return
    
    # Check for repeating 4-byte patterns
    for pattern_size in [4, 8, 16]:
        if len(data) < pattern_size * 4:
            continue
            
        pattern = data[:pattern_size]
        repeats = 1
        
        for i in range(pattern_size, len(data), pattern_size):
            if i + pattern_size <= len(data) and data[i:i+pattern_size] == pattern:
                repeats += 1
            else:
                break
        
        if repeats > 3:
            print(f"Found repeating {pattern_size}-byte pattern in {section_name} section ({repeats} times)")
            print(f"Pattern: {binascii.hexlify(pattern)}")
            return

def hex_dump(data, bytes_per_line=16):
    """Print a hex dump of the data"""
    for i in range(0, len(data), bytes_per_line):
        line = data[i:i+bytes_per_line]
        hex_values = ' '.join(f'{b:02x}' for b in line)
        print(f"{i:04x}: {hex_values}")

def ascii_dump(data, bytes_per_line=16):
    """Print an ASCII representation of the data"""
    for i in range(0, len(data), bytes_per_line):
        line = data[i:i+bytes_per_line]
        ascii_values = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in line)
        print(f"{i:04x}: {ascii_values}")

def format_size(size_bytes):
    """Format file size in human-readable format"""
    if size_bytes < 1024:
        return f"{size_bytes} B"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes/1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes/(1024*1024):.1f} MB"
    else:
        return f"{size_bytes/(1024*1024*1024):.1f} GB"

def provide_recommendations(file_path, found_signatures, file_size):
    """Provide recommendations based on the analysis"""
    print("\n=== RECOMMENDATIONS ===")
    
    if not found_signatures:
        print("1. The file does not appear to be a valid Firebird database.")
        print("2. Try running the repair_firebird_db.py script to attempt repair.")
        print("3. Check if there are any backup files (.first_chunk, .header) in the same directory.")
        print("4. If repair fails, try requesting a new database transfer from the client.")
    elif file_size < 1024 * 1024:
        print("1. The file has Firebird signatures but is unusually small.")
        print("2. This might indicate an incomplete transfer or corruption.")
        print("3. Try running the repair_firebird_db.py script to attempt repair.")
        print("4. If repair fails, try requesting a new database transfer from the client.")
    else:
        print("1. The file appears to be a Firebird database but might have issues.")
        print("2. Try opening it with FlameRobin or another Firebird client.")
        print("3. If that fails, run the repair_firebird_db.py script to attempt repair.")
        print("4. Check server logs for any errors during the transfer process.")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Analyze a Firebird database file')
    parser.add_argument('file_path', help='Path to the database file to analyze')
    args = parser.parse_args()
    
    file_path = args.file_path
    
    print(f"\nAnalyzing database file: {file_path}")
    
    # Check if file exists
    exists, file_size = check_file_exists(file_path)
    if not exists:
        print(f"Error: File does not exist or is empty: {file_path}")
        return 1
    
    # Analyze header
    found_signatures = analyze_header(file_path)
    
    # Analyze file structure
    analyze_file_structure(file_path, file_size)
    
    # Provide recommendations
    provide_recommendations(file_path, found_signatures, file_size)
    
    print("\nAnalysis complete. See analyze_firebird_db.log for details.")
    return 0

if __name__ == '__main__':
    sys.exit(main())
