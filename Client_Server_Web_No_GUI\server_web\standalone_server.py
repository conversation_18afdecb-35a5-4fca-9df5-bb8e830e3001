from flask import Flask, render_template, jsonify, request
import os
import json
import time
import datetime

app = Flask(__name__)

# Dummy data to simulate query results
dummy_results = {
    'monitor_diff_job_field': {
        'headers': ['Ka<PERSON>wan<PERSON>', 'OvertimeID', 'TanggalOvertime', 'Jam<PERSON>er<PERSON>', 'Kode<PERSON>ield', 'AccCode', 'DeskripsiPekerjaan', 'TarifDasar', 'TarifTambahan', '<PERSON><PERSON>Dasar', '<PERSON><PERSON><PERSON><PERSON>bahan', 'Catatan', 'KodeField_2Char', 'AccCode_2Char', 'NomorKendaraan', '<PERSON><PERSON>endaraan', 'VehicleID'],
        'rows': [
            {
                'KaryawanID': '23',
                'OvertimeID': '53204',
                'TanggalOvertime': '2025-04-07',
                'JamKerja': '2.00',
                'KodeField': 'YYYYY',
                'AccCode': 'GA9234',
                '<PERSON><PERSON><PERSON>siPekerjaan': 'UPKEEP OF BUILDINGS',
                'TarifDasar': '10000',
                '<PERSON>ri<PERSON><PERSON><PERSON>bahan': '5000',
                'NilaiDasar': '20000',
                '<PERSON><PERSON><PERSON><PERSON>bahan': '10000',
                'Catatan': 'Test data',
                'KodeField_2Char': 'YY',
                'AccCode_2Char': 'GA',
                'NomorKendaraan': '',
                'ModelKendaraan': '',
                'VehicleID': ''
            },
            {
                'KaryawanID': '23',
                'OvertimeID': '53210',
                'TanggalOvertime': '2025-04-07',
                'JamKerja': '2.00',
                'KodeField': 'YYYYY',
                'AccCode': 'GA9234',
                'DeskripsiPekerjaan': 'UPKEEP OF BUILDINGS',
                'TarifDasar': '10000',
                'TarifTambahan': '5000',
                'NilaiDasar': '20000',
                'NilaiTambahan': '10000',
                'Catatan': 'Test data 2',
                'KodeField_2Char': 'YY',
                'AccCode_2Char': 'GA',
                'NomorKendaraan': '',
                'ModelKendaraan': '',
                'VehicleID': ''
            }
        ]
    }
}

# Load predefined queries
predefined_queries = {}
queries_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'queries')
if os.path.exists(queries_dir):
    for filename in os.listdir(queries_dir):
        if filename.endswith('.sql'):
            query_name = os.path.splitext(filename)[0]
            with open(os.path.join(queries_dir, filename), 'r', encoding='utf-8') as f:
                query_content = f.read()
                predefined_queries[query_name] = query_content
                print(f"Loaded query: {query_name}")

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('index.html')

@app.route('/monitoring')
def monitoring():
    """Monitoring dashboard page"""
    return render_template('monitoring_simple.html')

@app.route('/api/monitoring/run', methods=['POST'])
def api_run_monitoring():
    """API endpoint to run a monitoring query"""
    query_name = request.form.get('query_name', '')
    
    print(f"Running monitoring query: {query_name}")
    
    if query_name not in predefined_queries:
        print(f"Query '{query_name}' not found in predefined queries")
        print(f"Available queries: {list(predefined_queries.keys())}")
        return jsonify({
            'success': False,
            'message': f"Query '{query_name}' not found"
        })
    
    # Return dummy results
    return jsonify({
        'success': True,
        'message': "Dummy results generated for monitoring query",
        'query_name': query_name
    })

@app.route('/api/query/results', methods=['GET'])
def api_query_results():
    """API endpoint to get query results"""
    client_id = request.args.get('client_id')
    
    print(f"Getting query results for client_id: {client_id or 'all'}")
    
    # Create dummy client result
    dummy_result = {
        'client_name': "Dummy Client",
        'client_id': "dummy_client",
        'headers': dummy_results['monitor_diff_job_field']['headers'],
        'rows': dummy_results['monitor_diff_job_field']['rows']
    }
    
    return jsonify({
        'success': True,
        'results': [dummy_result]
    })

@app.route('/api/clients', methods=['GET'])
def api_clients():
    """API endpoint to get client list"""
    client_list = [
        {
            'id': "dummy_client",
            'name': "Dummy Client",
            'address': "127.0.0.1:0",
            'connected_since': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'db_info': {
                'path': r"C:\Gawean Rebinmas\Monitoring Database\Ifess Monitoring\PTRJ_P1A_08042025\PTRJ_P1A.FDB",
                'tables': 50
            }
        }
    ]
    
    return jsonify({
        'success': True,
        'clients': client_list
    })

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    templates_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'templates')
    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)
    
    # Run the Flask app
    app.run(host='0.0.0.0', port=5000, debug=True)
