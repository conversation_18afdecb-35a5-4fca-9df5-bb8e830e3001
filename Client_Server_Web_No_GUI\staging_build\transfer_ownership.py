#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Transfer Ownership Script
-------------------------
This script transfers ownership of all files owned by the service account
to a specified user account.
"""

import os
import sys
import time
import logging
import argparse
import traceback
from google.oauth2 import service_account
from googleapiclient.discovery import build

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('transfer_ownership')

def find_service_account_files(service, target_folder_id=None):
    """Find all files owned by the service account
    
    Args:
        service: Google Drive API service
        target_folder_id: Optional folder ID to limit the search
        
    Returns:
        list: List of file IDs owned by the service account
    """
    try:
        # Build the query to find files owned by the service account
        query = "trashed=false"
        
        # If target_folder_id is provided, limit the search to that folder
        if target_folder_id:
            query += f" and '{target_folder_id}' in parents"
            
        # Execute the query
        results = service.files().list(
            q=query,
            spaces='drive',
            fields='files(id, name, mimeType, owners, parents)',
            pageSize=1000
        ).execute()
        
        items = results.get('files', [])
        logger.info(f"Found {len(items)} files/folders")
        
        # Filter to only include files owned by the service account
        service_account_files = []
        for item in items:
            owners = item.get('owners', [])
            if owners and 'gserviceaccount.com' in owners[0].get('emailAddress', ''):
                service_account_files.append(item)
                
        logger.info(f"Found {len(service_account_files)} files/folders owned by the service account")
        return service_account_files
        
    except Exception as e:
        logger.error(f"Error finding service account files: {e}")
        logger.error(traceback.format_exc())
        return []

def transfer_ownership(service, file_id, new_owner):
    """Transfer ownership of a file to a new owner
    
    Args:
        service: Google Drive API service
        file_id: ID of the file to transfer
        new_owner: Email of the new owner
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # First, add the user as an editor (this doesn't require consent)
        logger.info(f"Adding {new_owner} as an editor...")
        service.permissions().create(
            fileId=file_id,
            body={
                'type': 'user',
                'role': 'writer',
                'emailAddress': new_owner
            },
            sendNotificationEmail=False
        ).execute()
        
        # Then try to transfer ownership
        logger.info(f"Attempting to transfer ownership to {new_owner}...")
        service.permissions().create(
            fileId=file_id,
            body={
                'type': 'user',
                'role': 'owner',
                'emailAddress': new_owner
            },
            transferOwnership=True,
            sendNotificationEmail=True,
            emailMessage="IFESS Backup System is transferring ownership of this file to you to save space in the service account."
        ).execute()
        
        logger.info(f"Ownership transferred successfully")
        return True
        
    except Exception as e:
        if "consentRequiredForOwnershipTransfer" in str(e):
            logger.warning(f"Ownership transfer requires consent. A notification has been sent to {new_owner}.")
            logger.warning("Please check your email and accept the ownership transfer request.")
        else:
            logger.error(f"Error transferring ownership: {e}")
            logger.error(traceback.format_exc())
        return False

def main():
    parser = argparse.ArgumentParser(description="Transfer ownership of files from service account to a user")
    parser.add_argument("--credentials", default="ptrj-backup-services-account.json", help="Path to service account credentials file")
    parser.add_argument("--new-owner", default="<EMAIL>", help="Email of the new owner")
    parser.add_argument("--folder-id", default="1HuSxKhmXRLuZxiYpgcSfVIb2i4YTUhpA", help="ID of the folder to process (optional)")
    parser.add_argument("--dry-run", action="store_true", help="Don't actually transfer ownership, just list files")
    
    args = parser.parse_args()
    
    # Find credentials file if not provided
    credentials_file = args.credentials
    if not os.path.exists(credentials_file):
        # Try to find the credentials file in the current directory
        possible_files = [
            "ptrj-backup-services-account.json",
            "staging_build/ptrj-backup-services-account.json",
            "../staging_build/ptrj-backup-services-account.json"
        ]
        
        for file in possible_files:
            if os.path.exists(file):
                credentials_file = file
                logger.info(f"Found credentials file: {credentials_file}")
                break
    
    if not os.path.exists(credentials_file):
        logger.error(f"Credentials file not found: {credentials_file}")
        return 1
        
    try:
        # Initialize the Google Drive API
        logger.info(f"Loading service account credentials from: {credentials_file}")
        credentials = service_account.Credentials.from_service_account_file(
            credentials_file,
            scopes=['https://www.googleapis.com/auth/drive']
        )
        
        service = build('drive', 'v3', credentials=credentials)
        logger.info("Google Drive API initialized successfully")
        
        # Find files owned by the service account
        logger.info(f"Finding files owned by the service account in folder: {args.folder_id}")
        files = find_service_account_files(service, args.folder_id)
        
        if not files:
            logger.info("No files found owned by the service account")
            return 0
            
        # Print file information
        logger.info("Files owned by the service account:")
        for i, file in enumerate(files):
            logger.info(f"{i+1}. {file.get('name')} (ID: {file.get('id')}, Type: {file.get('mimeType')})")
            
        if args.dry_run:
            logger.info("Dry run mode - not transferring ownership")
            return 0
            
        # Transfer ownership
        logger.info(f"Transferring ownership to {args.new_owner}...")
        success_count = 0
        
        for i, file in enumerate(files):
            file_id = file.get('id')
            file_name = file.get('name')
            
            logger.info(f"Processing {i+1}/{len(files)}: {file_name}")
            
            if transfer_ownership(service, file_id, args.new_owner):
                success_count += 1
                
            # Sleep to avoid rate limiting
            if i < len(files) - 1:
                time.sleep(1)
                
        logger.info(f"Ownership transfer completed: {success_count}/{len(files)} successful")
        
        if success_count < len(files):
            logger.info("Some transfers required consent. Please check your email and accept the ownership transfer requests.")
            logger.info("After accepting, run this script again to transfer ownership of the remaining files.")
            
        return 0
        
    except Exception as e:
        logger.error(f"Error: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main())
