<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Firebird Monitoring{% endblock %}</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <style>
        .navbar {
            margin-bottom: 20px;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .sticky-top {
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .card {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
        }
        .card-header {
            font-weight: 500;
        }
        .sort-icon {
            cursor: pointer;
        }
        .sort-icon:hover {
            color: #0d6efd !important;
        }
    </style>
    {% block styles %}{% endblock %}
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-database me-2"></i>Firebird Monitoring
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" 
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/' %}active{% endif %}" href="/">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/clients' %}active{% endif %}" href="/clients">
                            <i class="fas fa-laptop me-1"></i>Clients
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/monitoring' %}active{% endif %}" href="/monitoring">
                            <i class="fas fa-chart-line me-1"></i>Monitoring
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/query' %}active{% endif %}" href="/query">
                            <i class="fas fa-terminal me-1"></i>Query
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.path == '/backup' %}active{% endif %}" href="/backup">
                            <i class="fas fa-save me-1"></i>Backup
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container">
        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p class="mb-0">Firebird Monitoring System &copy; 2025</p>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Common JavaScript Functions -->
    <script>
        // Table sorting function
        function sortTable(element) {
            const table = $(element).closest('table');
            const headerIndex = $(element).closest('th').index();
            const rows = table.find('tbody tr').toArray();
            const isAscending = $(element).find('i').hasClass('fa-sort') || $(element).find('i').hasClass('fa-sort-down');
            
            // Update sort icon
            table.find('th .sort-icon i').removeClass('fa-sort-up fa-sort-down').addClass('fa-sort');
            $(element).find('i').removeClass('fa-sort').addClass(isAscending ? 'fa-sort-up' : 'fa-sort-down');
            
            // Sort rows
            rows.sort((a, b) => {
                const aValue = $(a).find('td').eq(headerIndex).text().trim();
                const bValue = $(b).find('td').eq(headerIndex).text().trim();
                
                // Check if values are numbers
                const aNum = parseFloat(aValue.replace(/,/g, ''));
                const bNum = parseFloat(bValue.replace(/,/g, ''));
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return isAscending ? aNum - bNum : bNum - aNum;
                }
                
                // Check if values are dates
                const aDate = new Date(aValue);
                const bDate = new Date(bValue);
                
                if (!isNaN(aDate) && !isNaN(bDate)) {
                    return isAscending ? aDate - bDate : bDate - aDate;
                }
                
                // Default to string comparison
                return isAscending ? 
                    aValue.localeCompare(bValue) : 
                    bValue.localeCompare(aValue);
            });
            
            // Reattach sorted rows
            table.find('tbody').empty().append(rows);
        }
        
        // Table filtering function
        function filterTable(input) {
            const filterValue = $(input).val().toLowerCase();
            const table = $(input).closest('.card').find('table');
            
            table.find('tbody tr').each(function() {
                const rowText = $(this).text().toLowerCase();
                $(this).toggle(rowText.indexOf(filterValue) > -1);
            });
            
            // Update row count
            const visibleRows = table.find('tbody tr:visible').length;
            const totalRows = table.find('tbody tr').length;
            
            $(input).closest('.card-footer').find('.text-muted').html(
                `<i class="fas fa-info-circle me-1"></i>Showing ${visibleRows} of ${totalRows} rows.`
            );
        }
        
        // Export table to CSV
        function exportTableToCSV(button, filename) {
            const table = $(button).closest('.card').find('table');
            let csv = [];
            
            // Get headers
            const headers = [];
            table.find('thead th').each(function() {
                headers.push($(this).text().trim());
            });
            csv.push(headers.join(','));
            
            // Get rows
            table.find('tbody tr:visible').each(function() {
                const row = [];
                $(this).find('td').each(function() {
                    // Escape quotes and wrap in quotes if contains comma
                    let cell = $(this).text().trim();
                    if (cell.includes(',') || cell.includes('"') || cell.includes("'")) {
                        cell = '"' + cell.replace(/"/g, '""') + '"';
                    }
                    row.push(cell);
                });
                csv.push(row.join(','));
            });
            
            // Download CSV
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.setAttribute('href', url);
            link.setAttribute('download', filename);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        // Print table
        function printTable(button) {
            const table = $(button).closest('.card').find('table');
            const tableHtml = table.prop('outerHTML');
            
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                    <head>
                        <title>Query Results</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
                        <style>
                            body { padding: 20px; }
                            table { width: 100%; border-collapse: collapse; }
                            th, td { padding: 8px; border: 1px solid #ddd; }
                            th { background-color: #f8f9fa; }
                            .table-striped tbody tr:nth-of-type(odd) { background-color: rgba(0,0,0,.05); }
                        </style>
                    </head>
                    <body>
                        <h3 class="mb-3">Query Results</h3>
                        ${tableHtml}
                    </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.focus();
            printWindow.print();
        }
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
