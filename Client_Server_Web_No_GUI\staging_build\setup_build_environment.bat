@echo off
REM ===============================================
REM IFESS Build Environment Setup Script
REM Installs and configures all build dependencies
REM ===============================================

title IFESS Build Environment Setup
color 0E

echo.
echo ===============================================
echo    IFESS Build Environment Setup
echo ===============================================
echo This script will install and configure all
echo dependencies required for building IFESS clients.
echo.

REM Change to script directory
cd /d "%~dp0"

REM Check Python installation
echo [1/5] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH!
    echo.
    echo Please install Python 3.8 or later from:
    echo https://www.python.org/downloads/
    echo.
    echo Make sure to check "Add Python to PATH" during installation.
    pause
    exit /b 1
) else (
    python --version
    echo Python installation verified ✓
)

echo.

REM Update pip
echo [2/5] Updating pip...
python -m pip install --upgrade pip
echo Pip updated ✓

echo.

REM Install PyInstaller
echo [3/5] Installing PyInstaller...

REM Remove conflicting pathlib package (Python 3.13+ issue)
echo Removing obsolete pathlib package...
pip uninstall pathlib -y >nul 2>&1

pip install --upgrade pyinstaller
if errorlevel 1 (
    echo ERROR: Failed to install PyInstaller!
    pause
    exit /b 1
) else (
    echo PyInstaller installed ✓
    REM Test PyInstaller access
    python -m PyInstaller --version >nul 2>&1
    if errorlevel 1 (
        echo WARNING: PyInstaller installed but not accessible via python -m
    ) else (
        python -m PyInstaller --version
        echo PyInstaller command line access verified ✓
    )
)

echo.

REM Install core dependencies
echo [4/5] Installing core dependencies...
echo Installing Google API client libraries...
pip install --upgrade google-api-python-client google-auth google-auth-oauthlib google-auth-httplib2

echo Installing database libraries...
pip install --upgrade fdb firebirdsql

echo Installing utility libraries...
pip install --upgrade requests schedule

echo Installing GUI libraries...
pip install --upgrade tkinter

echo Core dependencies installed ✓

echo.

REM Optional: Install UPX for compression
echo [5/5] Checking UPX compression tool...
upx --version >nul 2>&1
if errorlevel 1 (
    echo UPX not found - executable compression will be disabled
    echo.
    echo Optional: To enable compression, download UPX from:
    echo https://upx.github.io/
    echo And add it to your PATH
    echo.
) else (
    upx --version
    echo UPX compression available ✓
)

echo.

REM Create build directories
echo Creating build directories...
if not exist "dist" mkdir "dist"
if not exist "build" mkdir "build"
echo Build directories ready ✓

echo.
echo ===============================================
echo    SETUP COMPLETED SUCCESSFULLY!
echo ===============================================
echo.
echo Your build environment is now ready!
echo.
echo Available build scripts:
echo - build_all_clients_comprehensive.bat (Full build with logging)
echo - quick_build.bat (Fast build for development)
echo.
echo You can now run these scripts to build the IFESS clients.
echo ===============================================

pause 