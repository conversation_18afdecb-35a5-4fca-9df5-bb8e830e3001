# MEGA Cloud Storage Upload Feature

This feature allows uploading database backups to MEGA cloud storage directly from the server_web interface.

## Setup Instructions

### Option 1: Using a Virtual Environment (Recommended)

This option works with any Python version and is the easiest way to get started:

1. Run the application using the virtual environment batch file:

```
run_with_venv.bat
```

This will:
- Create a virtual environment if it doesn't exist
- Install the required dependencies
- Start both the server and client

### Option 2: Using Python 3.10 Directly

If you prefer to use Python 3.10 directly:

1. Make sure you have Python 3.10 installed. If you're not sure where Python 3.10 is installed on your system, run the provided helper script:

```
simple_python_finder.bat
```

This will search for Python installations on your system and show you the paths.

2. Edit the `run_with_python310.bat` file and update the `PYTHON_PATH` variable with the correct path to your Python 3.10 installation. For example:

```batch
set PYTHON_PATH=C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe
```

3. Run the application using the batch file:

```
run_with_python310.bat
```

This will automatically install the required dependencies and start both the server and client.

4. Make sure the client application is running and connected to the server.

5. Access the server_web interface at http://localhost:5000 (or your configured address).

6. Navigate to the "Backups" page.

7. For any backup file, click the "Upload to MEGA" button to upload it to MEGA cloud storage.

## How It Works

1. When you click the "Upload to MEGA" button on the backups page, the server sends a request to the client.

2. The client logs in to MEGA using the configured credentials and uploads the file.

3. The upload progress and result are sent back to the server.

4. The file is stored in the MEGA account specified in the client configuration.

## Troubleshooting

If you encounter issues with the MEGA upload feature:

1. Check that the client is connected to the server (visible on the Clients page).

2. Verify that the MEGA credentials in `Client/mega_client.py` are correct.

3. Check the client logs for any error messages related to MEGA uploads.

4. Ensure the client has the `mega.py` package installed.

## MEGA Account Information

The MEGA uploads use the following account:

- Email: <EMAIL>
- Password: ptrj@123

You can access the uploaded files by logging into this account at https://mega.nz/
