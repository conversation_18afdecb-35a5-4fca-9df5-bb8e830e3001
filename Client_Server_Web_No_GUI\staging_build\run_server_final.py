import os, sys 
import webbrowser 
import flask 
import flask_cors 
import fdb 
 
# Add the server_web directory to the path 
server_web_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'server_web') 
if server_web_dir not in sys.path: 
    sys.path.insert(0, server_web_dir) 
 
# Import the Flask app from server_web.py 
try: 
    from server_web import app 
    print("Successfully imported server_web module") 
except ImportError as e: 
    print(f"Error importing server_web: {e}") 
    print(f"Current path: {os.getcwd()}") 
    print(f"Python path: {sys.path}") 
    print(f"Files in current directory: {os.listdir('.')}") 
    if os.path.exists('server_web'): 
        print(f"Files in server_web directory: {os.listdir('server_web')}") 
    sys.exit(1) 
 
if __name__ == '__main__': 
    print("Starting IFESS Server Web...") 
    print("Server will be accessible at http://localhost:5000") 
    # Open the browser 
    webbrowser.open('http://localhost:5000') 
    # Run the Flask app 
    app.run(host='0.0.0.0', port=5000, debug=False, threaded=True) 
