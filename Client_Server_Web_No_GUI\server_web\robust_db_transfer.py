#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Robust Firebird Database Transfer Tool

This tool provides a more reliable way to transfer Firebird databases from clients to the server.
It ensures proper handling of database files, with verification at each step.

Usage:
    python robust_db_transfer.py <client_id>

Example:
    python robust_db_transfer.py client1
"""

import os
import sys
import time
import json
import uuid
import socket
import struct
import base64
import zlib
import shutil
import logging
import datetime
import threading
import traceback
import hashlib
import subprocess
from pathlib import Path

# Add parent directory to path to import common modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Import common modules
try:
    from common.network import NetworkMessage, send_message, receive_message, DEFAULT_PORT
except ImportError:
    print("Error importing common modules. Make sure the common directory exists and contains the required modules.")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('robust_db_transfer.log')
    ]
)
logger = logging.getLogger('robust_db_transfer')

# Constants
DEFAULT_CHUNK_SIZE = 256 * 1024  # 256KB chunks
MAX_RETRIES = 5
SOCKET_TIMEOUT = 120  # 2 minutes
BACKUP_DIR = os.path.join(current_dir, 'static', 'backups')

# Ensure backup directory exists
os.makedirs(BACKUP_DIR, exist_ok=True)

class DatabaseTransfer:
    def __init__(self, client_id, host='localhost', port=DEFAULT_PORT):
        self.client_id = client_id
        self.host = host
        self.port = port
        self.socket = None
        self.transfer_id = str(uuid.uuid4())[:8]
        self.db_path = None
        self.chunks_received = 0
        self.bytes_received = 0
        self.total_bytes = 0
        self.start_time = None
        self.end_time = None
        self.status = 'initializing'
        self.error = None
        self.is_firebird_db = False

        # Create a unique directory for this transfer
        self.transfer_dir = os.path.join(BACKUP_DIR, f"import_{self.transfer_id}")
        os.makedirs(self.transfer_dir, exist_ok=True)
        logger.info(f"Created transfer directory: {self.transfer_dir}")

        # Initialize chunk tracking
        self.chunks = {}  # offset -> data
        self.missing_chunks = set()
        self.verified_chunks = set()

    def connect(self):
        """Connect to the client"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(SOCKET_TIMEOUT)
            self.socket.connect((self.host, self.port))
            logger.info(f"Connected to {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"Error connecting to {self.host}:{self.port}: {e}")
            self.error = f"Connection error: {str(e)}"
            self.status = 'failed'
            return False

    def register(self):
        """Register with the client"""
        try:
            # Create registration message
            register_data = {
                'client_id': f"server_{self.transfer_id}",
                'display_name': f"Server Transfer {self.transfer_id}",
                'timestamp': time.time()
            }

            register_message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_REGISTER,
                data=register_data,
                client_id=f"server_{self.transfer_id}"
            )

            # Send registration message
            send_message(self.socket, register_message)
            logger.info(f"Sent registration message")

            # Wait for response
            time.sleep(1)

            return True
        except Exception as e:
            logger.error(f"Error registering with client: {e}")
            self.error = f"Registration error: {str(e)}"
            self.status = 'failed'
            return False

    def request_database(self):
        """Request database from client"""
        try:
            # Create database request message
            request_data = {
                'request_id': self.transfer_id,
                'timestamp': time.time()
            }

            request_message = NetworkMessage(
                msg_type=NetworkMessage.TYPE_DB_REQUEST,
                data=request_data,
                client_id=f"server_{self.transfer_id}"
            )

            # Send request message
            send_message(self.socket, request_message)
            logger.info(f"Sent database request message")

            # Start receiving database
            self.start_time = time.time()
            self.status = 'requesting'

            # Process responses
            return self.process_responses()
        except Exception as e:
            logger.error(f"Error requesting database: {e}")
            self.error = f"Request error: {str(e)}"
            self.status = 'failed'
            return False

    def process_responses(self):
        """Process responses from client"""
        try:
            # Initialize variables
            db_info_received = False
            all_chunks_received = False
            completion_received = False
            consecutive_errors = 0

            # Process messages until completion or error
            while not completion_received:
                try:
                    # Receive message
                    message = receive_message(self.socket)

                    if not message:
                        logger.warning("Received empty message")
                        consecutive_errors += 1
                        if consecutive_errors >= MAX_RETRIES:
                            logger.error(f"Too many consecutive errors ({consecutive_errors}), aborting")
                            self.error = "Too many consecutive errors"
                            self.status = 'failed'
                            return False
                        continue

                    # Reset error counter on successful receive
                    consecutive_errors = 0

                    # Process message based on type
                    msg_type = getattr(message, 'msg_type', None)
                    logger.debug(f"Received message: {msg_type}")

                    if msg_type == NetworkMessage.TYPE_DB_INFO:
                        # Process database info
                        self.process_db_info(message.data)
                        db_info_received = True
                        self.status = 'receiving'

                    elif msg_type == NetworkMessage.TYPE_DB_CHUNK:
                        # Process database chunk
                        self.process_db_chunk(message.data)

                    elif msg_type == NetworkMessage.TYPE_DB_COMPLETE:
                        # Process database completion
                        self.process_db_complete(message.data)
                        completion_received = True

                    elif msg_type == NetworkMessage.TYPE_ERROR:
                        # Process error message
                        logger.error(f"Received error from client: {message.data.get('error', 'Unknown error')}")
                        self.error = f"Client error: {message.data.get('error', 'Unknown error')}"
                        self.status = 'failed'
                        return False

                    else:
                        logger.warning(f"Received unknown message type: {msg_type}")

                except socket.timeout:
                    logger.warning("Socket timeout while waiting for message")
                    consecutive_errors += 1
                    if consecutive_errors >= MAX_RETRIES:
                        logger.error(f"Too many consecutive errors ({consecutive_errors}), aborting")
                        self.error = "Socket timeout"
                        self.status = 'failed'
                        return False
                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    consecutive_errors += 1
                    if consecutive_errors >= MAX_RETRIES:
                        logger.error(f"Too many consecutive errors ({consecutive_errors}), aborting")
                        self.error = f"Processing error: {str(e)}"
                        self.status = 'failed'
                        return False

            # Verify all chunks were received
            self.verify_chunks()

            # Finalize database
            return self.finalize_database()
        except Exception as e:
            logger.error(f"Error processing responses: {e}")
            self.error = f"Response processing error: {str(e)}"
            self.status = 'failed'
            return False

    def process_db_info(self, data):
        """Process database info message"""
        try:
            logger.info(f"Received database info: {data}")

            # Extract database info
            filename = data.get('filename', f"database_{int(time.time())}.fdb")
            self.total_bytes = data.get('size', 0)

            # Check if this is a Firebird database
            self.is_firebird_db = filename.lower().endswith('.fdb')

            # Set database path
            self.db_path = os.path.join(self.transfer_dir, filename)
            logger.info(f"Database will be saved to: {self.db_path}")

            # Create empty file to pre-allocate space
            with open(self.db_path, 'wb') as f:
                # For Firebird databases, don't pre-allocate
                if not self.is_firebird_db:
                    # Pre-allocate file space
                    try:
                        f.seek(self.total_bytes - 1)
                        f.write(b'\0')
                        logger.info(f"Pre-allocated {self.total_bytes} bytes for database file")
                    except Exception as e:
                        logger.warning(f"Could not pre-allocate file: {e}")

            # Update status
            self.status = 'receiving'
        except Exception as e:
            logger.error(f"Error processing database info: {e}")
            raise

    def process_db_chunk(self, data):
        """Process database chunk message"""
        try:
            # Extract chunk info
            offset = data.get('offset', 0)
            chunk_size = data.get('chunk_size', 0)
            chunk_data_encoded = data.get('data', '')
            is_last = data.get('is_last', False)

            # Decode chunk data
            try:
                chunk_data = base64.b64decode(chunk_data_encoded)

                # Check if data is compressed
                try:
                    # Try to decompress (assuming it might be compressed)
                    decompressed_data = zlib.decompress(chunk_data)
                    chunk_data = decompressed_data
                    logger.debug(f"Decompressed chunk data from {len(chunk_data_encoded)} to {len(chunk_data)} bytes")
                except:
                    # Not compressed, use as is
                    pass
            except Exception as e:
                logger.error(f"Error decoding chunk data: {e}")
                raise

            # Verify chunk size
            if len(chunk_data) != chunk_size:
                logger.warning(f"Chunk size mismatch: expected {chunk_size}, got {len(chunk_data)}")

            # Store chunk in memory for verification
            self.chunks[offset] = chunk_data

            # Write chunk to file
            try:
                with open(self.db_path, 'r+b') as f:
                    f.seek(offset)
                    f.write(chunk_data)
                    f.flush()

                    # For Firebird databases, ensure proper flushing
                    if self.is_firebird_db:
                        os.fsync(f.fileno())

                        # For the first chunk, save a copy for potential repair
                        if offset == 0:
                            with open(f"{self.db_path}.first_chunk", 'wb') as header_file:
                                header_file.write(chunk_data)
                            logger.info(f"Saved first chunk as header backup")
            except Exception as e:
                logger.error(f"Error writing chunk to file: {e}")
                raise

            # Update progress
            self.chunks_received += 1
            self.bytes_received += len(chunk_data)

            # Log progress periodically
            if self.chunks_received % 10 == 0 or is_last:
                progress = (self.bytes_received / self.total_bytes) * 100 if self.total_bytes > 0 else 0
                logger.info(f"Progress: {progress:.1f}% ({self.bytes_received}/{self.total_bytes} bytes, {self.chunks_received} chunks)")

            # Mark chunk as verified
            self.verified_chunks.add(offset)
        except Exception as e:
            logger.error(f"Error processing database chunk: {e}")
            raise

    def process_db_complete(self, data):
        """Process database completion message"""
        try:
            logger.info(f"Received database completion: {data}")

            # Extract completion info
            self.end_time = time.time()

            # Update status
            self.status = 'verifying'
        except Exception as e:
            logger.error(f"Error processing database completion: {e}")
            raise

    def verify_chunks(self):
        """Verify that all chunks were received"""
        try:
            logger.info(f"Verifying chunks...")

            # Check if file size matches expected size
            file_size = os.path.getsize(self.db_path)
            if file_size != self.total_bytes:
                logger.warning(f"File size mismatch: expected {self.total_bytes}, got {file_size}")

                # Try to fix file size
                if file_size < self.total_bytes:
                    logger.warning(f"File is smaller than expected, may be missing chunks")

                    # Check for missing chunks
                    expected_offsets = set(range(0, self.total_bytes, DEFAULT_CHUNK_SIZE))
                    received_offsets = set(self.chunks.keys())
                    self.missing_chunks = expected_offsets - received_offsets

                    if self.missing_chunks:
                        logger.warning(f"Missing {len(self.missing_chunks)} chunks")

                        # Request missing chunks (not implemented in this version)
                        logger.warning(f"Requesting missing chunks is not implemented in this version")

            # Update status
            self.status = 'verified'
            logger.info(f"Chunks verified: {self.chunks_received} chunks, {self.bytes_received} bytes")
        except Exception as e:
            logger.error(f"Error verifying chunks: {e}")
            raise

    def finalize_database(self):
        """Finalize the database file"""
        try:
            logger.info(f"Finalizing database...")

            # For Firebird databases, perform additional validation
            if self.is_firebird_db:
                # Check if the file has a valid Firebird header
                with open(self.db_path, 'rb') as f:
                    header = f.read(4096)  # Read first 4KB

                    # Check for common Firebird signatures
                    firebird_signatures = [
                        b'Firebird', b'INTERBASE', b'GENERAL', b'ODS',
                        b'FILE FORMAT', b'Database', b'\x01\x00\x09\x00',  # ODS signature
                        b'\x16\x00',  # Firebird/InterBase ODS marker
                    ]

                    header_valid = False
                    for signature in firebird_signatures:
                        if signature in header:
                            logger.info(f"Found valid signature in header: {signature}")
                            header_valid = True
                            break

                    if not header_valid:
                        logger.warning(f"No valid Firebird signatures found in header!")

                        # Try to repair the header
                        if os.path.exists(f"{self.db_path}.first_chunk"):
                            logger.info(f"Attempting to restore header from backup")

                            with open(f"{self.db_path}.first_chunk", 'rb') as src, open(self.db_path, 'r+b') as dest:
                                header_data = src.read()
                                dest.seek(0)
                                dest.write(header_data)
                                dest.flush()
                                os.fsync(dest.fileno())

                            logger.info(f"Restored header from backup")
                        else:
                            logger.warning(f"No header backup found, cannot repair")

                # Validate with isql if available
                isql_path = r"C:\Program Files (x86)\Firebird\Firebird_1_5\bin\isql.exe"
                if os.path.exists(isql_path):
                    logger.info(f"Validating database with isql")

                    try:
                        # Create connection string
                        connection_string = f"{self.db_path}"

                        # Run isql for database validation
                        cmd = [isql_path, "-user", "sysdba", "-password", "masterkey", connection_string, "-x"]

                        # Set timeout to avoid hanging
                        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                        stdout, stderr = process.communicate(timeout=30)

                        # Check for errors
                        if process.returncode != 0:
                            logger.warning(f"isql validation failed: {stderr}")
                        else:
                            logger.info(f"Database validated successfully with isql")
                    except Exception as e:
                        logger.warning(f"Error validating with isql: {e}")

            # Set file permissions
            try:
                os.chmod(self.db_path, 0o644)  # rw-r--r--
                logger.info(f"Set file permissions to 644")
            except Exception as e:
                logger.warning(f"Error setting file permissions: {e}")

            # Update status
            self.status = 'completed'
            logger.info(f"Database transfer completed: {self.db_path}")

            # Calculate statistics
            duration = self.end_time - self.start_time if self.end_time and self.start_time else 0
            speed = self.bytes_received / duration if duration > 0 else 0

            logger.info(f"Transfer statistics:")
            logger.info(f"  Duration: {duration:.1f} seconds")
            logger.info(f"  Speed: {speed/1024/1024:.1f} MB/s")
            logger.info(f"  Chunks: {self.chunks_received}")
            logger.info(f"  Bytes: {self.bytes_received}")

            return True
        except Exception as e:
            logger.error(f"Error finalizing database: {e}")
            self.error = f"Finalization error: {str(e)}"
            self.status = 'failed'
            return False

    def close(self):
        """Close the connection"""
        try:
            if self.socket:
                self.socket.close()
                logger.info(f"Closed connection")
        except Exception as e:
            logger.error(f"Error closing connection: {e}")

    def get_status(self):
        """Get transfer status"""
        return {
            'transfer_id': self.transfer_id,
            'client_id': self.client_id,
            'status': self.status,
            'error': self.error,
            'db_path': self.db_path,
            'chunks_received': self.chunks_received,
            'bytes_received': self.bytes_received,
            'total_bytes': self.total_bytes,
            'progress': (self.bytes_received / self.total_bytes) * 100 if self.total_bytes > 0 else 0,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.end_time - self.start_time if self.end_time and self.start_time else 0
        }

def main():
    """Main function"""
    # Parse command line arguments
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <client_id> [host] [port]")
        return 1

    client_id = sys.argv[1]
    host = sys.argv[2] if len(sys.argv) > 2 else 'localhost'
    port = int(sys.argv[3]) if len(sys.argv) > 3 else DEFAULT_PORT

    print(f"Starting database transfer from {client_id} at {host}:{port}")

    # Create transfer object
    transfer = DatabaseTransfer(client_id, host, port)

    try:
        # Connect to client
        if not transfer.connect():
            print(f"Error connecting to client: {transfer.error}")
            return 1

        # Register with client
        if not transfer.register():
            print(f"Error registering with client: {transfer.error}")
            return 1

        # Request database
        if not transfer.request_database():
            print(f"Error requesting database: {transfer.error}")
            return 1

        # Print final status
        status = transfer.get_status()
        print(f"\nDatabase transfer completed:")
        print(f"  Status: {status['status']}")
        print(f"  Database: {status['db_path']}")
        print(f"  Size: {status['bytes_received']} bytes")
        print(f"  Chunks: {status['chunks_received']}")
        print(f"  Duration: {status['duration']:.1f} seconds")
        print(f"  Speed: {status['bytes_received']/status['duration']/1024/1024:.1f} MB/s" if status['duration'] > 0 else "  Speed: N/A")

        return 0
    except Exception as e:
        print(f"Error during database transfer: {e}")
        logger.error(f"Error during database transfer: {e}")
        logger.error(traceback.format_exc())
        return 1
    finally:
        # Close connection
        transfer.close()

if __name__ == '__main__':
    sys.exit(main())
