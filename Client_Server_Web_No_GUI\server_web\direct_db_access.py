"""
Direct Database Access Module

This module provides API endpoints and functions for direct database access.
It allows the server to directly access client databases without requiring a full backup process.
"""

import os
import time
import datetime
import math
from flask import jsonify

# Import from server_web.py
from server_web import app, clients, clients_lock, logger, format_file_size
from server_web import request_direct_db_access

# Define backup directory - CHANGED to user-specified location
# Lokasi penyimpanan sesuai dengan permintaan pengguna
BACKUP_DIR = r"D:\Gawean Rebinmas\Monitoring Database\ifess\Client_Server_Web_No_GUI\server_web\static\backups"

# Buat direktori backup jika belum ada
if not os.path.exists(BACKUP_DIR):
    try:
        os.makedirs(BACKUP_DIR, exist_ok=True)
        logger.info(f"[DIRECT-DB] Created backup directory at {BACKUP_DIR}")
    except Exception as e:
        logger.error(f"[DIRECT-DB] Failed to create backup directory: {e}")
        # Gunakan direktori default jika gagal membuat direktori baru
        current_dir = os.path.dirname(os.path.abspath(__file__))
        BACKUP_DIR = os.path.join(current_dir, "static", "backups")
        logger.info(f"[DIRECT-DB] Falling back to default backup directory: {BACKUP_DIR}")

logger.info(f"[DIRECT-DB] Using backup directory: {BACKUP_DIR}")

@app.route('/api/direct-db/access/<client_id>', methods=['GET'])
def api_direct_db_access(client_id):
    """API endpoint to get direct database access from a client"""
    logger.info(f"[DIRECT-DB] Direct access request for client {client_id}")
    logger.info(f"[DIRECT-DB] Request time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Print debug info
    print(f"\n[DIRECT-DB] ===== DIRECT ACCESS REQUEST =====")
    print(f"[DIRECT-DB] Client ID: {client_id}")
    print(f"[DIRECT-DB] Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Validate client_id
    with clients_lock:
        if client_id not in clients:
            logger.error(f"[DIRECT-DB] Client {client_id} not found for direct access")
            return jsonify({
                'success': False,
                'message': "Client not found"
            })

        client = clients[client_id]
        logger.info(f"[DIRECT-DB] Found client: {client.display_name}, connected: {client.connected}")
        print(f"[DIRECT-DB] Found client: {client.display_name}, connected: {client.connected}")

    # Request direct database access
    db_info = request_direct_db_access(client_id)

    if not db_info:
        logger.error(f"[DIRECT-DB] Failed to get direct access to database from {client.display_name}")
        print(f"[DIRECT-DB] Failed to get direct access to database from {client.display_name}")
        # Return error but indicate that database transfer might still be happening
        return jsonify({
            'success': False,
            'message': "Failed to get direct access to database, but database transfer may still be in progress. Please check debug console for details."
        })

    # Return database info
    logger.info(f"[DIRECT-DB] Successfully got direct access to database from {client.display_name}")
    return jsonify({
        'success': True,
        'message': f"Successfully got direct access to database from {client.display_name}",
        'db_info': db_info
    })

@app.route('/api/direct-db/download/<client_id>', methods=['GET'])
def api_direct_db_download(client_id):
    """API endpoint to download a database file directly from a client"""
    logger.info(f"[DIRECT-DB] Direct download request for client {client_id}")
    start_time = time.time()

    print(f"\n[DIRECT-DB] ===== DIRECT DOWNLOAD REQUEST =====")
    print(f"[DIRECT-DB] Client ID: {client_id}")
    print(f"[DIRECT-DB] Time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Validate client_id
    with clients_lock:
        if client_id not in clients:
            logger.error(f"[DIRECT-DB] Client {client_id} not found for direct download")
            return jsonify({
                'success': False,
                'message': "Client not found"
            })

        client = clients[client_id]
        logger.info(f"[DIRECT-DB] Found client for download: {client.display_name}")
        print(f"[DIRECT-DB] Found client for download: {client.display_name}")

    # Check if client has direct access
    with clients_lock:
        if not hasattr(client, 'db_direct_access') or not client.db_direct_access:
            # Try to get direct access first
            logger.info(f"[DIRECT-DB] No direct access yet for {client.display_name}, requesting it now")
            print(f"[DIRECT-DB] No direct access yet for {client.display_name}, requesting it now")
            db_info = request_direct_db_access(client_id)

            if not db_info:
                logger.warning(f"[DIRECT-DB] Failed to get direct access to database from {client.display_name}, but trying to continue anyway")
                print(f"[DIRECT-DB] Failed to get direct access to database from {client.display_name}, but trying to continue anyway")

                # As a fallback, create some basic info structure to allow process to continue
                db_info = {
                    'file_name': f"database_{client.display_name}.fdb",
                    'file_size': 100000000,  # Assume 100MB as default
                    'access_token': 'fallback_token'
                }
                logger.info(f"[DIRECT-DB] Using fallback db_info: {db_info}")
                print(f"[DIRECT-DB] Using fallback db_info: {db_info}")
        else:
            logger.info(f"[DIRECT-DB] Using existing direct access for {client.display_name}")
            db_info = client.db_direct_access

    # Get file info from db_info
    file_name = db_info.get('file_name', '')
    file_size = db_info.get('file_size', 0)

    if not file_name:
        file_name = f"database_{client.display_name}.fdb"
        logger.warning(f"[DIRECT-DB] No filename provided, using default: {file_name}")

    if file_size <= 0:
        file_size = 100000000  # Assume 100MB as default
        logger.warning(f"[DIRECT-DB] Invalid file size, using default: {file_size}")

    # Generate timestamp for the file
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

    # Get base name and extension
    base_name, ext = os.path.splitext(file_name)
    timestamped_filename = f"{base_name}_{timestamp}{ext}"

    # Create directory for client if it doesn't exist
    client_dir = os.path.join(BACKUP_DIR, client_id)
    try:
        os.makedirs(client_dir, exist_ok=True)
        logger.info(f"[DIRECT-DB] Created/verified client directory: {client_dir}")
        print(f"[DIRECT-DB] Created/verified client directory: {client_dir}")
    except Exception as e:
        logger.error(f"[DIRECT-DB] Error creating client directory: {e}")
        # Use root backup directory as fallback
        client_dir = BACKUP_DIR
        logger.info(f"[DIRECT-DB] Using root backup directory as fallback: {client_dir}")
        print(f"[DIRECT-DB] Using root backup directory as fallback: {client_dir}")

    # Set file path
    file_path = os.path.join(client_dir, timestamped_filename)
    logger.info(f"[DIRECT-DB] Setting database file path to: {file_path}")
    print(f"[DIRECT-DB] Setting database file path to: {file_path}")

    # Store file info in client object - IMPORTANT for process_db_file_chunk to work
    with clients_lock:
        client.db_file_path = file_path
        client.db_file_size = file_size
        client.db_file_received = 0  # Penting: mulai dari 0 bytes diterima
        client.db_actual_received = 0  # Tracker baru untuk bytes aktual yang diterima
        client.db_backup_start_time = time.time()
        client.db_chunks_received = 0
        client.db_total_chunks = 0  # Will be updated when we receive chunks
        client.db_backup_status = 'in_progress'
        client.db_backup_timestamp = timestamp
        client.db_backup_filename = timestamped_filename
        client.last_logged_progress = 0  # Initialize progress tracking
        client.db_file_preallocated = False  # Track if file was preallocated

    logger.info(f"[DIRECT-DB] Initialized database file path: {file_path}")
    logger.info(f"[DIRECT-DB] File size: {file_size} bytes")

    try:
        # Create an empty file but don't pre-allocate full size to avoid reporting false progress
        with open(file_path, 'wb') as f:
            # Just create an empty file - don't pre-allocate
            f.write(b'')
            f.flush()
            logger.info(f"[DIRECT-DB] Created empty file, will populate as chunks arrive")
            print(f"[DIRECT-DB] Created empty file, will populate as chunks arrive")
    except Exception as file_error:
        logger.error(f"[DIRECT-DB] Error creating file: {file_error}")
        print(f"[DIRECT-DB] Error creating file: {file_error}")
        return jsonify({
            'success': False,
            'message': f"Error creating file: {str(file_error)}"
        })

    # Download the database file in chunks
    logger.info(f"[DIRECT-DB] Downloading database file from {client.display_name} to {file_path}")
    logger.info(f"[DIRECT-DB] File size: {file_size} bytes")
    print(f"[DIRECT-DB] Downloading database file from {client.display_name} to {file_path}")
    print(f"[DIRECT-DB] File size: {file_size} bytes ({format_file_size(file_size)})")

    # Return success immediately, the UI will poll for status
    # Gunakan relative path untuk download URL
    download_url = f"/static/backups/{client_id}/{timestamped_filename}"

    # Jika menggunakan path kustom, buat URL download alternatif
    download_path = file_path

    # Verify if client still has the db_file_path set
    with clients_lock:
        if not hasattr(client, 'db_file_path') or not client.db_file_path:
            logger.error(f"[DIRECT-DB] Database file path was cleared for {client.display_name}!")
            print(f"[DIRECT-DB] Database file path was cleared for {client.display_name}!")
            return jsonify({
                'success': False,
                'message': "Database file path was not properly set"
            })
        logger.info(f"[DIRECT-DB] Verified database file path is still set: {client.db_file_path}")
        print(f"[DIRECT-DB] Verified database file path is still set: {client.db_file_path}")

    # Return success with download URL and info
    return jsonify({
        'success': True,
        'message': f"Database download started for {client.display_name}",
        'status': 'in_progress',
        'progress': 0,
        'file_name': timestamped_filename,
        'file_size': file_size,
        'file_size_formatted': format_file_size(file_size),
        'download_url': download_url,
        'download_path': download_path,
        'elapsed_time': round(time.time() - start_time, 2)
    })

@app.route('/api/direct-db/status/<client_id>', methods=['GET'])
def api_direct_db_status(client_id):
    """API endpoint to get the status of a direct database download"""
    # Validate client_id
    with clients_lock:
        if client_id not in clients:
            logger.error(f"[DIRECT-DB] Client {client_id} not found for status check")
            return jsonify({
                'success': False,
                'message': "Client not found"
            })

        client = clients[client_id]

    # Check if client has backup attributes (check both direct and regular backup attributes)
    has_direct_attrs = hasattr(client, 'db_direct_progress')
    has_backup_attrs = hasattr(client, 'db_backup_status') and hasattr(client, 'db_file_received') and hasattr(client, 'db_file_size')

    # Cek apakah ada transfer aktif dengan melihat file path dan bytes received
    file_path = getattr(client, 'db_file_path', '')
    # Gunakan bytes aktual yang diterima, bukan ukuran file
    bytes_received = getattr(client, 'db_actual_received', 0)
    if bytes_received == 0:
        # Fallback ke metode lama jika db_actual_received belum tersedia
        bytes_received = getattr(client, 'db_file_received', 0)

    file_size = getattr(client, 'db_file_size', 0)
    has_active_transfer = file_path and bytes_received > 0

    # Cek apakah status sudah complete
    status = getattr(client, 'db_backup_status', '')
    is_completed = status in ['completed', 'complete', 'received_all_chunks']

    # Cek waktu terakhir update - untuk timeout polling
    last_update_time = getattr(client, 'last_chunk_time', 0)
    time_since_last_update = time.time() - last_update_time

    # Log status untuk debugging
    logger.debug(f"[DIRECT-DB] Status check for {client.display_name}: has_direct_attrs={has_direct_attrs}, has_backup_attrs={has_backup_attrs}, bytes_received={bytes_received}, status={status}")

    # Timeout untuk polling - jika tidak ada update lebih dari 20 detik dan bytes sudah lebih dari 50% file size
    timeout_threshold = 20  # detik
    has_stalled = time_since_last_update > timeout_threshold and bytes_received > 0

    # Jika sudah complete, kembalikan status complete dengan data lengkap
    if is_completed:
        # Cek keberadaan file
        if os.path.exists(file_path):
            actual_size = bytes_received
            # Hanya gunakan ukuran file disk jika benar-benar update terakhir
            if time_since_last_update > 5:
                # Verifikasi dengan ukuran file aktual
                actual_size = os.path.getsize(file_path)

            file_name = os.path.basename(file_path)

            return jsonify({
                'success': True,
                'status': 'complete',
                'message': "Database transfer completed successfully",
                'progress': 100,
                'bytes_received': actual_size,
                'file_name': file_name,
                'file_path': file_path,
                'file_size': actual_size,
                'download_url': f"/static/backups/{client_id}/{file_name}",
                'elapsed_time': round(time.time() - getattr(client, 'db_backup_start_time', time.time()), 2),
                'polling_complete': True  # Signal to UI to stop polling
            })

    # Jika timeout dan file size cukup besar, anggap selesai
    if has_stalled and bytes_received > file_size * 0.9:  # 90% file sudah terdownload
        logger.info(f"[DIRECT-DB] Transfer appears complete but not marked as such. Bytes: {bytes_received}, File size: {file_size}, Time since update: {time_since_last_update}s")

        if file_path and os.path.exists(file_path):
            # Update status
            with clients_lock:
                client.db_backup_status = 'completed'
                client.db_file_verified = True
                client.db_backup_complete_time = time.time()

            # Kembalikan status complete
            return jsonify({
                'success': True,
                'status': 'complete',
                'message': "Database transfer appears complete (timeout detected)",
                'progress': 100,
                'bytes_received': bytes_received,
                'file_name': os.path.basename(file_path),
                'file_path': file_path,
                'file_size': file_size,
                'download_url': f"/static/backups/{client_id}/{os.path.basename(file_path)}",
                'elapsed_time': round(time.time() - getattr(client, 'db_backup_start_time', time.time()), 2),
                'polling_complete': True  # Signal to UI to stop polling
            })

    # Jika tidak ada indikasi transfer, kembalikan status awal
    if not has_direct_attrs and not has_backup_attrs and not has_active_transfer:
        return jsonify({
            'success': True,
            'status': 'not_started',
            'message': "Database import not started"
        })

    # Get progress information - gunakan HANYA bytes yang benar-benar diterima
    if has_active_transfer:
        # Periksa waktu sejak chunk terakhir diterima untuk memutuskan apakah masih aktif
        last_chunk_time = getattr(client, 'last_chunk_time', 0)
        is_recent_activity = (time.time() - last_chunk_time) < 5  # 5 detik

        # Hitung progress berdasarkan bytes aktual yang diterima
        if file_size > 0:
            progress = min(99, int(bytes_received * 100 / file_size))  # Cap at 99% until verified complete
        else:
            progress = 0

        # Tentukan status berdasarkan progress dan waktu terakhir update
        status = getattr(client, 'db_backup_status', 'in_progress')
        chunks_received = getattr(client, 'db_chunks_received', 0)

        logger.debug(f"[DIRECT-DB] Status from file transfer: status={status}, progress={progress}%, received={bytes_received} bytes, chunks={chunks_received}")

        # Jika status menunjukkan selesai, set progress ke 100%
        if status in ['completed', 'received_all_chunks'] or progress >= 99:
            # Verifikasi ukuran file jika kita menggunakan fallback file size
            if file_size == 1000000000 and os.path.exists(file_path):
                # Cek ukuran sebenarnya vs. bytes yang diterima
                if is_recent_activity:
                    # Jika aktivitas baru saja terjadi, tetap gunakan status in_progress
                    status = 'in_progress'
                else:
                    # Jika sudah tidak ada aktivitas untuk beberapa waktu, anggap selesai
                    status = 'complete'
                    progress = 100
            else:
                status = 'complete'
                progress = 100

            # Cek apakah ini Firebird DB dan lakukan validasi file
            is_firebird_db = file_path.lower().endswith('.fdb')
            is_valid = False

            if is_firebird_db and status == 'complete':
                # Coba validasi header file Firebird
                try:
                    with open(file_path, 'rb') as f:
                        # Baca 1024 byte pertama untuk periksa header Firebird
                        header = f.read(1024)
                        # Signature Firebird DB biasanya mengandung string ini di header
                        is_valid = (b'Firebird' in header or b'INTERBASE' in header or
                                  b'GENERAL' in header or b'ODS' in header)

                        if not is_valid:
                            logger.warning(f"[DIRECT-DB] Firebird database validation failed: {file_path}")
                            # Status tetap complete, tapi tambahkan peringatan
                            status_message = "Database transfer completed, but file validation warning"
                        else:
                            logger.info(f"[DIRECT-DB] Firebird database validation successful: {file_path}")
                            status_message = "Database transfer and validation completed successfully"
                except Exception as e:
                    logger.error(f"[DIRECT-DB] Error validating Firebird database: {e}")
                    status_message = "Database transfer completed, but validation error"
            else:
                status_message = "Database transfer in progress"

            if status == 'complete':
                return jsonify({
                    'success': True,
                    'status': status,
                    'progress': progress,
                    'bytes_received': bytes_received,
                    'chunks_received': chunks_received,
                    'file_name': getattr(client, 'db_backup_filename', ''),
                    'file_path': file_path,
                    'file_size': file_size,
                    'validation_result': is_valid if is_firebird_db else None,
                    'message': status_message,
                    'elapsed_time': round(time.time() - getattr(client, 'db_backup_start_time', time.time()), 2),
                    'download_url': f"/static/backups/{client_id}/{getattr(client, 'db_backup_filename', '')}",
                    'polling_complete': (progress >= 100 and status == 'complete')  # Signal to UI to stop polling if complete
                })

        # Jika transfer masih berlangsung
        return jsonify({
            'success': True,
            'status': 'in_progress',
            'progress': progress,
            'bytes_received': bytes_received,
            'chunks_received': chunks_received,
            'file_size': file_size,
            'last_update': getattr(client, 'last_chunk_time', time.time()),
            'should_continue_polling': True
        })

    # Get progress information from direct attributes if available
    if has_direct_attrs:
        direct_progress = getattr(client, 'db_direct_progress', 0)
        direct_bytes_received = getattr(client, 'db_direct_bytes_received', 0)
        chunks_received = getattr(client, 'db_direct_chunks_received', 0)
        last_update = getattr(client, 'db_direct_last_update', 0)

    # Check if download is complete
    if hasattr(client, 'db_direct_download'):
        return jsonify({
            'success': True,
            'status': 'complete',
            'progress': 100,
            'file_name': client.db_direct_download.get('file_name', ''),
            'file_size': client.db_direct_download.get('file_size', 0),
            'bytes_received': client.db_direct_download.get('file_size', 0),
            'elapsed_time': client.db_direct_download.get('elapsed_time', 0),
            'transfer_rate': client.db_direct_download.get('speed_kb', 0),
            'download_url': f"/api/backup/download/{client_id}/{client.db_direct_download.get('file_name', '')}",
            'polling_complete': True  # Signal to UI to stop polling
        })

    # Check if download is in progress
    if direct_progress > 0 and time.time() - last_update < 30:  # Consider active if updated in last 30 seconds
        return jsonify({
            'success': True,
            'status': 'in_progress',
            'progress': direct_progress,
            'bytes_received': direct_bytes_received,
            'chunks_received': chunks_received,
            'last_update': last_update
        })

    # If we have progress but no recent updates, consider it stalled
    if direct_progress > 0:
        return jsonify({
            'success': True,
            'status': 'stalled',
            'progress': direct_progress,
            'bytes_received': direct_bytes_received,
            'chunks_received': chunks_received,
            'last_update': last_update,
            'message': f"Download stalled at {direct_progress}% for {int(time.time() - last_update)} seconds"
        })

    # Default response
    return jsonify({
        'success': True,
        'status': 'unknown',
        'message': "Direct database access status unknown",
        'bytes_received': bytes_received
    })

@app.route('/api/direct-db/cancel/<client_id>', methods=['POST'])
def api_direct_db_cancel(client_id):
    """API endpoint to cancel an ongoing database transfer"""
    logger.info(f"[DIRECT-DB] Cancellation request for client {client_id}")

    # Validate client_id
    with clients_lock:
        if client_id not in clients:
            logger.error(f"[DIRECT-DB] Client {client_id} not found for cancellation")
            return jsonify({
                'success': False,
                'message': "Client not found"
            })

        client = clients[client_id]

        # Cek apakah ada transfer yang sedang berlangsung
        if not hasattr(client, 'db_file_path') or not hasattr(client, 'db_file_received'):
            logger.warning(f"[DIRECT-DB] No active transfer found for client {client_id}")
            return jsonify({
                'success': False,
                'message': "No active transfer found"
            })

        file_path = client.db_file_path

        try:
            # Reset status transfer pada client object
            logger.info(f"[DIRECT-DB] Cancelling transfer for client {client_id}")

            # Catat status untuk referensi
            bytes_received = getattr(client, 'db_file_received', 0)
            chunks_received = getattr(client, 'db_chunks_received', 0)

            # Simpan file yang sudah ditransfer sebagai .cancelled untuk debugging
            if os.path.exists(file_path):
                # Jika ukuran file cukup besar, simpan untuk keperluan debug/resume
                if os.path.getsize(file_path) > 1024*1024:  # 1MB
                    cancelled_path = f"{file_path}.cancelled"
                    try:
                        import shutil
                        shutil.copy2(file_path, cancelled_path)
                        logger.info(f"[DIRECT-DB] Saved partial transfer to {cancelled_path}")
                    except Exception as e:
                        logger.error(f"[DIRECT-DB] Error saving cancelled file: {e}")

                # Coba hapus file yang sedang ditransfer
                try:
                    os.remove(file_path)
                    logger.info(f"[DIRECT-DB] Removed partial file: {file_path}")
                except Exception as e:
                    logger.error(f"[DIRECT-DB] Error removing partial file: {e}")

            # Reset status transfer
            client.db_backup_status = 'cancelled'
            client.db_file_path = ''
            client.db_file_received = 0
            client.db_actual_received = 0
            client.db_chunks_received = 0

            logger.info(f"[DIRECT-DB] Transfer cancelled. Stats: {bytes_received} bytes, {chunks_received} chunks received")
            return jsonify({
                'success': True,
                'message': f"Transfer cancelled. {formatBytes(bytes_received)} were received.",
                'bytes_received': bytes_received,
                'chunks_received': chunks_received
            })

        except Exception as e:
            logger.error(f"[DIRECT-DB] Error cancelling transfer: {e}")
            return jsonify({
                'success': False,
                'message': f"Error cancelling transfer: {str(e)}"
            })

def formatBytes(bytes, decimals=2):
    """Format bytes to human readable string"""
    if bytes == 0:
        return "0 Bytes"

    k = 1024
    dm = decimals
    sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"]

    i = int(math.floor(math.log(bytes) / math.log(k)))

    return f"{round(bytes / (k ** i), dm)} {sizes[i]}"
