# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['ifess_config_gui.py'],
    pathex=[],
    binaries=[],
    datas=[('client_config.json', '.'), ('client_config_oauth_tokens.json', '.'), ('gdrive_client_oauth_simple.py', '.'), ('common', 'common')],
    hiddenimports=['tkinter', 'tkinter.ttk', 'googleapiclient.discovery', 'fdb'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ifess_config_gui',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
