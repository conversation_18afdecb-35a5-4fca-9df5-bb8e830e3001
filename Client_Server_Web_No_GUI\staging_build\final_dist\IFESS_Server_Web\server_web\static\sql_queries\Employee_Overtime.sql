-- Query untuk mencari data overtime ka<PERSON><PERSON> berdasarkan ID dan periode
SELECT 
  e.EMPNO AS NIP,
  e.NAME AS Nama_Karyawan,
  o.INPDATE AS Tanggal_Overtime,
  o.HOURS AS Jam_Kerja,
  j.DESCRIPTION AS Deskripsi_Pekerjaan,
  o.BASICRATE AS Tarif_Dasar,
  o.ADDRATE AS Tarif_Tambahan,
  o.HOURS * o.BASICRATE AS Nilai_Dasar,
  o.HOURS * o.ADDRATE AS Nilai_Tambahan,
  (o.HOURS * o.BASICRATE) + (o.HOURS * o.ADDRATE) AS Total_Nilai
FROM OVERTIME o
JOIN EMPLOYEE e ON o.EMPID = e.ID
JOIN JOBCODE j ON o.JOBID = j.ID
WHERE e.EMPNO = '$EMPLOYEE_ID'
  AND o.INPDATE BETWEEN '$START_DATE' AND '$END_DATE'
ORDER BY o.INPDATE
