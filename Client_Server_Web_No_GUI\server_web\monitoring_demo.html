<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Monitoring Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="container-fluid mt-3">
        <div class="row">
            <div class="col-md-3">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line me-2"></i>Monitoring Queries
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group" id="monitoring-queries">
                            <div class="list-group-item active">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">monitor_diff_job_field</h6>
                                    <button type="button" class="btn btn-sm btn-light monitoring-query" data-query-name="monitor_diff_job_field">
                                        <i class="fas fa-play me-1"></i>Run
                                    </button>
                                </div>
                                <p class="mb-0 small text-white">
                                    Query untuk menemukan data yang salah di bulan berjalan (bulan saat ini). Monitoring query to find inconsistencies between job codes and field codes.
                                </p>
                            </div>
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="mb-0">simple_test</h6>
                                    <button type="button" class="btn btn-sm btn-primary monitoring-query" data-query-name="simple_test">
                                        <i class="fas fa-play me-1"></i>Run
                                    </button>
                                </div>
                                <p class="mb-0 small text-muted">
                                    Simple test query to check if we can get results
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-9">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-table me-2"></i>Monitoring Results
                            <div class="spinner-border spinner-border-sm text-light ms-2 d-none" id="monitoring-loading" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="monitoring-results">
                            <h4 class="mb-3">Results for: monitor_diff_job_field</h4>
                            <h5 class="mb-3">Client: PTRJ_P1A.FDB</h5>
                            
                            <div class="table-responsive mb-4">
                                <table class="table table-striped table-bordered table-hover">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>KaryawanID</th>
                                            <th>OvertimeID</th>
                                            <th>TanggalOvertime</th>
                                            <th>JamKerja</th>
                                            <th>KodeField</th>
                                            <th>AccCode</th>
                                            <th>DeskripsiPekerjaan</th>
                                            <th>TarifDasar</th>
                                            <th>TarifTambahan</th>
                                            <th>NilaiDasar</th>
                                            <th>NilaiTambahan</th>
                                            <th>Catatan</th>
                                            <th>KodeField_2Char</th>
                                            <th>AccCode_2Char</th>
                                            <th>NomorKendaraan</th>
                                            <th>ModelKendaraan</th>
                                            <th>VehicleID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>23</td>
                                            <td>53204</td>
                                            <td>2025-04-07</td>
                                            <td>2.00</td>
                                            <td>YYYYY</td>
                                            <td>GA9234</td>
                                            <td>UPKEEP OF BUILDINGS</td>
                                            <td>10000</td>
                                            <td>5000</td>
                                            <td>20000</td>
                                            <td>10000</td>
                                            <td>Test data</td>
                                            <td>YY</td>
                                            <td>GA</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td>23</td>
                                            <td>53210</td>
                                            <td>2025-04-07</td>
                                            <td>2.00</td>
                                            <td>YYYYY</td>
                                            <td>GA9234</td>
                                            <td>UPKEEP OF BUILDINGS</td>
                                            <td>10000</td>
                                            <td>5000</td>
                                            <td>20000</td>
                                            <td>10000</td>
                                            <td>Test data 2</td>
                                            <td>YY</td>
                                            <td>GA</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="text-muted mb-4">
                                <i class="fas fa-info-circle me-1"></i>Showing 2 rows.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Handle monitoring query button click
            $('.monitoring-query').click(function() {
                const queryName = $(this).data('query-name');
                
                // Update active query
                $('.list-group-item').removeClass('active');
                $(this).closest('.list-group-item').addClass('active');
                
                // Update button styles
                $('.monitoring-query').removeClass('btn-light').addClass('btn-primary');
                $(this).removeClass('btn-primary').addClass('btn-light');
                
                // Show loading indicator
                $('#monitoring-loading').removeClass('d-none');
                
                // Simulate loading delay
                setTimeout(function() {
                    // Hide loading indicator
                    $('#monitoring-loading').addClass('d-none');
                    
                    // Update results based on query name
                    if (queryName === 'simple_test') {
                        $('#monitoring-results').html(`
                            <h4 class="mb-3">Results for: simple_test</h4>
                            <h5 class="mb-3">Client: PTRJ_P1A.FDB</h5>
                            
                            <div class="table-responsive mb-4">
                                <table class="table table-striped table-bordered table-hover">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>KaryawanID</th>
                                            <th>OvertimeID</th>
                                            <th>TanggalOvertime</th>
                                            <th>JamKerja</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>23</td>
                                            <td>53204</td>
                                            <td>2025-04-07</td>
                                            <td>2.00</td>
                                        </tr>
                                        <tr>
                                            <td>23</td>
                                            <td>53210</td>
                                            <td>2025-04-07</td>
                                            <td>2.00</td>
                                        </tr>
                                        <tr>
                                            <td>24</td>
                                            <td>53211</td>
                                            <td>2025-04-07</td>
                                            <td>3.50</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="text-muted mb-4">
                                <i class="fas fa-info-circle me-1"></i>Showing 3 rows.
                            </div>
                        `);
                    } else {
                        // Show the default monitoring query results (already in the HTML)
                        $('#monitoring-results').html(`
                            <h4 class="mb-3">Results for: monitor_diff_job_field</h4>
                            <h5 class="mb-3">Client: PTRJ_P1A.FDB</h5>
                            
                            <div class="table-responsive mb-4">
                                <table class="table table-striped table-bordered table-hover">
                                    <thead class="table-primary">
                                        <tr>
                                            <th>KaryawanID</th>
                                            <th>OvertimeID</th>
                                            <th>TanggalOvertime</th>
                                            <th>JamKerja</th>
                                            <th>KodeField</th>
                                            <th>AccCode</th>
                                            <th>DeskripsiPekerjaan</th>
                                            <th>TarifDasar</th>
                                            <th>TarifTambahan</th>
                                            <th>NilaiDasar</th>
                                            <th>NilaiTambahan</th>
                                            <th>Catatan</th>
                                            <th>KodeField_2Char</th>
                                            <th>AccCode_2Char</th>
                                            <th>NomorKendaraan</th>
                                            <th>ModelKendaraan</th>
                                            <th>VehicleID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>23</td>
                                            <td>53204</td>
                                            <td>2025-04-07</td>
                                            <td>2.00</td>
                                            <td>YYYYY</td>
                                            <td>GA9234</td>
                                            <td>UPKEEP OF BUILDINGS</td>
                                            <td>10000</td>
                                            <td>5000</td>
                                            <td>20000</td>
                                            <td>10000</td>
                                            <td>Test data</td>
                                            <td>YY</td>
                                            <td>GA</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td>23</td>
                                            <td>53210</td>
                                            <td>2025-04-07</td>
                                            <td>2.00</td>
                                            <td>YYYYY</td>
                                            <td>GA9234</td>
                                            <td>UPKEEP OF BUILDINGS</td>
                                            <td>10000</td>
                                            <td>5000</td>
                                            <td>20000</td>
                                            <td>10000</td>
                                            <td>Test data 2</td>
                                            <td>YY</td>
                                            <td>GA</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="text-muted mb-4">
                                <i class="fas fa-info-circle me-1"></i>Showing 2 rows.
                            </div>
                        `);
                    }
                }, 1000);
            });
        });
    </script>
</body>
</html>
