"""
API endpoints for the robust database transfer system.
"""

import os
import time
import json
import logging
import base64
from flask import request, jsonify, Blueprint

from robust_transfer import get_transfer_manager, TransferState

# Configure logger
logger = logging.getLogger('server_web')

# Create Blueprint for robust transfer API
robust_transfer_routes = Blueprint('robust_transfer_routes', __name__)

@robust_transfer_routes.route('/api/robust-transfer/initiate/<client_id>', methods=['POST'])
def api_initiate_transfer(client_id):
    """API endpoint to initiate a new database transfer"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })
        
        # Get file info from request
        file_info = request.json or {}
        client_name = file_info.get('client_name', client_id)
        
        # Initiate transfer
        transfer_info = transfer_manager.initiate_transfer(client_id, client_name, file_info)
        
        return jsonify({
            'success': True,
            'message': f"Transfer initiated for {client_name}",
            'transfer_info': {
                'file_name': transfer_info['file_name'],
                'file_path': transfer_info['file_path'],
                'file_size': transfer_info['file_size'],
                'file_size_formatted': _format_bytes(transfer_info['file_size']),
                'state': transfer_info['state'],
                'chunk_size': transfer_info['chunk_size']
            }
        })
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error initiating transfer: {e}")
        return jsonify({
            'success': False,
            'message': f"Error initiating transfer: {str(e)}"
        })

@robust_transfer_routes.route('/api/robust-transfer/chunk/<client_id>', methods=['POST'])
def api_upload_chunk(client_id):
    """API endpoint to upload a chunk of data"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })
        
        # Get chunk info from request
        chunk_info = request.json or {}
        
        # Process chunk
        result = transfer_manager.process_chunk(client_id, chunk_info)
        
        # Return simplified result
        response = {
            'success': result.get('success', False),
            'message': result.get('message', "Unknown error")
        }
        
        # Add transfer info if available
        if 'transfer_info' in result:
            transfer_info = result['transfer_info']
            response['progress'] = transfer_info.get('progress', 0)
            response['bytes_received'] = transfer_info.get('bytes_received', 0)
            response['chunks_received'] = transfer_info.get('chunks_received', 0)
        
        return jsonify(response)
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error processing chunk: {e}")
        return jsonify({
            'success': False,
            'message': f"Error processing chunk: {str(e)}"
        })

@robust_transfer_routes.route('/api/robust-transfer/status/<client_id>', methods=['GET'])
def api_transfer_status(client_id):
    """API endpoint to get the status of a transfer"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })
        
        # Get transfer status
        status = transfer_manager.get_transfer_status(client_id)
        
        return jsonify(status)
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error getting transfer status: {e}")
        return jsonify({
            'success': False,
            'message': f"Error getting transfer status: {str(e)}"
        })

@robust_transfer_routes.route('/api/robust-transfer/resume/<client_id>', methods=['POST'])
def api_resume_transfer(client_id):
    """API endpoint to resume a paused or stalled transfer"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })
        
        # Resume transfer
        result = transfer_manager.resume_transfer(client_id)
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error resuming transfer: {e}")
        return jsonify({
            'success': False,
            'message': f"Error resuming transfer: {str(e)}"
        })

@robust_transfer_routes.route('/api/robust-transfer/cancel/<client_id>', methods=['POST'])
def api_cancel_transfer(client_id):
    """API endpoint to cancel an active transfer"""
    try:
        # Get transfer manager
        transfer_manager = get_transfer_manager()
        if not transfer_manager:
            return jsonify({
                'success': False,
                'message': "Transfer manager not initialized"
            })
        
        # Cancel transfer
        result = transfer_manager.cancel_transfer(client_id)
        
        return jsonify(result)
    except Exception as e:
        logger.error(f"[ROBUST-TRANSFER-API] Error cancelling transfer: {e}")
        return jsonify({
            'success': False,
            'message': f"Error cancelling transfer: {str(e)}"
        })

def _format_bytes(bytes_value):
    """Format bytes to human-readable string"""
    if bytes_value == 0:
        return "0 Bytes"
    
    sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB"]
    i = 0
    while bytes_value >= 1024 and i < len(sizes) - 1:
        bytes_value /= 1024
        i += 1
    
    return f"{bytes_value:.2f} {sizes[i]}"
