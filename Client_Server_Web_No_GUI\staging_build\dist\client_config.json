{"server_address": "localhost", "server_port": 5555, "reconnect_interval": 5, "client_id": "client_coba", "display_name": "FDB-Client-Monitoring", "database": {"path": "D:/Gawean Rebinmas/Monitoring Database/Database Ifess/PTRJ_AB1_08042025/PTRJ_AB1.FDB", "username": "sysdba", "password": "masterkey", "isql_path": "C:/Program Files (x86)/Firebird/Firebird_1_5/bin/isql.exe", "use_localhost": true}, "mega": {"email": "<EMAIL>", "password": "ptrj@123"}, "gdrive": {"authentication_method": "token_file", "token_file": "token.json", "folder_structure": {"root_folder": "Backup_PTRJ", "app_folder": "Auto_Backup_App", "database_folder": "Ifess_Database", "client_folder": "{client_id}"}, "upload_schedule": {"enabled": true, "daily_time": "12:00", "timezone": "local"}}, "connection": {"exponential_backoff": {"enabled": true, "base_interval": 5, "max_interval": 300, "jitter_percent": 20}, "keep_alive": true, "timeout": null}}