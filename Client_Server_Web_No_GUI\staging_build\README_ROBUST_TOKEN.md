# IFESS Robust Token Recovery

Dokumen ini menjelaskan fitur auto-recovery token yang kuat untuk aplikasi IFESS.

## Fitur Baru

1. **Auto-Recovery Token yang Kuat**
   - Mencoba mengunduh token dari Google Drive beberapa kali jika gagal
   - Memvalidasi token yang diunduh untuk memastikan formatnya benar
   - Membuat backup token yang valid untuk digunakan jika token baru tidak dapat diunduh
   - Menghapus token yang tidak valid dan mencoba mengunduh yang baru
   - Mencoba memulihkan dari backup jika token rusak atau tidak valid

2. **Validasi Token yang Komprehensif**
   - Memeriksa keberadaan semua field yang diperlukan
   - Memeriksa apakah token dan refresh_token tidak kosong
   - Memeriksa apakah token sudah kedaluwarsa
   - Mendukung berbagai format tanggal dan zona waktu

3. **GUI Pengujian yang Ditingkatkan**
   - Menampilkan informasi validasi token yang lebih detail
   - Menampilkan status backup token
   - Menampilkan informasi tentang mekanisme recovery
   - Progres bar yang lebih informatif selama proses download

## Cara Menggunakan

### Menjalankan Aplikasi dengan Token Recovery yang Kuat

Gunakan script `run_with_robust_token.bat` untuk menjalankan aplikasi dengan token recovery yang kuat:

```
run_with_robust_token.bat
```

Script ini akan:
- Menginstal dependensi yang diperlukan
- Mengunduh token dari Google Drive dengan mekanisme recovery yang kuat
- Menjalankan server web, client hidden, dan client debug

### Menjalankan GUI Pengujian

Gunakan script `run_gdrive_test_gui.bat` untuk menjalankan GUI pengujian:

```
run_gdrive_test_gui.bat
```

GUI pengujian memungkinkan Anda untuk:
- Mengunduh token dari Google Drive dengan mekanisme recovery yang kuat
- Memeriksa validitas token dan status kedaluwarsa
- Melihat informasi tentang backup token dan mekanisme recovery
- Memilih file untuk diupload
- Melihat progres upload
- Melihat hasil upload termasuk link ke file

## Mekanisme Recovery

Aplikasi menggunakan mekanisme recovery bertingkat untuk memastikan selalu memiliki token yang valid:

1. **Mencoba menggunakan token lokal yang ada**
   - Memvalidasi token untuk memastikan formatnya benar
   - Memeriksa apakah token akan kedaluwarsa segera

2. **Jika token akan kedaluwarsa segera atau tidak valid**:
   - Mencoba mengunduh token baru dari Google Drive
   - Memvalidasi token yang diunduh
   - Jika token baru valid, gunakan token tersebut
   - Jika token baru tidak valid, gunakan token lama jika masih valid

3. **Jika token rusak atau tidak dapat dimuat**:
   - Menghapus token yang rusak
   - Mencoba memulihkan dari backup
   - Jika backup valid, gunakan backup
   - Jika backup tidak valid, coba unduh token baru

4. **Jika semua upaya gagal**:
   - Mencoba beberapa kali dengan delay yang meningkat
   - Menggunakan backup sebagai upaya terakhir

## Konfigurasi

### Parameter Recovery

Anda dapat mengubah parameter recovery di file `token_downloader.py`:

- `MAX_DOWNLOAD_ATTEMPTS`: Jumlah maksimum percobaan download (default: 5)
- `DOWNLOAD_RETRY_DELAY`: Delay antara percobaan download dalam detik (default: 5)
- `max_attempts` di fungsi `get_token()`: Jumlah maksimum percobaan untuk mendapatkan token valid (default: 3)

### Token Google Drive

Token OAuth disimpan di Google Drive dan dapat diakses melalui link:
https://drive.google.com/file/d/1q2fJ-ORqJj2TQwdTKQgXG6RqJ7WqENAh/view?usp=sharing

## Troubleshooting

### Masalah Mengunduh Token

Jika token tidak dapat diunduh dari Google Drive setelah beberapa percobaan:
1. Pastikan komputer memiliki akses internet
2. Periksa apakah link Google Drive masih valid
3. Periksa log di `token_downloader.log` untuk informasi lebih detail
4. Coba jalankan `python token_downloader.py` secara manual untuk melihat error yang lebih detail

### Masalah Token Tidak Valid

Jika token yang diunduh tidak valid:
1. Periksa apakah token di Google Drive masih valid
2. Coba perbarui token di Google Drive dengan token yang baru
3. Periksa log di `token_downloader.log` untuk informasi lebih detail

## Informasi Debug

Aplikasi debug (ifess_client_debug.py) dan GUI pengujian (ifess_gdrive_test_gui.py) menampilkan informasi tentang status token dan upload Google Drive. Gunakan aplikasi ini untuk memantau proses dan melihat pesan error jika terjadi masalah.

Log detail tentang proses token recovery dapat ditemukan di file `token_downloader.log`.
