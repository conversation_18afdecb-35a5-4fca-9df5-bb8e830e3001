import socket
import json
import struct
import time
import logging
import sys
import os
import traceback

# Setup basic logging for debugging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
net_logger = logging.getLogger("IFESS-Network")

# Konstanta untuk komunikasi
DEFAULT_PORT = 5555
BUFFER_SIZE = 8192
ENCODING = 'utf-8'

class NetworkMessage:
    """Kelas untuk merepresentasikan pesan jaringan"""
    TYPE_QUERY = 'query'
    TYPE_RESULT = 'result'
    TYPE_ERROR = 'error'
    TYPE_REGISTER = 'register'
    TYPE_PING = 'ping'
    TYPE_PONG = 'pong'
    # Database backup message types - must match between client and server
    TYPE_DB_REQUEST = 'db_request'    # Request for database file
    TYPE_DB_INFO = 'db_info'          # Information about database file
    TYPE_DB_CHUNK = 'db_chunk'        # Chunk of database file
    TYPE_DB_COMPLETE = 'db_complete'  # Database transfer complete
    TYPE_DB_ACK = 'db_ack'            # Acknowledgment of database info
    TYPE_DB_PROGRESS = 'db_progress'  # Progress update for database transfer
    TYPE_DB_ERROR = 'db_error'        # Error during database transfer
    # MEGA upload message types
    TYPE_MEGA_UPLOAD_REQUEST = 'mega_upload_request'
    TYPE_MEGA_UPLOAD_ACK = 'mega_upload_ack'
    TYPE_MEGA_UPLOAD_PROGRESS = 'mega_upload_progress'
    TYPE_MEGA_UPLOAD_RESULT = 'mega_upload_result'

    def __init__(self, msg_type, data, client_id=None):
        self.msg_type = msg_type
        # Untuk backward compatibility dengan kode yang menggunakan 'type'
        self.type = msg_type
        self.data = data
        self.client_id = client_id
        self.timestamp = time.time()

    def to_json(self):
        """Konversi pesan ke format JSON"""
        return json.dumps({
            'msg_type': self.msg_type,
            'data': self.data,
            'client_id': self.client_id,
            'timestamp': self.timestamp
        })

    @classmethod
    def from_json(cls, json_str):
        """Buat objek pesan dari string JSON"""
        try:
            data = json.loads(json_str)
            return cls(
                data.get('msg_type'),
                data.get('data'),
                data.get('client_id')
            )
        except json.JSONDecodeError:
            net_logger.error("Failed to decode JSON message")
            return cls(cls.TYPE_ERROR, "Invalid JSON message", None)

    def __str__(self):
        """String representation for logging"""
        data_repr = str(self.data)
        if len(data_repr) > 100:
            data_repr = data_repr[:97] + "..."
        return f"NetworkMessage(type={self.msg_type}, client_id={self.client_id}, data={data_repr})"

def send_message(sock, message):
    """
    Kirim pesan melalui socket.
    Protokol: [4-byte length prefix][message bytes]

    :param sock: Socket terhubung untuk mengirim data
    :param message: Objek NetworkMessage untuk dikirim
    :return: True jika berhasil, False jika gagal
    """
    # Log state before sending
    net_logger.debug(f"[SEND] Sending message: {message}")

    # Check if socket is valid
    if sock is None:
        net_logger.error("[SEND] Cannot send message: Socket is None")
        return False

    try:
        # Konversi pesan ke JSON
        json_data = message.to_json()

        # Debug info tentang pesan yang akan dikirim
        msg_type = message.msg_type
        client_id = message.client_id

        if isinstance(message.data, dict):
            data_keys = list(message.data.keys())
        else:
            data_keys = "non-dict"

        net_logger.info(f"[SEND] Message type={msg_type}, client={client_id}, keys={data_keys}")

        # Log special message types for debugging
        if msg_type == NetworkMessage.TYPE_DB_REQUEST:
            net_logger.info(f"[SEND] [BACKUP] Sending DB_REQUEST to client: {client_id}")
        elif msg_type == NetworkMessage.TYPE_DB_INFO:
            net_logger.info(f"[SEND] [BACKUP] Sending DB_INFO from client: {client_id}")
        elif msg_type == NetworkMessage.TYPE_DB_CHUNK:
            chunk_num = message.data.get('chunk_number', '?')
            total_chunks = message.data.get('total_chunks', '?')
            progress = message.data.get('progress', '?')
            net_logger.debug(f"[SEND] [BACKUP] Sending chunk {chunk_num}/{total_chunks} ({progress}%)")
        elif msg_type == NetworkMessage.TYPE_DB_COMPLETE:
            net_logger.info(f"[SEND] [BACKUP] Sending DB_COMPLETE from client: {client_id}")
        elif msg_type == NetworkMessage.TYPE_ERROR and "backup" in str(message.data).lower():
            net_logger.error(f"[SEND] [BACKUP] Sending error related to backup: {message.data}")

        # Konversi string JSON ke bytes
        data = json_data.encode(ENCODING)
        # Dapatkan panjang data dalam bytes
        msg_len = len(data)
        net_logger.debug(f"[SEND] Message size: {msg_len} bytes")

        # Implement retry with exponential backoff for large messages
        max_retries = 3
        for retry in range(max_retries):
            try:
                # Kirim panjang pesan sebagai unsigned int (4 bytes)
                sock.sendall(struct.pack('>I', msg_len))
                # Kirim data pesan
                sock.sendall(data)
                net_logger.debug(f"[SEND] Message sent successfully")
                return True
            except ConnectionError as ce:
                if retry < max_retries - 1:
                    net_logger.warning(f"[SEND] Connection error on attempt {retry+1}/{max_retries}: {ce}. Retrying...")
                    time.sleep(0.5 * (2 ** retry))  # Exponential backoff
                else:
                    net_logger.error(f"[SEND] Connection error after {max_retries} attempts: {ce}")
                    return False
            except socket.timeout as to:
                if retry < max_retries - 1:
                    net_logger.warning(f"[SEND] Socket timeout on attempt {retry+1}/{max_retries}: {to}. Retrying...")
                    time.sleep(0.5 * (2 ** retry))  # Exponential backoff
                else:
                    net_logger.error(f"[SEND] Socket timeout after {max_retries} attempts: {to}")
                    return False
            except Exception as e:
                if retry < max_retries - 1:
                    net_logger.warning(f"[SEND] Error on attempt {retry+1}/{max_retries}: {e}. Retrying...")
                    time.sleep(0.5 * (2 ** retry))  # Exponential backoff
                else:
                    net_logger.error(f"[SEND] Error after {max_retries} attempts: {e}")
                    net_logger.debug(f"[SEND] Error details: {traceback.format_exc()}")
                    return False
    except Exception as e:
        net_logger.error(f"[SEND] Unexpected error: {e}")
        net_logger.debug(f"[SEND] Error details: {traceback.format_exc()}")
        return False

def receive_message(sock):
    """
    Terima pesan dari socket.
    Protokol: [4-byte length prefix][message bytes]

    :param sock: Socket terhubung untuk menerima data
    :return: Objek NetworkMessage atau None jika terjadi kesalahan
    """
    # Check if socket is valid
    if sock is None:
        net_logger.error("[RECV] Cannot receive message: Socket is None")
        return None

    try:
        # Tambahkan log untuk identifikasi client/socket
        socket_details = f"unknown"
        try:
            if sock and hasattr(sock, 'getsockname'):
                socket_details = f"{sock.getsockname()}"
        except:
            pass

        net_logger.info(f"[RECV] === BEGIN RECEIVE MESSAGE ATTEMPT === Socket: {socket_details}")

        # Log socket timeout setting
        current_timeout = None
        try:
            if sock:
                current_timeout = sock.gettimeout()
                # Remove timeout for unlimited waiting
                sock.settimeout(None)  # No timeout - wait indefinitely
                net_logger.info(f"[RECV] Removed socket timeout (was {current_timeout}) - waiting indefinitely")
        except Exception as e:
            net_logger.warning(f"[RECV] Could not get/set socket timeout: {e}")

        # Terima 4 byte pertama yang menunjukkan panjang pesan
        len_bytes = b''
        bytes_received = 0
        max_attempts = 5  # Meningkatkan jumlah percobaan
        attempt = 0

        # Coba beberapa kali untuk mendapatkan 4 byte panjang pesan
        while bytes_received < 4 and attempt < max_attempts:
            try:
                net_logger.info(f"[RECV] Waiting to receive message length (attempt {attempt+1}/{max_attempts})")
                print(f"[NETWORK] Waiting to receive message length (attempt {attempt+1}/{max_attempts})")

                chunk = sock.recv(4 - bytes_received)
                if not chunk:  # Koneksi ditutup
                    if attempt < max_attempts - 1:
                        net_logger.warning(f"[RECV] No data received, trying again (attempt {attempt+1}/{max_attempts})")
                        print(f"[NETWORK] No data received, trying again (attempt {attempt+1}/{max_attempts})")
                        time.sleep(1.0)  # Tunggu lebih lama sebelum mencoba lagi
                        attempt += 1
                        continue
                    else:
                        net_logger.error("[RECV] Connection closed: no data received for message length after multiple attempts")
                        print("[NETWORK] Connection closed: no data received for message length after multiple attempts")
                        return None
                len_bytes += chunk
                bytes_received = len(len_bytes)
                net_logger.info(f"[RECV] Received {bytes_received}/4 bytes for message length")
                print(f"[NETWORK] Received {bytes_received}/4 bytes for message length")
            except socket.timeout:
                if attempt < max_attempts - 1:
                    net_logger.warning(f"[RECV] Timeout while receiving message length, trying again (attempt {attempt+1}/{max_attempts})")
                    print(f"[NETWORK] Timeout while receiving message length, trying again (attempt {attempt+1}/{max_attempts})")
                    attempt += 1
                    time.sleep(1.0)  # Tunggu lebih lama sebelum mencoba lagi
                    continue
                else:
                    net_logger.error("[RECV] Repeated timeout while receiving message length")
                    print("[NETWORK] Repeated timeout while receiving message length")
                    # Restore timeout setting
                    if current_timeout is not None:
                        try:
                            sock.settimeout(current_timeout)
                        except:
                            pass
                    return None
            except ConnectionError as ce:
                net_logger.error(f"[RECV] Connection error while receiving message length: {ce}")
                print(f"[NETWORK] Connection error while receiving message length: {ce}")
                # Restore timeout setting
                if current_timeout is not None:
                    try:
                        sock.settimeout(current_timeout)
                    except:
                        pass
                return None
            except Exception as e:
                net_logger.error(f"[RECV] Error receiving message length: {e}")
                net_logger.debug(f"[RECV] Error details: {traceback.format_exc()}")
                print(f"[NETWORK] Error receiving message length: {e}")
                # Restore timeout setting
                if current_timeout is not None:
                    try:
                        sock.settimeout(current_timeout)
                    except:
                        pass
                return None

        if len(len_bytes) < 4:
            net_logger.error(f"[RECV] Could not receive 4 bytes for message length, got only {len(len_bytes)} bytes")
            print(f"[NETWORK] Could not receive 4 bytes for message length, got only {len(len_bytes)} bytes")
            # Restore timeout setting
            if current_timeout is not None:
                try:
                    sock.settimeout(current_timeout)
                except:
                    pass
            return None  # Koneksi ditutup

        # Unpack 4 bytes menjadi unsigned int
        try:
            msg_len = struct.unpack('>I', len_bytes)[0]
            net_logger.info(f"[RECV] Message length: {msg_len} bytes")
            print(f"[NETWORK] Message length: {msg_len} bytes")
        except struct.error as se:
            net_logger.error(f"[RECV] Error unpacking message length: {se}")
            print(f"[NETWORK] Error unpacking message length: {se}")
            # Restore timeout setting
            if current_timeout is not None:
                try:
                    sock.settimeout(current_timeout)
                except:
                    pass
            return None

        # Validasi ukuran pesan untuk mencegah DoS
        if msg_len > 50 * 1024 * 1024:  # 50MB batas maksimum
            net_logger.error(f"[RECV] Message too large: {msg_len} bytes (max 50MB)")
            print(f"[NETWORK] Message too large: {msg_len} bytes (max 50MB)")
            # Restore timeout setting
            if current_timeout is not None:
                try:
                    sock.settimeout(current_timeout)
                except:
                    pass
            return None

        # Terima semua data pesan
        data = b''
        remaining = msg_len
        start_time = time.time()
        # No timeout limit for receiving data
        retry_count = 0
        max_retries = 20  # Meningkatkan jumlah retry untuk kasus koneksi lambat

        net_logger.info(f"[RECV] Starting to receive message data ({msg_len} bytes)")
        print(f"[NETWORK] Starting to receive message data ({msg_len} bytes)")

        while remaining > 0:
            # No global timeout check - we'll wait indefinitely
            # Just log progress periodically
            elapsed_time = time.time() - start_time
            if elapsed_time > 0 and elapsed_time % 60 < 0.1:  # Log approximately every minute
                net_logger.info(f"[RECV] Still receiving data after {int(elapsed_time)}s, {remaining} bytes remaining")
                print(f"[NETWORK] Still receiving data after {int(elapsed_time)}s, {remaining} bytes remaining")

            try:
                # Calculate progress for large messages
                if msg_len > 1024 * 1024:  # Only for messages > 1MB
                    progress = 100 - (remaining * 100 // msg_len)
                    if progress % 5 == 0:  # Log every 5% (instead of 10%)
                        received = msg_len - remaining
                        net_logger.info(f"[RECV] Progress: {progress}% ({received}/{msg_len} bytes)")
                        print(f"[NETWORK] Progress: {progress}% ({received}/{msg_len} bytes)")

                # Receive chunk with adjusted buffer size for large messages
                current_buffer = min(remaining, BUFFER_SIZE)
                chunk = sock.recv(current_buffer)

                if not chunk:
                    retry_count += 1
                    if retry_count <= max_retries:
                        net_logger.warning(f"[RECV] No data received, retrying ({retry_count}/{max_retries})")
                        print(f"[NETWORK] No data received, retrying ({retry_count}/{max_retries})")
                        time.sleep(1.0)  # Tunggu lebih lama
                        continue
                    else:
                        net_logger.error("[RECV] Connection closed unexpectedly while receiving message data")
                        print("[NETWORK] Connection closed unexpectedly while receiving message data")
                        # Restore timeout setting
                        if current_timeout is not None:
                            try:
                                sock.settimeout(current_timeout)
                            except:
                                pass
                        return None  # Koneksi ditutup secara tidak terduga

                # Reset retry counter on successful receive
                retry_count = 0
                data += chunk
                remaining -= len(chunk)
            except socket.timeout:
                retry_count += 1
                if retry_count <= max_retries:
                    net_logger.warning(f"[RECV] Timeout while receiving chunk, retrying ({retry_count}/{max_retries})")
                    print(f"[NETWORK] Timeout while receiving chunk, retrying ({retry_count}/{max_retries})")
                    time.sleep(1.0)  # Tunggu lebih lama
                    continue
                else:
                    net_logger.error(f"[RECV] Too many timeouts while receiving message data")
                    print(f"[NETWORK] Too many timeouts while receiving message data")
                    # Restore timeout setting
                    if current_timeout is not None:
                        try:
                            sock.settimeout(current_timeout)
                        except:
                            pass
                    return None
            except Exception as e:
                net_logger.error(f"[RECV] Error receiving message data: {e}")
                print(f"[NETWORK] Error receiving message data: {e}")
                # Restore timeout setting
                if current_timeout is not None:
                    try:
                        sock.settimeout(current_timeout)
                    except:
                        pass
                return None

        # Restore original timeout
        if current_timeout is not None:
            try:
                sock.settimeout(current_timeout)
                net_logger.debug(f"[RECV] Restored socket timeout to {current_timeout}")
            except Exception as e:
                net_logger.warning(f"[RECV] Could not restore socket timeout: {e}")

        # Dekode data ke string JSON
        try:
            net_logger.info(f"[RECV] Received complete message, size: {len(data)} bytes")
            print(f"[NETWORK] Received complete message, size: {len(data)} bytes")
            json_data = data.decode(ENCODING)

            # Parse pesan JSON
            message = NetworkMessage.from_json(json_data)

            # Log received message type
            net_logger.info(f"[RECV] Message type: {message.msg_type}, Client ID: {message.client_id}")
            print(f"[NETWORK] Message type: {message.msg_type}, Client ID: {message.client_id}")

            # Log special message types with more details
            if message.msg_type == NetworkMessage.TYPE_DB_REQUEST:
                net_logger.info(f"[RECV] [BACKUP] Received database backup request for client: {message.client_id}")
                net_logger.info(f"[RECV] [BACKUP] Request data: {message.data}")
                print(f"[NETWORK] [BACKUP] Received database backup request for client: {message.client_id}")
                print(f"[NETWORK] [BACKUP] Request data: {message.data}")
            elif message.msg_type == NetworkMessage.TYPE_DB_INFO:
                if isinstance(message.data, dict):
                    filename = message.data.get('filename', 'unknown')
                    size = message.data.get('size', 0)
                    net_logger.info(f"[RECV] [BACKUP] Received DB_INFO: {filename}, size: {size} bytes")
                    print(f"[NETWORK] [BACKUP] Received DB_INFO: {filename}, size: {size} bytes")
            elif message.msg_type == NetworkMessage.TYPE_DB_CHUNK:
                if isinstance(message.data, dict):
                    chunk_num = message.data.get('chunk_number', '?')
                    total_chunks = message.data.get('total_chunks', '?')
                    net_logger.debug(f"[RECV] [BACKUP] Received chunk {chunk_num}/{total_chunks}")

            net_logger.info(f"[RECV] === END RECEIVE MESSAGE === Success")
            return message
        except UnicodeDecodeError as ude:
            net_logger.error(f"[RECV] Error decoding message: {ude}")
            print(f"[NETWORK] Error decoding message: {ude}")
            return None
        except json.JSONDecodeError as jde:
            net_logger.error(f"[RECV] Error parsing JSON: {jde}")
            net_logger.debug(f"[RECV] First 200 chars of data: {data[:200]}")
            print(f"[NETWORK] Error parsing JSON: {jde}")
            return None
    except socket.timeout as to:
        net_logger.error(f"[RECV] Socket timeout: {to}")
        print(f"[NETWORK] Socket timeout: {to}")
        return None
    except ConnectionError as ce:
        net_logger.error(f"[RECV] Connection error: {ce}")
        print(f"[NETWORK] Connection error: {ce}")
        return None
    except Exception as e:
        net_logger.error(f"[RECV] Unexpected error: {e}")
        net_logger.debug(f"[RECV] Error details: {traceback.format_exc()}")
        print(f"[NETWORK] Unexpected error: {e}")
        return None